import time
import json
from typing import Dict, Any, Optional, List
from flask import current_app
from redis import Redis


class ProgressTracker:
    """Track and report progress for long-running tasks."""

    def __init__(self, task_id: str) -> None:
        """
        Initialize a progress tracker for a specific task.

        Args:
            task_id: The Celery task ID to track
        """
        self.task_id = task_id
        # Use CELERY_BROKER_URL as fallback for Redis connection
        redis_url = current_app.config.get("REDIS_URL", current_app.config.get("CELERY_BROKER_URL"))
        self.redis = Redis.from_url(redis_url)
        self.start_time = time.time()

    def set_progress(
        self,
        progress: int,
        message: Optional[str] = None,
        status: str = "processing",
        current_file_index: Optional[int] = None,
    ) -> None:
        """
        Update progress percentage and optional message.

        Args:
            progress: Percentage of completion (0-100)
            message: Optional status message
            status: Status of the task (processing, completed, failed, canceled)
            current_file_index: Index of the current file being processed
        """
        data = {
            "progress": progress,
            "message": message,
            "status": status,
            "timestamp": time.time(),
            "estimated_time": self.estimate_time(progress) if progress > 0 else None,
            "current_file_index": current_file_index,
        }

        # Get existing file progress data if available
        file_progress = self.get_file_progress()
        if file_progress:
            data["file_progress"] = file_progress

        # Serialize to JSON before publishing
        self.redis.publish(f"progress_{self.task_id}", json.dumps(data))

        # Also store the latest progress in a key for clients that missed the message
        self.redis.set(f"task_progress:{self.task_id}", json.dumps(data))
        # Set expiration to 1 hour
        self.redis.expire(f"task_progress:{self.task_id}", 3600)

    def get_progress(self) -> Dict[str, Any]:
        """
        Get the latest progress information.

        Returns:
            Dict containing progress information or None if no progress is found
        """
        data = self.redis.get(f"task_progress:{self.task_id}")
        if data:
            return json.loads(data)
        return {"progress": 0, "message": "Task pending...", "timestamp": time.time(), "status": "pending"}

    def update_file_progress(self, file_index: int, file_info: Dict[str, Any]) -> None:
        """
        Update progress for a specific file in the batch.

        Args:
            file_index: Index of the file in the batch
            file_info: Dictionary with file progress information
        """
        # Get current file progress data
        file_progress_key = f"file_progress:{self.task_id}"
        file_progress_data = self.redis.get(file_progress_key)

        if file_progress_data:
            file_progress = json.loads(file_progress_data)
        else:
            file_progress = {}

        # Update the specific file's progress
        file_progress[str(file_index)] = file_info

        # Save updated file progress
        self.redis.set(file_progress_key, json.dumps(file_progress))
        self.redis.expire(file_progress_key, 3600)  # 1 hour expiration

        # Also update the main progress data to include file progress
        self._update_main_progress_with_file_data(file_progress)

    def get_file_progress(self) -> Dict[str, Any]:
        """
        Get progress information for all files in the batch.

        Returns:
            Dictionary mapping file indices to their progress information
        """
        file_progress_key = f"file_progress:{self.task_id}"
        file_progress_data = self.redis.get(file_progress_key)

        if file_progress_data:
            return json.loads(file_progress_data)
        return {}

    def _update_main_progress_with_file_data(self, file_progress: Dict[str, Any]) -> None:
        """
        Update the main progress data to include file progress.

        Args:
            file_progress: Dictionary with file progress information
        """
        progress_key = f"task_progress:{self.task_id}"
        progress_data = self.redis.get(progress_key)

        if progress_data:
            data = json.loads(progress_data)
            data["file_progress"] = file_progress
            self.redis.set(progress_key, json.dumps(data))

    def cancel_task(self) -> bool:
        """
        Mark a task as canceled.

        Returns:
            True if the task was successfully marked as canceled
        """
        cancel_key = f"task_cancel:{self.task_id}"
        self.redis.set(cancel_key, "1")
        self.redis.expire(cancel_key, 3600)  # 1 hour expiration

        # Also update the main progress status
        progress_data = self.get_progress()
        progress_data["status"] = "canceling"
        progress_data["message"] = "Canceling task..."
        self.redis.set(f"task_progress:{self.task_id}", json.dumps(progress_data))

        return True

    def is_canceled(self) -> bool:
        """
        Check if a task has been marked for cancellation.

        Returns:
            True if the task has been marked for cancellation
        """
        cancel_key = f"task_cancel:{self.task_id}"
        return bool(self.redis.get(cancel_key))

    def retry_failed_files(self, file_indices: List[int]) -> bool:
        """
        Mark specific files for retry.

        Args:
            file_indices: List of file indices to retry

        Returns:
            True if the files were successfully marked for retry
        """
        retry_key = f"task_retry:{self.task_id}"
        self.redis.set(retry_key, json.dumps(file_indices))
        self.redis.expire(retry_key, 3600)  # 1 hour expiration
        return True

    def get_retry_files(self) -> List[int]:
        """
        Get the list of file indices marked for retry.

        Returns:
            List of file indices to retry
        """
        retry_key = f"task_retry:{self.task_id}"
        retry_data = self.redis.get(retry_key)

        if retry_data:
            return json.loads(retry_data)
        return []

    def estimate_time(self, progress: int) -> str:
        """
        Calculate estimated remaining time.

        Args:
            progress: Current progress percentage (0-100)

        Returns:
            String representation of estimated time remaining
        """
        if progress <= 0:
            return "Estimating..."

        elapsed = time.time() - self.start_time
        if elapsed < 0.5:  # Avoid division by very small numbers
            return "Calculating..."

        try:
            total_estimated = elapsed / (progress / 100)
            remaining = total_estimated - elapsed

            if remaining < 60:
                return f"{int(remaining)}s remaining"
            elif remaining < 3600:
                minutes = int(remaining // 60)
                seconds = int(remaining % 60)
                return f"{minutes}m {seconds}s remaining"
            else:
                hours = int(remaining // 3600)
                minutes = int((remaining % 3600) // 60)
                return f"{hours}h {minutes}m remaining"
        except Exception:
            return "Calculating..."
