{% extends "base.html" %}

{% block title %}Batch File Converter - Convert Multiple Files at Once | File Processing Hub{% endblock %}
{% block meta %}
    <meta name="description" content="Convert multiple files at once between data science formats (CSV, JSON, Parquet, Excel) - transform datasets in batch with our free online conversion tool">
    <meta name="keywords" content="batch conversion, multiple file conversion, CSV to JSON, JSON to CSV, Excel to Parquet, data format converter, data science tools, dataset conversion, pandas file conversion">
    <meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}
{% block content %}
<style>
    /* Style the dropdown options */
    select option {
        background-color: #1F2937; /* gray-800 */
        color: #F3F4F6; /* gray-100 */
    }

    /* Style the dropdown when opened */
    select:focus option:checked {
        background-color: #047857; /* green-700 */
    }

    /* File preview container */
    #file-preview-container {
        max-height: 200px;
        overflow-y: auto;
        margin-top: 1rem;
    }

    /* File item in preview */
    .file-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem;
        border-bottom: 1px solid #374151;
    }

    .file-item:last-child {
        border-bottom: none;
    }

    /* Progress bar animation */
    @keyframes progress {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .animated-bg {
        background: linear-gradient(90deg, #047857, #10B981, #047857);
        background-size: 200% 100%;
        animation: progress 2s linear infinite;
    }
</style>

<div class="min-h-screen py-8 px-4">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">Batch File Converter</h1>
        <p class="text-gray-400 max-w-2xl mx-auto">
            Convert multiple files at once to your desired format. Perfect for data scientists and analysts working with large datasets.
        </p>

        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto">
            <div class="bg-gray-700 p-3 rounded-lg">
                <h3 class="text-green-400 text-lg font-semibold mb-2">Data Formats</h3>
                <ul class="text-gray-300 text-sm space-y-1">
                    <li>• CSV ↔ Excel, JSON, XML, Parquet</li>
                    <li>• Excel ↔ CSV, JSON, XML, Parquet</li>
                </ul>
            </div>
            <div class="bg-gray-700 p-3 rounded-lg">
                <h3 class="text-green-400 text-lg font-semibold mb-2">Document Formats</h3>
                <ul class="text-gray-300 text-sm space-y-1">
                    <li>• PDF ↔ Word (DOCX)</li>
                    <li>• PDF ↔ Excel (XLSX)</li>
                    <li>• Word ↔ PDF</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="w-full max-w-2xl mx-auto bg-gray-800 rounded-lg shadow-lg p-6 form-container">
        <div id="error-container" class="hidden"></div>

        <form id="batch-convert-form" method="POST" enctype="multipart/form-data">
            {{ form.csrf_token }}

            <!-- Enhanced Drag-and-Drop Area -->
            <div id="drop-area" class="drop-area convert-files" tabindex="0" role="button" aria-label="Drop files here or click to browse">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-green-300 mt-2">Drag & Drop your files here (max 20)</p>
                <p class="text-gray-400">or</p>
                {{ form.files(class="hidden", id="file-input") }}
                <button type="button" onclick="document.getElementById('file-input').click()"
                        class="mt-2 bg-green-600 px-4 py-2 rounded hover:bg-green-500 transition duration-300 text-white"
                        aria-label="Choose Files">
                    Choose Files
                </button>
                <div class="text-gray-400 text-xs mt-4">
                    Note: Maximum 20 files, 16MB per file, 100MB total
                </div>
                <div class="sr-only" aria-live="polite" id="file-upload-status">No files selected</div>
            </div>

            <!-- File Preview -->
            <div id="file-preview-container" class="bg-gray-700 rounded-lg p-2 mt-4 hidden">
                <div id="file-list" class="text-gray-300 text-sm"></div>
                <div id="total-size" class="text-right text-gray-400 text-xs mt-2 pr-2"></div>
            </div>

            <!-- Presets -->
            <div class="mt-4" id="presets-container">
                <!-- Will be populated by JavaScript -->
            </div>

            <!-- Conversion Options -->
            <div class="mt-4">
                <div>
                    <label for="output_format" class="block text-sm font-medium text-gray-300">Output Format</label>
                    {{ form.output_format(class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-green-600 focus:border-transparent",
                                  id="output_format") }}
                    <p class="mt-1 text-xs text-gray-400">Select target format for all files</p>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="submit-button"
                    class="w-full px-4 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                    disabled>
                Convert Files
            </button>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script src="{{ url_for('static', filename='js/presets.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const filePreviewContainer = document.getElementById('file-preview-container');
    const fileList = document.getElementById('file-list');
    const totalSizeElement = document.getElementById('total-size');
    const submitButton = document.getElementById('submit-button');

    let files = [];
    const MAX_FILES = 20;
    const MAX_FILE_SIZE = 16 * 1024 * 1024; // 16MB
    const MAX_TOTAL_SIZE = 100 * 1024 * 1024; // 100MB

    // Helper function to format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Update file preview
    function updateFilePreview() {
        if (files.length === 0) {
            filePreviewContainer.classList.add('hidden');
            submitButton.disabled = true;
            return;
        }

        filePreviewContainer.classList.remove('hidden');
        fileList.innerHTML = '';

        let totalSize = 0;

        files.forEach((file, index) => {
            totalSize += file.size;

            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-name">${file.name}</div>
                <div class="file-size">${formatFileSize(file.size)}</div>
                <button type="button" class="text-red-400 hover:text-red-300" data-index="${index}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            `;

            fileList.appendChild(fileItem);

            // Add event listener to remove button
            const removeButton = fileItem.querySelector('button');
            removeButton.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                files.splice(index, 1);
                updateFilePreview();
            });
        });

        totalSizeElement.textContent = `Total: ${formatFileSize(totalSize)}`;

        // Enable/disable submit button based on validation
        submitButton.disabled = files.length === 0 || totalSize > MAX_TOTAL_SIZE;

        if (totalSize > MAX_TOTAL_SIZE) {
            totalSizeElement.classList.add('text-red-400');
            totalSizeElement.textContent += ' (exceeds 100MB limit)';
        } else {
            totalSizeElement.classList.remove('text-red-400');
        }

        // Update screen reader status
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            if (files.length === 0) {
                statusElement.textContent = "No files selected";
            } else {
                statusElement.textContent = `${files.length} file${files.length > 1 ? 's' : ''} selected, total size: ${formatFileSize(totalSize)}`;
                if (totalSize > MAX_TOTAL_SIZE) {
                    statusElement.textContent += ' (exceeds 100MB limit)';
                }
            }
        }
    }

    // Handle file selection
    fileInput.addEventListener('change', function() {
        const newFiles = Array.from(this.files);

        if (files.length + newFiles.length > MAX_FILES) {
            alert(`You can only upload a maximum of ${MAX_FILES} files at once.`);
            return;
        }

        // Validate file sizes
        const oversizedFiles = newFiles.filter(file => file.size > MAX_FILE_SIZE);
        if (oversizedFiles.length > 0) {
            alert(`The following files exceed the 16MB size limit:\n${oversizedFiles.map(f => f.name).join('\n')}`);
            return;
        }

        files = [...files, ...newFiles];
        updateFilePreview();
    });

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // Highlight drop area when dragging over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => highlightDropArea(true), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => highlightDropArea(false), false);
    });

    // Make drop area clickable
    dropArea.addEventListener('click', () => fileInput.click());

    // Add keyboard support for drop area
    dropArea.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            fileInput.click();
        }
    });

    function highlightDropArea(highlight) {
        dropArea.classList.toggle('drag-active', highlight);

        // Update screen reader status
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = highlight ?
                "Files are being dragged over the drop area" :
                files.length > 0 ? `${files.length} file${files.length > 1 ? 's' : ''} selected` : "No files selected";
        }
    }

    // Handle dropped files
    dropArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const newFiles = Array.from(dt.files);

        if (files.length + newFiles.length > MAX_FILES) {
            alert(`You can only upload a maximum of ${MAX_FILES} files at once.`);
            return;
        }

        // Validate file sizes
        const oversizedFiles = newFiles.filter(file => file.size > MAX_FILE_SIZE);
        if (oversizedFiles.length > 0) {
            alert(`The following files exceed the 16MB size limit:\n${oversizedFiles.map(f => f.name).join('\n')}`);
            return;
        }

        files = [...files, ...newFiles];
        updateFilePreview();
    }

    // Initialize presets manager
    const presetsManager = new PresetsManager({
        presetType: 'batch',
        containerSelector: '#presets-container',
        onPresetSelected: (preset) => {
            console.log('Preset selected:', preset);
        },
        getFormValues: () => {
            return {
                output_format: document.getElementById('output_format').value
            };
        },
        setFormValues: (settings) => {
            if (settings.output_format) {
                document.getElementById('output_format').value = settings.output_format;
            }
        }
    });

    // Add class to body if user is authenticated
    {% if current_user.is_authenticated %}
    document.body.classList.add('user-authenticated');
    {% endif %}
});
</script>
{% endblock %}
