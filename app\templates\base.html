<!DOCTYPE html>
<html lang="en" data-theme="dark">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <meta name="description" content="Free online tools for file conversion, PDF splitting and merging, image conversion, and text extraction.">
  <title>{% block title %}Code Scrapyard{% endblock %}</title>

  <!-- Resource Hints -->
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
  <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
  <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

  <!-- Preload critical assets -->
  <link rel="preload" href="{{ versioned_url_for('static', filename='css/theme.css') }}" as="style">
  <link rel="preload" href="{{ versioned_url_for('static', filename='js/mobile-menu.js') }}" as="script">
  <link rel="preload" href="{{ versioned_url_for('static', filename='filecipher-01.png') }}" as="image" type="image/png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="{{ versioned_url_for('static', filename='tcl-logo.png') }}">

  <!-- Critical CSS -->
  <!-- Tailwind CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
  <!-- FontAwesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

  {% set critical_css = get_critical_css() %}
  {% if critical_css.endswith('.min.css') %}
    <!-- Bundled Critical CSS -->
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename=critical_css) }}">
  {% else %}
    <!-- Individual Critical CSS -->
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/theme.css') }}">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/responsive.css') }}">
  {% endif %}

  {% set non_critical_css = get_non_critical_css() %}
  {% if non_critical_css %}
    <!-- Bundled Non-critical CSS loaded asynchronously -->
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename=non_critical_css) }}" media="print" onload="this.media='all'">
  {% else %}
    <!-- Individual Non-critical CSS loaded asynchronously -->
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/animations.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/fun-zone.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/drag-drop.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/guided-tour.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/contextual-help.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/progress-indicators.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/result-pages.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/file-preview.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/presets.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ versioned_url_for('static', filename='css/qa-fixes.css') }}" media="print" onload="this.media='all'">
  {% endif %}

  <!-- Fallback for browsers that don't support onload for stylesheets -->
  <noscript>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fun-zone.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/drag-drop.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/guided-tour.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/contextual-help.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/progress-indicators.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/result-pages.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/file-preview.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/presets.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/qa-fixes.css') }}">
  </noscript>

  <!-- Custom styles for header alignment and micro-interactions -->
  <style>
    /* Center-right alignment for desktop navigation tools */
    @media (min-width: 768px) {
      .tools-container {
        display: flex;
        justify-content: flex-end;
        margin-right: 2rem;
      }

      /* Add spacing between tool categories */
      .tools-container > div,
      .tools-container > a {
        margin-left: 0.5rem;
      }

      /* Ensure dropdown menus appear below their buttons */
      .tools-container .relative.group {
        position: relative;
      }

      /* Improve dropdown positioning */
      .tools-container .absolute {
        left: 50%;
        transform: translateX(-50%);
        min-width: 12rem;
      }
    }

    /* Enhanced dropdown animations */
    .dropdown-menu {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
      transition:
        opacity 0.2s var(--ease-out),
        transform 0.2s var(--ease-out);
      transform-origin: top center;
      pointer-events: none;
    }

    /* Fix dropdown visibility for both hover and click with animations */
    .group:hover .dropdown-menu {
      display: block !important;
      opacity: 1;
      transform: translateY(0) scale(1);
      pointer-events: auto;
    }

    /* Ensure dropdown stays visible when focused with animations */
    .group:focus-within .dropdown-menu {
      display: block !important;
      opacity: 1;
      transform: translateY(0) scale(1);
      pointer-events: auto;
    }

    /* For JavaScript toggle with animations */
    .dropdown-menu.block {
      display: block !important;
      opacity: 1;
      transform: translateY(0) scale(1);
      pointer-events: auto;
    }

    /* Add z-index to ensure dropdowns appear above other elements */
    .absolute {
      z-index: 50;
    }

    /* Menu item hover effects */
    .menu-item {
      position: relative;
      overflow: hidden;
    }

    .menu-item::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background-color: var(--text-accent);
      transition: width 0.3s var(--ease-out), left 0.3s var(--ease-out);
    }

    .menu-item:hover::after,
    .menu-item:focus::after,
    .active-page::after {
      width: 100%;
      left: 0;
    }

    /* Dropdown menu item hover effects */
    .dropdown-menu a {
      position: relative;
      transition:
        background-color 0.2s var(--ease-out),
        color 0.2s var(--ease-out),
        padding-left 0.2s var(--ease-out);
    }

    .dropdown-menu a:hover {
      padding-left: 1.5rem;
    }

    /* Icon animations in menu items */
    .menu-item i,
    .dropdown-menu a i {
      transition: transform 0.2s var(--ease-out);
    }

    .menu-item:hover i:not(.fa-chevron-down),
    .dropdown-menu a:hover i {
      transform: scale(1.2);
    }

    /* Chevron rotation animation */
    .fa-chevron-down {
      transition: transform 0.3s var(--ease-out);
    }

    .group:hover .fa-chevron-down,
    .group:focus-within .fa-chevron-down {
      transform: rotate(180deg);
    }
  </style>
</head>
<body class="flex flex-col min-h-screen">
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-to-content">Skip to content</a>

    <nav class="fixed top-0 left-0 w-full z-50 flex items-center justify-between py-3 px-6 shadow-md" role="navigation" aria-label="Main navigation">
      <!-- Logo and Brand -->
      <div class="flex items-center">
        <a href="{{ url_for('main.index') }}" aria-label="Home page" class="flex items-center" data-tooltip="Return to the homepage">
          <!-- Critical logo image loaded normally -->
          <img src="{{ url_for('static', filename='filecipher-01.png') }}" alt="ImgCipher Logo" class="h-10 mr-2" width="40" height="40">
          <span class="text-white font-semibold text-lg hidden sm:inline-block">ImgCipher</span>
        </a>
      </div>

      <!-- Main Navigation - Desktop -->
      <div class="hidden md:flex items-center justify-center flex-grow tools-container" role="menubar" aria-label="Main menu">


        <!-- PDF Tools Dropdown -->
        <div class="relative group" role="menuitem" aria-haspopup="true">
          <button class="menu-item text-gray-300 font-semibold px-3 py-2 rounded hover:bg-gray-700 hover:text-white flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  aria-expanded="false" id="pdf-tools-button" data-tooltip="PDF manipulation tools" data-tooltip-position="bottom">
            <i class="fas fa-file-pdf mr-1 text-red-500" aria-hidden="true"></i>PDF
            <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-200 group-hover:rotate-180" aria-hidden="true"></i>
          </button>

          <!-- PDF Dropdown Menu -->
          <div class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 hidden dropdown-menu"
               role="menu" aria-orientation="vertical" aria-labelledby="pdf-tools-button">
            <div class="py-1" role="none">
              <a href="{{ url_for('tools.split_pdf_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.split_pdf_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-cut mr-2 text-blue-500" aria-hidden="true"></i>Split PDF
              </a>
              <a href="{{ url_for('tools.merge_pdf_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.merge_pdf_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-object-group mr-2 text-red-500" aria-hidden="true"></i>Merge PDF
              </a>
            </div>
          </div>
        </div>

        <!-- DOCX Tools Dropdown -->
        <div class="relative group" role="menuitem" aria-haspopup="true">
          <button class="menu-item text-gray-300 font-semibold px-3 py-2 rounded hover:bg-gray-700 hover:text-white flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  aria-expanded="false" id="docx-tools-button" data-tooltip="Word document tools" data-tooltip-position="bottom">
            <i class="fas fa-file-word mr-1 text-blue-500" aria-hidden="true"></i>DOCX
            <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-200 group-hover:rotate-180" aria-hidden="true"></i>
          </button>

          <!-- DOCX Dropdown Menu -->
          <div class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 hidden dropdown-menu"
               role="menu" aria-orientation="vertical" aria-labelledby="docx-tools-button">
            <div class="py-1" role="none">
              <a href="{{ url_for('tools.convert_file_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.convert_file_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-file-export mr-2 text-blue-500" aria-hidden="true"></i>Convert to PDF
              </a>
            </div>
          </div>
        </div>

        <!-- Data Tools Dropdown -->
        <div class="relative group" role="menuitem" aria-haspopup="true">
          <button class="menu-item text-gray-300 font-semibold px-3 py-2 rounded hover:bg-gray-700 hover:text-white flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  aria-expanded="false" id="data-tools-button" data-tooltip="Data conversion tools" data-tooltip-position="bottom">
            <i class="fas fa-database mr-1 text-green-500" aria-hidden="true"></i>Data
            <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-200 group-hover:rotate-180" aria-hidden="true"></i>
          </button>

          <!-- Data Dropdown Menu -->
          <div class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 hidden dropdown-menu"
               role="menu" aria-orientation="vertical" aria-labelledby="data-tools-button">
            <div class="py-1" role="none">
              <a href="{{ url_for('tools.convert_file_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.convert_file_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-file-export mr-2 text-green-500" aria-hidden="true"></i>Convert Files
              </a>
              {% if current_user.is_authenticated %}
              <a href="{{ url_for('tools.batch_convert_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.batch_convert_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-layer-group mr-2 text-indigo-500" aria-hidden="true"></i>Batch Convert
              </a>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Images Tools Dropdown -->
        <div class="relative group" role="menuitem" aria-haspopup="true">
          <button class="menu-item text-gray-300 font-semibold px-3 py-2 rounded hover:bg-gray-700 hover:text-white flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  aria-expanded="false" id="images-tools-button" data-tooltip="Image manipulation tools" data-tooltip-position="bottom">
            <i class="fas fa-images mr-1 text-yellow-500" aria-hidden="true"></i>Images
            <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-200 group-hover:rotate-180" aria-hidden="true"></i>
          </button>

          <!-- Images Dropdown Menu -->
          <div class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 hidden dropdown-menu"
               role="menu" aria-orientation="vertical" aria-labelledby="images-tools-button">
            <div class="py-1" role="none">
              <a href="{{ url_for('tools.image_converter_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.image_converter_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-exchange-alt mr-2 text-yellow-500" aria-hidden="true"></i>Convert Image
              </a>
              <a href="{{ url_for('tools.remove_background_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.remove_background_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-eraser mr-2 text-purple-500" aria-hidden="true"></i>Remove Background
              </a>
            </div>
          </div>
        </div>

        <!-- Text Tools Dropdown -->
        <div class="relative group" role="menuitem" aria-haspopup="true">
          <button class="menu-item text-gray-300 font-semibold px-3 py-2 rounded hover:bg-gray-700 hover:text-white flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  aria-expanded="false" id="text-tools-button" data-tooltip="Text extraction tools" data-tooltip-position="bottom">
            <i class="fas fa-font mr-1 text-purple-500" aria-hidden="true"></i>Text
            <i class="fas fa-chevron-down ml-1 text-xs transition-transform duration-200 group-hover:rotate-180" aria-hidden="true"></i>
          </button>

          <!-- Text Dropdown Menu -->
          <div class="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 hidden dropdown-menu"
               role="menu" aria-orientation="vertical" aria-labelledby="text-tools-button">
            <div class="py-1" role="none">
              <a href="{{ url_for('tools.extract_text_page') }}" role="menuitem"
                 class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white flex items-center {% if request.path == url_for('tools.extract_text_page') %}bg-gray-700 text-white{% endif %}">
                <i class="fas fa-font mr-2 text-purple-500" aria-hidden="true"></i>Extract Text
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- User Menu - Desktop -->
      <div class="hidden md:flex items-center" role="navigation" aria-label="User menu">
        {% if current_user.is_authenticated %}
          <!-- User Avatar and Dropdown -->
          <div class="relative group">
            <button class="flex items-center text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-full"
                    aria-expanded="false" id="user-menu-button">
              <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold mr-2">
                {{ current_user.username[0]|upper }}
              </div>
              <span class="mr-1">{{ current_user.username }}</span>
              <i class="fas fa-chevron-down text-xs transition-transform duration-200 group-hover:rotate-180" aria-hidden="true"></i>
            </button>

            <!-- User Dropdown Menu -->
            <div class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 hidden dropdown-menu"
                 role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button">
              <div class="py-1" role="none">
                <a href="{{ url_for('auth.profile') }}" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" role="menuitem">
                  <i class="fas fa-user mr-2" aria-hidden="true"></i>Profile
                </a>
                <a href="{{ url_for('history.history_page') }}" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" role="menuitem">
                  <i class="fas fa-history mr-2" aria-hidden="true"></i>Conversion History
                </a>
                <a href="{{ url_for('auth.logout') }}" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white" role="menuitem">
                  <i class="fas fa-sign-out-alt mr-2" aria-hidden="true"></i>Logout
                </a>
              </div>
            </div>
          </div>
        {% else %}
          <!-- Login/Register Buttons -->
          <div class="flex items-center space-x-2">
            <a href="{{ url_for('auth.login') }}" class="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
              <i class="fas fa-sign-in-alt mr-1" aria-hidden="true"></i>Login
            </a>
            <a href="{{ url_for('auth.register') }}" class="px-4 py-2 rounded-md border border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
              Register
            </a>
          </div>
        {% endif %}
      </div>



      <!-- Mobile Menu Button -->
      <button id="mobile-menu-button" class="md:hidden flex items-center justify-center w-10 h-10 rounded-md bg-gray-700 text-white hover:bg-gray-600 transition-colors"
              aria-expanded="false" aria-controls="mobile-menu" aria-label="Toggle mobile menu">
        <i class="fas fa-bars" aria-hidden="true"></i>
      </button>
    </nav>

    <!-- Mobile Menu - Simplified -->
    <div id="mobile-menu" class="fixed top-0 right-0 h-full w-64 bg-gray-800 shadow-lg transform translate-x-full mobile-menu p-6 flex flex-col space-y-4 z-60" role="dialog" aria-modal="true" aria-label="Mobile menu">
      <button id="close-mobile-menu" class="text-gray-300 self-end mb-4" aria-label="Close menu">✕</button>

      <!-- Main Navigation Links -->
      <div role="navigation" aria-label="Mobile navigation">


        <!-- Tools Section -->
        <div class="mt-4">
          <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2 px-4">Tools</h3>

          <!-- PDF Tools -->
          <div class="mb-2">
            <h4 class="text-xs font-semibold text-gray-400 px-4 py-1 flex items-center">
              <i class="fas fa-file-pdf mr-2 text-red-500" aria-hidden="true"></i>PDF
            </h4>
            <a href="{{ url_for('tools.split_pdf_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-blue-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.split_pdf_page') %}active-page bg-blue-600{% endif %}"
               aria-current="{% if request.path == url_for('tools.split_pdf_page') %}page{% else %}false{% endif %}">
              <i class="fas fa-cut mr-2" aria-hidden="true"></i>Split PDF
            </a>
            <a href="{{ url_for('tools.merge_pdf_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-red-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.merge_pdf_page') %}active-page bg-red-600{% endif %}"
               aria-current="{% if request.path == url_for('tools.merge_pdf_page') %}page{% else %}false{% endif %}">
              <i class="fas fa-object-group mr-2" aria-hidden="true"></i>Merge PDF
            </a>
          </div>

          <!-- DOCX Tools -->
          <div class="mb-2">
            <h4 class="text-xs font-semibold text-gray-400 px-4 py-1 flex items-center">
              <i class="fas fa-file-word mr-2 text-blue-500" aria-hidden="true"></i>DOCX
            </h4>
            <a href="{{ url_for('tools.convert_file_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-blue-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.convert_file_page') %}bg-blue-600 text-white{% endif %}">
              <i class="fas fa-file-export mr-2" aria-hidden="true"></i>Convert to PDF
            </a>
          </div>

          <!-- Data Tools -->
          <div class="mb-2">
            <h4 class="text-xs font-semibold text-gray-400 px-4 py-1 flex items-center">
              <i class="fas fa-database mr-2 text-green-500" aria-hidden="true"></i>Data
            </h4>
            <a href="{{ url_for('tools.convert_file_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-green-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.convert_file_page') %}active-page bg-green-600{% endif %}"
               aria-current="{% if request.path == url_for('tools.convert_file_page') %}page{% else %}false{% endif %}">
              <i class="fas fa-file-export mr-2" aria-hidden="true"></i>Convert Files
            </a>
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('tools.batch_convert_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-indigo-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.batch_convert_page') %}active-page bg-indigo-600{% endif %}"
               aria-current="{% if request.path == url_for('tools.batch_convert_page') %}page{% else %}false{% endif %}">
              <i class="fas fa-layer-group mr-2" aria-hidden="true"></i>Batch Convert
            </a>
            {% endif %}
          </div>

          <!-- Images Tools -->
          <div class="mb-2">
            <h4 class="text-xs font-semibold text-gray-400 px-4 py-1 flex items-center">
              <i class="fas fa-images mr-2 text-yellow-500" aria-hidden="true"></i>Images
            </h4>
            <a href="{{ url_for('tools.image_converter_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-yellow-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.image_converter_page') %}active-page bg-yellow-600{% endif %}"
               aria-current="{% if request.path == url_for('tools.image_converter_page') %}page{% else %}false{% endif %}">
               <i class="fas fa-exchange-alt mr-2" aria-hidden="true"></i>Convert Image
            </a>
            <a href="{{ url_for('tools.remove_background_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-purple-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.remove_background_page') %}active-page bg-purple-600{% endif %}"
               aria-current="{% if request.path == url_for('tools.remove_background_page') %}page{% else %}false{% endif %}">
               <i class="fas fa-eraser mr-2" aria-hidden="true"></i>Remove Background
            </a>
          </div>

          <!-- Text Tools -->
          <div class="mb-2">
            <h4 class="text-xs font-semibold text-gray-400 px-4 py-1 flex items-center">
              <i class="fas fa-font mr-2 text-purple-500" aria-hidden="true"></i>Text
            </h4>
            <a href="{{ url_for('tools.extract_text_page') }}"
               class="text-gray-300 font-semibold px-6 py-1 rounded hover:bg-purple-600 hover:text-white transition-colors block mb-1 {% if request.path == url_for('tools.extract_text_page') %}active-page bg-purple-600{% endif %}"
               aria-current="{% if request.path == url_for('tools.extract_text_page') %}page{% else %}false{% endif %}">
              <i class="fas fa-font mr-2" aria-hidden="true"></i>Extract Text
            </a>
          </div>
        </div>
      </div>



      <!-- User Account Section -->
      <div class="border-t border-gray-700 my-2 pt-2" role="navigation" aria-label="User account">
        {% if current_user.is_authenticated %}
          <a href="{{ url_for('auth.profile') }}"
             class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-gray-700 hover:text-white transition-colors block mb-1">
            <i class="fas fa-user mr-2" aria-hidden="true"></i>Profile
          </a>
          <a href="{{ url_for('history.history_page') }}"
             class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-gray-700 hover:text-white transition-colors block mb-1">
            <i class="fas fa-history mr-2" aria-hidden="true"></i>Conversion History
          </a>
          <a href="{{ url_for('auth.logout') }}"
             class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-gray-700 hover:text-white transition-colors block">
            <i class="fas fa-sign-out-alt mr-2" aria-hidden="true"></i>Logout
          </a>
        {% else %}
          <a href="{{ url_for('auth.login') }}"
             class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-gray-700 hover:text-white transition-colors block mb-1">
            <i class="fas fa-sign-in-alt mr-2" aria-hidden="true"></i>Login
          </a>
          <a href="{{ url_for('auth.register') }}"
             class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-gray-700 hover:text-white transition-colors block">
            <i class="fas fa-user-plus mr-2" aria-hidden="true"></i>Register
          </a>
        {% endif %}
      </div>
    </div>

    <div class="container mx-auto px-4 pt-20 pb-8">
      <!-- Flash Messages -->
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          <div class="flash-messages mb-4" role="alert" aria-live="polite">
            {% for category, message in messages %}
              <div class="p-4 mb-2 rounded-md {% if category == 'error' %}bg-red-100 text-red-700{% elif category == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                {{ message }}
              </div>
            {% endfor %}
          </div>
        {% endif %}
      {% endwith %}

      <main id="main-content" class="flex-grow" tabindex="-1">
        {% block content %}{% endblock %}
      </main>
    </div>

    <footer class="mt-auto py-6 px-6 flex flex-col md:flex-row justify-between items-center text-sm shadow-md" role="contentinfo">
      <p class="text-gray-400 mb-4 md:mb-0">
          Powered by: <a href="https://www.tomscyberlab.net" target="_blank" rel="noopener" class="text-blue-500 hover:text-blue-400 transition-colors">tomscyberlab</a>
      </p>
      <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-6">
          <a href="/terms" class="text-blue-500 hover:text-blue-400 transition-colors">Terms & Conditions</a>
          <a href="/privacy" class="text-blue-500 hover:text-blue-400 transition-colors">Privacy Policy</a>
          <a href="{{ url_for('main.browser_compatibility') }}" class="text-blue-500 hover:text-blue-400 transition-colors">Browser Compatibility</a>
      </div>
    </footer>



    <!-- Main Help Panel -->
    <div id="main-help-panel" class="contextual-help-panel" tabindex="-1">
      <div class="contextual-help-panel-header">
        <h2 class="contextual-help-panel-title">ImgCipher Help</h2>
        <button class="contextual-help-panel-close" aria-label="Close help panel" data-help-toggle="main-help-panel">
          <i class="fas fa-times" aria-hidden="true"></i>
        </button>
      </div>
      <div class="contextual-help-panel-content">
        <p>Welcome to ImgCipher! Here's how to get started:</p>

        <h3>Navigation</h3>
        <p>Use the navigation menu at the top to access different tools:</p>
        <ul>
          <li><strong>PDF</strong> - Split and merge PDF files</li>
          <li><strong>DOCX</strong> - Convert Word documents</li>
          <li><strong>Data</strong> - Convert between data formats</li>
          <li><strong>Images</strong> - Convert images and remove backgrounds</li>
          <li><strong>Text</strong> - Extract text from images and documents</li>
        </ul>

        <h3>File Upload</h3>
        <p>You can upload files in two ways:</p>
        <ul>
          <li>Click the upload area to select files from your computer</li>
          <li>Drag and drop files directly onto the upload area</li>
        </ul>

        <h3>Need More Help?</h3>
        <p>Look for <span class="help-toggle-button inline-flex items-center justify-center w-4 h-4 text-xs">?</span> icons throughout the application for contextual help on specific features.</p>

        <p>The guided tour will automatically start on your first visit to each page to help you get familiar with the features.</p>
      </div>
    </div>

  <!-- Critical JavaScript -->
  {% if get_critical_css().endswith('.min.css') %}
    <!-- Bundled Critical JavaScript -->
    <script src="{{ versioned_url_for('static', filename='dist/common.min.js') }}"></script>
  {% else %}
    <!-- Individual Critical JavaScript -->
    <script src="{{ versioned_url_for('static', filename='js/mobile-menu.js') }}"></script>
    <script src="{{ versioned_url_for('static', filename='js/lazy-load.js') }}"></script>
    <script src="{{ versioned_url_for('static', filename='js/module-loader.js') }}"></script>
  {% endif %}

  <!-- Deferred JavaScript -->

  <!-- Lazy-loaded JavaScript -->
  <script>
    // Function to dynamically load scripts
    function loadScript(src, callback) {
      const script = document.createElement('script');
      script.src = src;
      script.onload = callback || function() {};
      document.head.appendChild(script);
    }

    // Load non-critical scripts after page load
    window.addEventListener('load', function() {
      // Load scripts with a small delay to prioritize initial rendering
      setTimeout(function() {
        // Load page-specific modules
        {% set page_modules = get_current_page_modules() %}
        {% if page_modules %}
          console.log('Loading page-specific modules: {{ page_modules|join(", ") }}');
          {% for module in page_modules %}
            if (!window.ModuleLoader.isLoaded('{{ module }}')) {
              window.ModuleLoader.load('{{ module }}');
            }
          {% endfor %}
        {% endif %}

        // Load common modules
        loadScript("{{ versioned_url_for('static', filename='js/guided-tour.js') }}");
        loadScript("{{ versioned_url_for('static', filename='js/contextual-help.js') }}");
        loadScript("{{ versioned_url_for('static', filename='js/progress-indicators.js') }}");
        loadScript("{{ versioned_url_for('static', filename='js/result-pages.js') }}");
        loadScript("{{ versioned_url_for('static', filename='js/file-preview.js') }}");
      }, 100);
    });

    // Function to load scripts on demand
    window.loadOnDemand = function(scriptName, callback) {
      loadScript("{{ url_for('static', filename='js/') }}" + scriptName, callback);
    };
  </script>

  <!-- Dropdown Menu JavaScript -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get all dropdown buttons
      const dropdownButtons = document.querySelectorAll('.relative.group > button');

      // Add click event listeners to toggle dropdown visibility
      dropdownButtons.forEach(button => {
        button.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();

          // Toggle aria-expanded attribute
          const isExpanded = this.getAttribute('aria-expanded') === 'true';
          this.setAttribute('aria-expanded', !isExpanded);

          // Find the dropdown menu
          const dropdown = this.nextElementSibling;

          // Toggle the 'block' class manually
          if (isExpanded) {
            dropdown.classList.remove('block');
          } else {
            dropdown.classList.add('block');
          }
        });
      });

      // Close dropdowns when clicking outside
      document.addEventListener('click', function(e) {
        dropdownButtons.forEach(button => {
          if (!button.contains(e.target)) {
            button.setAttribute('aria-expanded', 'false');
            const dropdown = button.nextElementSibling;
            dropdown.classList.remove('block');
          }
        });
      });
    });
  </script>
</body>
</html>