import os
import tempfile
import shutil
import pytest
from app import create_app
from app.extensions import db
from app.config import Config
from app.models.auth import User, Role


class TestConfig(Config):
    """Test configuration."""

    TESTING = True
    SQLALCHEMY_DATABASE_URI = "sqlite:///:memory:"
    WTF_CSRF_ENABLED = False
    UPLOAD_FOLDER = tempfile.mkdtemp()
    OUTPUT_FOLDER = tempfile.mkdtemp()
    SERVER_NAME = "localhost.localdomain"
    CELERY_BROKER_URL = "memory://"
    CELERY_RESULT_BACKEND = "memory://"


@pytest.fixture
def app():
    """Create and configure a Flask app for testing."""
    app, _ = create_app(TestConfig)

    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()

    # Clean up temporary directories
    if os.path.exists(TestConfig.UPLOAD_FOLDER):
        shutil.rmtree(TestConfig.UPLOAD_FOLDER, ignore_errors=True)
    if os.path.exists(TestConfig.OUTPUT_FOLDER):
        shutil.rmtree(TestConfig.OUTPUT_FOLDER, ignore_errors=True)


@pytest.fixture
def client(app):
    """A test client for the app."""
    return app.test_client()


@pytest.fixture
def runner(app):
    """A test CLI runner for the app."""
    return app.test_cli_runner()


class AuthActions:
    """Helper class for authentication actions in tests."""

    def __init__(self, client):
        self._client = client

    def register(self, username="testuser", email="<EMAIL>", password="password"):
        """Register a new user."""
        return self._client.post(
            "/auth/register", data={"username": username, "email": email, "password": password, "confirm_password": password}
        )

    def login(self, email="<EMAIL>", password="password"):
        """Log in as the test user."""
        # First register a user if they don't exist
        with db.session.begin():
            if not User.query.filter_by(email=email).first():
                user = User(username="testuser", email=email)
                user.password = password  # Use the password property setter

                # Create roles if they don't exist
                registered_role = Role.query.filter_by(name="registered").first()
                if not registered_role:
                    registered_role = Role(name="registered", description="Registered user")
                    db.session.add(registered_role)

                admin_role = Role.query.filter_by(name="admin").first()
                if not admin_role:
                    admin_role = Role(name="admin", description="Administrator")
                    db.session.add(admin_role)

                # Add registered role to user
                user.roles.append(registered_role)
                db.session.add(user)

        return self._client.post("/auth/login", data={"username": "testuser", "password": password})

    def logout(self):
        """Log out the current user."""
        return self._client.get("/auth/logout")


@pytest.fixture
def auth(client):
    """Authentication fixture."""
    return AuthActions(client)
