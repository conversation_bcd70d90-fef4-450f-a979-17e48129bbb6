{% extends "base.html" %}

{% block title %}Extract Text from Images - OCR Tool{% endblock %}

{% block content %}
<style>
    /* Style the dropdown options */
    select option {
        background-color: #1F2937; /* gray-800 */
        color: #F3F4F6; /* gray-100 */
    }

    /* Style the dropdown when opened */
    select:focus option:checked {
        background-color: #7E22CE; /* purple-700 */
    }

    /* Style the dropdown hover state */
    select option:hover {
        background-color: #6B21A8; /* purple-800 */
    }

    /* Style for the extracted text container */
    .extracted-text {
        max-height: 400px;
        overflow-y: auto;
        white-space: pre-wrap;
        font-family: monospace;
        line-height: 1.5;
    }

    /* Loading animation */
    .loading-spinner {
        display: inline-block;
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255,255,255,.3);
        border-radius: 50%;
        border-top-color: #9333EA; /* purple-600 */
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Image preview */
    .image-preview {
        max-width: 100%;
        max-height: 200px;
        border-radius: 4px;
        margin-top: 8px;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(17, 24, 39, 0.8); /* bg-gray-900 with opacity */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    /* Ensure loading overlay is hidden by default */
    .loading-overlay.hidden {
        display: none !important;
    }

    .loading-text {
        margin-top: 1rem;
        color: white;
        font-size: 1.25rem;
        font-weight: 500;
    }
</style>

<div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center px-4 py-8">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <p class="loading-text">Processing your image...</p>
        <p class="text-gray-300 text-sm mt-2">This may take a moment for large images</p>
    </div>

    <div class="text-center max-w-xl mb-8 relative">
        <h1 class="text-3xl md:text-4xl font-bold text-white mb-4 inline-block relative">
            Extract Text from Images
        </h1>
        <p class="text-gray-400">Upload an image and extract text using Optical Character Recognition (OCR).</p>
    </div>

    <!-- Upload Form -->
    <div class="w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 form-container">
        <div id="error-container" class="hidden"></div>

        <form id="extract-text-form" method="POST" enctype="multipart/form-data">
            {{ form.hidden_tag() }}

            <!-- Enhanced Drag-and-Drop Area -->
            <div id="drop-area" class="drop-area extract-text" tabindex="0" role="button" aria-label="Drop image file here or click to browse">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p class="text-purple-300 mt-2">Drag & Drop your image here</p>
                <p class="text-gray-400">or</p>
                {{ form.image_file(class="hidden", id="file-input", accept=".jpg,.jpeg,.png,.gif,.webp,.tiff,.bmp") }}
                <button type="button" onclick="document.getElementById('file-input').click()"
                        class="mt-2 bg-purple-600 px-4 py-2 rounded hover:bg-purple-500 transition duration-300 text-white"
                        aria-label="Choose file">
                    Choose Image
                </button>
                <div class="sr-only" aria-live="polite" id="file-upload-status">No image selected</div>
            </div>

            <!-- File Info -->
            <div id="file-details" class="hidden text-gray-400 text-sm mt-4">
                <div class="flex items-center bg-gray-700 px-3 py-2 rounded-lg mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <div class="flex-1 min-w-0">
                        <p id="file-name" class="text-gray-300 truncate"></p>
                        <p id="file-size" class="text-gray-500 text-xs"></p>
                    </div>
                    <button id="remove-file" class="text-red-400 hover:text-red-300 ml-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div id="image-preview-container" class="mt-2 text-center hidden">
                    <img id="image-preview" class="image-preview" alt="Image preview" />
                </div>
                <div id="size-warning" class="hidden text-yellow-400 mt-1">
                    Note: Large images may take longer to process
                </div>
            </div>
            <div id="no-file-selected" class="text-gray-400 text-sm mt-4">No image selected.</div>

            <!-- File Preview Display (initially hidden) -->
            <div id="file-preview-display" class="hidden relative mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700"></div>

            <!-- OCR Options -->
            <div class="mt-4">
                <div>
                    <label for="language" class="block text-sm font-medium text-gray-300">OCR Language</label>
                    {{ form.language(class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent",
                                  id="language") }}
                    <p class="mt-1 text-xs text-gray-400">Select the language of the text in the image</p>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="submit-button"
                    class="w-full px-4 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                    disabled>
                Extract Text
            </button>

            <!-- Progress Bar -->
            <div id="progress-container" class="hidden mt-4">
                <div class="flex justify-between text-sm text-gray-400 mb-1">
                    <span>Processing...</span>
                    <span id="progress-text">0%</span>
                </div>
                <div id="progress-bar" class="h-2 bg-purple-600 rounded-full transition-all duration-300" style="width: 0%;"></div>
            </div>
        </form>
    </div>

    {% if result %}
    <!-- Results Container -->
    <div id="results-container" class="w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 mt-6">
        <div class="mb-4">
            <h2 class="text-2xl font-bold text-white">Extracted Text</h2>
            <div class="flex justify-between items-center mt-2">
                <div class="text-gray-400">
                    <span class="text-purple-400">{{ result.word_count }}</span> words,
                    <span class="text-purple-400">{{ result.char_count }}</span> characters
                </div>
                <div class="flex space-x-2">
                    <button onclick="copyToClipboard()" class="px-3 py-1 bg-gray-700 text-gray-300 rounded hover:bg-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                        </svg>
                        Copy
                    </button>
                    <button onclick="downloadText()" class="px-3 py-1 bg-gray-700 text-gray-300 rounded hover:bg-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download
                    </button>
                </div>
            </div>
        </div>

        <div class="bg-gray-700 rounded-lg p-4">
            <div id="extracted-text" class="extracted-text text-gray-300">{{ result.text }}</div>
        </div>

        <div class="mt-4 grid grid-cols-2 gap-4">
            <div class="bg-gray-700 p-3 rounded-lg">
                <h3 class="text-purple-400 text-lg font-semibold mb-2">Image Info</h3>
                <ul class="text-gray-300 text-sm space-y-1">
                    <li><span class="text-gray-400">Format:</span> {{ result.format }}</li>
                    <li><span class="text-gray-400">Size:</span> {{ result.size[0] }}x{{ result.size[1] }}</li>
                    <li><span class="text-gray-400">Mode:</span> {{ result.mode }}</li>
                </ul>
            </div>
            <div class="bg-gray-700 p-3 rounded-lg">
                <h3 class="text-purple-400 text-lg font-semibold mb-2">Text Stats</h3>
                <ul class="text-gray-300 text-sm space-y-1">
                    <li><span class="text-gray-400">Words:</span> {{ result.word_count }}</li>
                    <li><span class="text-gray-400">Characters:</span> {{ result.char_count }}</li>
                    <li><span class="text-gray-400">Lines:</span> {{ result.text.count('\n') + 1 }}</li>
                    {% if result.optimized %}
                    <li><span class="text-gray-400">Optimized:</span> Yes</li>
                    {% endif %}
                    {% if result.processing_time %}
                    <li><span class="text-gray-400">Processing Time:</span> {{ result.processing_time }}</li>
                    {% endif %}
                </ul>
            </div>
        </div>

        <div class="mt-4">
            <button onclick="window.location.href='{{ url_for('tools.extract_text_page') }}'" class="w-full px-4 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700">
                Extract Another Image
            </button>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/extract_text.js') }}"></script>
{% endblock %}
