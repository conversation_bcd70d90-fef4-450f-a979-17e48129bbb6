/* 
 * Enhanced Drag & Drop Styling
 * Improves user experience with better visual feedback and accessibility
 */

/* Base drop area styling */
.drop-area {
  position: relative;
  border-width: 2px;
  border-style: dashed;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

/* Drop area hover state */
.drop-area:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Drop area active/focus state */
.drop-area:active,
.drop-area:focus-within {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Drop area when file is being dragged over */
.drop-area.drag-active {
  border-style: solid;
  background-color: rgba(55, 65, 81, 0.7);
  transform: scale(1.02);
}

/* Pulse animation for drop area when dragging over */
.drop-area.drag-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  animation: pulse 1.5s infinite;
  pointer-events: none;
}

@keyframes pulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

/* Icon animation on hover */
.drop-area:hover svg {
  transform: translateY(-5px);
}

.drop-area svg {
  transition: transform 0.3s ease;
}

/* Drop area text styling */
.drop-area p {
  margin-top: 0.5rem;
  transition: all 0.3s ease;
}

.drop-area:hover p {
  transform: scale(1.05);
}

/* Tool-specific drop area colors */
.drop-area.pdf-split {
  border-color: var(--tool-split-pdf);
}

.drop-area.pdf-split.drag-active {
  background-color: rgba(29, 78, 216, 0.15);
  border-color: var(--tool-split-pdf);
}

.drop-area.pdf-merge {
  border-color: var(--tool-merge-pdf);
}

.drop-area.pdf-merge.drag-active {
  background-color: rgba(185, 28, 28, 0.15);
  border-color: var(--tool-merge-pdf);
}

.drop-area.image-convert {
  border-color: var(--tool-convert-image);
}

.drop-area.image-convert.drag-active {
  background-color: rgba(180, 83, 9, 0.15);
  border-color: var(--tool-convert-image);
}

.drop-area.extract-text {
  border-color: var(--tool-extract-text);
}

.drop-area.extract-text.drag-active {
  background-color: rgba(109, 40, 217, 0.15);
  border-color: var(--tool-extract-text);
}

.drop-area.convert-files {
  border-color: var(--tool-convert-files);
}

.drop-area.convert-files.drag-active {
  background-color: rgba(4, 120, 87, 0.15);
  border-color: var(--tool-convert-files);
}

/* Enhanced error message styling */
.error-message {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: rgba(220, 38, 38, 0.1);
  border-left: 4px solid rgba(220, 38, 38, 0.8);
  color: var(--text-primary);
  animation: slideIn 0.3s ease-out;
  display: flex;
  align-items: flex-start;
}

.error-message svg {
  flex-shrink: 0;
  margin-right: 0.75rem;
  color: rgba(220, 38, 38, 0.8);
}

.error-message-content {
  flex-grow: 1;
}

.error-message-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.error-message-text {
  font-size: 0.875rem;
  opacity: 0.9;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* File type indicator badges */
.file-type-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-right: 0.5rem;
  text-transform: uppercase;
}

.file-type-badge.pdf {
  background-color: rgba(239, 68, 68, 0.2);
  color: rgb(239, 68, 68);
}

.file-type-badge.image {
  background-color: rgba(245, 158, 11, 0.2);
  color: rgb(245, 158, 11);
}

.file-type-badge.document {
  background-color: rgba(59, 130, 246, 0.2);
  color: rgb(59, 130, 246);
}

.file-type-badge.spreadsheet {
  background-color: rgba(16, 185, 129, 0.2);
  color: rgb(16, 185, 129);
}

/* Accessibility focus styles */
.drop-area:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
