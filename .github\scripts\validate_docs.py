#!/usr/bin/env python3
"""
Validate documentation files for ImgCipherF1.

This script checks:
1. API.md for required sections
2. Validates JSON examples in API.md
3. Ensures all endpoints are documented
"""

import os
import re
import sys
import json
from pathlib import Path

# Required sections in API.md
REQUIRED_SECTIONS = ["Authentication API", "Image Conversion API", "Task Management API", "Utility APIs", "Error Codes"]

# Required endpoints that must be documented
REQUIRED_ENDPOINTS = [
    "/auth/login",
    "/auth/register",
    "/auth/logout",
    "/tools/convert",
    "/tools/convert_file",
    "/tools/batch_convert",
    "/tools/api/task_progress/",
    "/tools/api/task_result/",
]


def validate_api_md():
    """Validate the API.md documentation file."""
    api_md_path = Path("docs/API.md")

    if not api_md_path.exists():
        print(f"Error: {api_md_path} does not exist")
        return False

    content = api_md_path.read_text(encoding="utf-8")

    # Check for required sections
    missing_sections = []
    for section in REQUIRED_SECTIONS:
        if section not in content:
            missing_sections.append(section)

    if missing_sections:
        print(f"Error: Missing required sections in API.md: {', '.join(missing_sections)}")
        return False

    # Check for required endpoints
    missing_endpoints = []
    for endpoint in REQUIRED_ENDPOINTS:
        if endpoint not in content:
            missing_endpoints.append(endpoint)

    if missing_endpoints:
        print(f"Error: Missing required endpoints in API.md: {', '.join(missing_endpoints)}")
        return False

    # Validate JSON examples
    json_blocks = re.findall(r"```json\n(.*?)\n```", content, re.DOTALL)

    for i, block in enumerate(json_blocks):
        try:
            json.loads(block)
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON in example block #{i+1}: {e}")
            print(f"Block content: {block}")
            return False

    print("API.md validation successful!")
    return True


def main():
    """Main entry point for the validation script."""
    success = validate_api_md()

    if not success:
        sys.exit(1)

    sys.exit(0)


if __name__ == "__main__":
    main()
