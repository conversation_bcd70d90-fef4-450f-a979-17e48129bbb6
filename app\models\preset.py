"""
Preset models for the application.
Defines Preset model for storing user conversion presets.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
import json
from flask import current_app
from app.extensions import db


class PresetType:
    """Constants for preset types."""

    FILE = "file"
    IMAGE = "image"
    PDF = "pdf"
    BATCH = "batch"
    BACKGROUND_REMOVAL = "background_removal"
    TEXT_EXTRACTION = "text_extraction"


class Preset(db.Model):
    """Model for storing user presets."""

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey("user.id", ondelete="CASCADE"), nullable=False)

    # Preset details
    name = db.Column(db.String(100), nullable=False)
    preset_type = db.Column(db.String(50), nullable=False)
    is_default = db.Column(db.<PERSON>, default=False)

    # Settings stored as JSON
    settings = db.Column(db.Text, nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship("User", backref=db.backref("presets", lazy="dynamic", cascade="all, delete-orphan"))

    def __repr__(self) -> str:
        return f"<Preset {self.name} ({self.preset_type})>"

    @property
    def settings_dict(self) -> Dict[str, Any]:
        """Get settings as a dictionary."""
        if not self.settings:
            return {}
        try:
            return json.loads(self.settings)
        except Exception:
            current_app.logger.error(f"Error parsing preset settings for preset {self.id}")
            return {}

    @settings_dict.setter
    def settings_dict(self, settings_dict: Dict[str, Any]) -> None:
        """Set settings from a dictionary."""
        self.settings = json.dumps(settings_dict)

    @classmethod
    def get_user_presets(cls, user_id: int, preset_type: Optional[str] = None) -> List["Preset"]:
        """Get all presets for a user, optionally filtered by type."""
        query = cls.query.filter_by(user_id=user_id)
        if preset_type:
            query = query.filter_by(preset_type=preset_type)
        return query.order_by(cls.is_default.desc(), cls.name).all()

    @classmethod
    def get_default_preset(cls, user_id: int, preset_type: str) -> Optional["Preset"]:
        """Get the default preset for a user and type."""
        return cls.query.filter_by(user_id=user_id, preset_type=preset_type, is_default=True).first()

    @classmethod
    def create_preset(
        cls, user_id: int, name: str, preset_type: str, settings_dict: Dict[str, Any], is_default: bool = False
    ) -> "Preset":
        """Create a new preset."""
        # If this is set as default, unset any existing defaults
        if is_default:
            cls.query.filter_by(user_id=user_id, preset_type=preset_type, is_default=True).update({"is_default": False})

        preset = cls(user_id=user_id, name=name, preset_type=preset_type, is_default=is_default)
        preset.settings_dict = settings_dict

        db.session.add(preset)
        db.session.commit()

        return preset

    def update(
        self, name: Optional[str] = None, settings_dict: Optional[Dict[str, Any]] = None, is_default: Optional[bool] = None
    ) -> "Preset":
        """Update the preset."""
        if name is not None:
            self.name = name

        if settings_dict is not None:
            self.settings_dict = settings_dict

        if is_default is not None and is_default != self.is_default:
            if is_default:
                # Unset any existing defaults
                Preset.query.filter_by(user_id=self.user_id, preset_type=self.preset_type, is_default=True).update(
                    {"is_default": False}
                )
            self.is_default = is_default

        db.session.commit()
        return self

    def delete(self) -> None:
        """Delete the preset."""
        db.session.delete(self)
        db.session.commit()
