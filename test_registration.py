from app import create_app
from app.extensions import db
from app.models.auth import Role, User, UserStatus
import traceback

app, _ = create_app()

with app.app_context():
    try:
        # Create database tables
        db.create_all()

        # Create default roles
        roles = [
            {"name": "guest", "description": "Limited access for non-registered users"},
            {"name": "registered", "description": "Standard access for registered users"},
            {"name": "admin", "description": "Full access to all features"},
        ]

        for role_data in roles:
            if not Role.query.filter_by(name=role_data["name"]).first():
                db.session.add(Role(**role_data))

        db.session.commit()
        print("Default roles created successfully")

        # Test user registration
        try:
            # Create a test user
            user = User(
                username="testuser",
                email="<EMAIL>",
                status=UserStatus.ACTIVE,
            )
            user.password = "testpassword"
            user.generate_api_key()

            # Add default role
            registered_role = Role.query.filter_by(name="registered").first()
            if registered_role:
                user.roles.append(registered_role)
            else:
                print("ERROR: 'registered' role not found")

            db.session.add(user)
            db.session.commit()

            print("Test user created successfully")

            # Verify the user was created
            test_user = User.query.filter_by(username="testuser").first()
            if test_user:
                print(f"User verification successful: {test_user.username}, {test_user.email}")
                print(f"User has roles: {[role.name for role in test_user.roles]}")
            else:
                print("ERROR: Failed to retrieve the test user")

        except Exception as e:
            print(f"ERROR during user creation: {str(e)}")
            traceback.print_exc()
            db.session.rollback()

    except Exception as e:
        print(f"ERROR: {str(e)}")
        traceback.print_exc()
