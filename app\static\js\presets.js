/**
 * Presets Functionality
 * Provides quick-select configurations for common scenarios
 */

class PresetsManager {
    /**
     * Initialize a presets manager
     * @param {Object} options - Configuration options
     * @param {string} options.presetType - Type of preset (file, image, pdf, batch, etc.)
     * @param {string} options.containerSelector - Selector for the presets container
     * @param {Function} options.onPresetSelected - Callback when a preset is selected
     * @param {Function} options.getFormValues - Function to get current form values
     * @param {Function} options.setFormValues - Function to set form values from a preset
     */
    constructor(options) {
        this.options = Object.assign({
            presetType: 'file',
            containerSelector: '#presets-container',
            onPresetSelected: null,
            getFormValues: () => ({}),
            setFormValues: () => {}
        }, options);

        this.container = document.querySelector(this.options.containerSelector);
        if (!this.container) {
            console.error(`Container with selector "${this.options.containerSelector}" not found.`);
            return;
        }

        this.presets = [];
        this.isAuthenticated = document.body.classList.contains('user-authenticated');
        this.init();
    }

    /**
     * Initialize the presets manager
     */
    init() {
        this.renderPresetsUI();
        this.loadPresets();
        this.setupEventListeners();
    }

    /**
     * Render the presets UI
     */
    renderPresetsUI() {
        // Create presets dropdown and buttons
        this.container.innerHTML = `
            <div class="presets-wrapper">
                <label for="preset-select" class="block text-sm font-medium text-gray-300 mb-1">Presets</label>
                <div class="flex space-x-2">
                    <select id="preset-select" class="flex-grow px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-green-600 focus:border-transparent">
                        <option value="">-- Select a preset --</option>
                    </select>
                    <button id="save-preset-btn" class="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition" title="Save current settings as preset">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                        </svg>
                    </button>
                    <button id="delete-preset-btn" class="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition hidden" title="Delete selected preset">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                </div>
            </div>
        `;

        // Get references to the elements
        this.presetSelect = this.container.querySelector('#preset-select');
        this.savePresetBtn = this.container.querySelector('#save-preset-btn');
        this.deletePresetBtn = this.container.querySelector('#delete-preset-btn');

        // Hide save button if user is not authenticated
        if (!this.isAuthenticated) {
            this.savePresetBtn.classList.add('hidden');
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Preset selection
        this.presetSelect.addEventListener('change', () => {
            const presetId = parseInt(this.presetSelect.value);
            if (presetId) {
                const preset = this.presets.find(p => p.id === presetId);
                if (preset) {
                    this.applyPreset(preset);
                    this.deletePresetBtn.classList.remove('hidden');
                } else {
                    this.deletePresetBtn.classList.add('hidden');
                }
            } else {
                this.deletePresetBtn.classList.add('hidden');
            }
        });

        // Save preset
        this.savePresetBtn.addEventListener('click', () => {
            if (this.isAuthenticated) {
                this.showSavePresetDialog();
            } else {
                alert('You must be logged in to save presets.');
            }
        });

        // Delete preset
        this.deletePresetBtn.addEventListener('click', () => {
            const presetId = parseInt(this.presetSelect.value);
            if (presetId) {
                if (confirm('Are you sure you want to delete this preset?')) {
                    this.deletePreset(presetId);
                }
            }
        });
    }

    /**
     * Load presets from the server
     */
    loadPresets() {
        if (!this.isAuthenticated) {
            // For non-authenticated users, load default presets
            this.loadDefaultPresets();
            return;
        }

        fetch(`/presets/api/presets?type=${this.options.presetType}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.presets = data.presets;
                    this.updatePresetsDropdown();
                }
            })
            .catch(error => {
                console.error('Error loading presets:', error);
            });
    }

    /**
     * Load default presets for non-authenticated users
     */
    loadDefaultPresets() {
        // Define default presets based on preset type
        const defaultPresets = this.getDefaultPresets();
        this.presets = defaultPresets;
        this.updatePresetsDropdown();
    }

    /**
     * Get default presets based on preset type
     * @returns {Array} Array of default presets
     */
    getDefaultPresets() {
        switch (this.options.presetType) {
            case 'file':
                return [
                    { id: -1, name: 'CSV to Excel', settings: { output_format: 'xlsx' } },
                    { id: -2, name: 'Excel to CSV', settings: { output_format: 'csv' } },
                    { id: -3, name: 'CSV to JSON', settings: { output_format: 'json' } },
                    { id: -4, name: 'JSON to CSV', settings: { output_format: 'csv' } }
                ];
            case 'image':
                return [
                    { id: -1, name: 'Web Optimization', settings: { format: 'webp', quality: 80 } },
                    { id: -2, name: 'High Quality JPG', settings: { format: 'jpg', quality: 95 } },
                    { id: -3, name: 'Transparent PNG', settings: { format: 'png', quality: 90 } },
                    { id: -4, name: 'Animated GIF', settings: { format: 'gif', quality: 85 } }
                ];
            case 'batch':
                return [
                    { id: -1, name: 'Batch to Excel', settings: { output_format: 'xlsx' } },
                    { id: -2, name: 'Batch to CSV', settings: { output_format: 'csv' } },
                    { id: -3, name: 'Batch to JSON', settings: { output_format: 'json' } },
                    { id: -4, name: 'Batch to XML', settings: { output_format: 'xml' } }
                ];
            default:
                return [];
        }
    }

    /**
     * Update the presets dropdown
     */
    updatePresetsDropdown() {
        // Clear existing options except the first one
        while (this.presetSelect.options.length > 1) {
            this.presetSelect.remove(1);
        }

        // Add presets to dropdown
        this.presets.forEach(preset => {
            const option = document.createElement('option');
            option.value = preset.id;
            option.textContent = preset.name;
            if (preset.is_default) {
                option.textContent += ' (Default)';
            }
            this.presetSelect.appendChild(option);
        });
    }

    /**
     * Apply a preset to the form
     * @param {Object} preset - The preset to apply
     */
    applyPreset(preset) {
        if (this.options.setFormValues) {
            this.options.setFormValues(preset.settings);
        }

        if (this.options.onPresetSelected) {
            this.options.onPresetSelected(preset);
        }
    }

    /**
     * Show dialog to save current settings as a preset
     */
    showSavePresetDialog() {
        const presetName = prompt('Enter a name for this preset:');
        if (presetName) {
            const formValues = this.options.getFormValues();
            this.savePreset(presetName, formValues);
        }
    }

    /**
     * Save a preset to the server
     * @param {string} name - Name of the preset
     * @param {Object} settings - Preset settings
     */
    savePreset(name, settings) {
        fetch('/presets/api/presets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            },
            body: JSON.stringify({
                name: name,
                preset_type: this.options.presetType,
                settings: settings,
                is_default: false
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.presets.push(data.preset);
                this.updatePresetsDropdown();
                
                // Select the new preset
                this.presetSelect.value = data.preset.id;
                this.deletePresetBtn.classList.remove('hidden');
                
                // Show success message
                alert('Preset saved successfully!');
            } else {
                alert('Error saving preset: ' + (data.error?.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error saving preset:', error);
            alert('Error saving preset. Please try again.');
        });
    }

    /**
     * Delete a preset
     * @param {number} presetId - ID of the preset to delete
     */
    deletePreset(presetId) {
        fetch(`/presets/api/presets/${presetId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove preset from array
                this.presets = this.presets.filter(p => p.id !== presetId);
                this.updatePresetsDropdown();
                
                // Reset selection
                this.presetSelect.value = '';
                this.deletePresetBtn.classList.add('hidden');
                
                // Show success message
                alert('Preset deleted successfully!');
            } else {
                alert('Error deleting preset: ' + (data.error?.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error deleting preset:', error);
            alert('Error deleting preset. Please try again.');
        });
    }

    /**
     * Get CSRF token from meta tag
     * @returns {string} CSRF token
     */
    getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }
}
