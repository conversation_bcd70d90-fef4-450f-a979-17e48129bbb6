import os
import pytesseract
from PIL import Image
import io
from flask import current_app
from werkzeug.datastructures import FileStorage
from werkzeug.utils import secure_filename
import logging
import concurrent.futures

# Configure logging
logger = logging.getLogger(__name__)


class TextExtractor:
    """Class for extracting text from images using OCR."""

    SUPPORTED_FORMATS = {"jpg", "jpeg", "png", "webp", "gif", "tiff", "bmp"}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    LARGE_IMAGE_THRESHOLD = 4 * 1024 * 1024  # 4MB
    MAX_IMAGE_DIMENSION = 4000  # Maximum width or height in pixels

    @classmethod
    def extract_text(cls, image_file: FileStorage, lang: str = "eng") -> dict:
        """
        Extract text from an image file using OCR.

        Args:
            image_file: The uploaded image file
            lang: Language for OCR (default: 'eng' for English)

        Returns:
            dict: Dictionary containing extracted text and metadata
        """
        # Validate input
        cls._validate_input(image_file)

        # Save image to temporary location
        temp_dir = os.path.join(current_app.config["UPLOAD_FOLDER"], "temp")
        os.makedirs(temp_dir, exist_ok=True)

        temp_filename = secure_filename(image_file.filename)
        temp_path = os.path.join(temp_dir, f"ocr_{temp_filename}")

        try:
            # Save the file temporarily
            image_file.save(temp_path)

            # Open the image with PIL
            with Image.open(temp_path) as img:
                # Check if image needs optimization
                is_large_image = cls._is_large_image(img)

                # Optimize image if needed
                if is_large_image:
                    logger.info(f"Optimizing large image: {temp_filename}")
                    img = cls._optimize_image(img)

                # For large images, use multi-processing to speed up OCR
                if is_large_image:
                    text = cls._process_large_image(img, lang)
                else:
                    # Extract text using pytesseract directly
                    text = pytesseract.image_to_string(img, lang=lang)

                # Get additional data
                data = {
                    "text": text.strip(),
                    "word_count": len(text.split()),
                    "char_count": len(text),
                    "format": img.format,
                    "mode": img.mode,
                    "size": img.size,
                    "optimized": is_large_image,
                }

                return data

        except Exception as e:
            logger.error(f"Error extracting text: {str(e)}")
            raise ValueError(f"Text extraction failed: {str(e)}")
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)

    @classmethod
    def _is_large_image(cls, img: Image.Image) -> bool:
        """Check if an image is considered large and needs optimization."""
        # Check dimensions
        width, height = img.size
        if width > cls.MAX_IMAGE_DIMENSION or height > cls.MAX_IMAGE_DIMENSION:
            return True

        # Check file size (approximate)
        try:
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format=img.format or "PNG")
            size = img_byte_arr.tell()
            return size > cls.LARGE_IMAGE_THRESHOLD
        except Exception:
            # If we can't determine size, use dimensions as fallback
            return width * height > 4000 * 3000

    @classmethod
    def _optimize_image(cls, img: Image.Image) -> Image.Image:
        """Optimize image for OCR processing."""
        # Resize large images while maintaining aspect ratio
        width, height = img.size
        if width > cls.MAX_IMAGE_DIMENSION or height > cls.MAX_IMAGE_DIMENSION:
            if width > height:
                new_width = cls.MAX_IMAGE_DIMENSION
                new_height = int(height * (cls.MAX_IMAGE_DIMENSION / width))
            else:
                new_height = cls.MAX_IMAGE_DIMENSION
                new_width = int(width * (cls.MAX_IMAGE_DIMENSION / height))

            img = img.resize((new_width, new_height), Image.LANCZOS)

        # Convert to grayscale if not already
        if img.mode != "L":
            img = img.convert("L")

        # Enhance contrast for better OCR
        from PIL import ImageEnhance

        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.5)  # Increase contrast by 50%

        return img

    @classmethod
    def _process_large_image(cls, img: Image.Image, lang: str) -> str:
        """Process large images by splitting into tiles and using parallel processing."""
        width, height = img.size

        # Define tile size (adjust based on your needs)
        tile_width = min(1000, width)
        tile_height = min(1000, height)

        # Calculate number of tiles
        cols = (width + tile_width - 1) // tile_width
        rows = (height + tile_height - 1) // tile_height

        # Create tiles
        tiles = []
        for row in range(rows):
            for col in range(cols):
                left = col * tile_width
                upper = row * tile_height
                right = min(left + tile_width, width)
                lower = min(upper + tile_height, height)

                tile = img.crop((left, upper, right, lower))
                tiles.append((row, col, tile))

        # Process tiles in parallel
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(4, os.cpu_count() or 1)) as executor:
            future_to_tile = {
                executor.submit(pytesseract.image_to_string, tile, lang=lang): (row, col) for row, col, tile in tiles
            }

            for future in concurrent.futures.as_completed(future_to_tile):
                row, col = future_to_tile[future]
                try:
                    text = future.result()
                    results.append((row, col, text))
                except Exception as e:
                    logger.error(f"Error processing tile ({row}, {col}): {str(e)}")
                    results.append((row, col, ""))

        # Sort results by row and column
        results.sort()

        # Combine text from all tiles
        text_rows = []
        current_row = -1
        current_row_text = []

        for row, col, text in results:
            if row != current_row:
                if current_row_text:
                    text_rows.append(" ".join(current_row_text))
                current_row = row
                current_row_text = []

            current_row_text.append(text.strip())

        # Add the last row
        if current_row_text:
            text_rows.append(" ".join(current_row_text))

        # Join all rows with newlines
        return "\n".join(text_rows)

    @classmethod
    def _validate_input(cls, image_file: FileStorage):
        """Validate the input image file."""
        if not image_file or not image_file.filename:
            raise ValueError("No file provided")

        # Check file size
        if image_file.content_length and image_file.content_length > cls.MAX_FILE_SIZE:
            raise ValueError(f"File size exceeds {cls.MAX_FILE_SIZE // (1024*1024)}MB limit")

        # Check file extension
        ext = image_file.filename.rsplit(".", 1)[1].lower() if "." in image_file.filename else ""
        if ext not in cls.SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported image format: {ext}. Supported formats: {', '.join(cls.SUPPORTED_FORMATS)}")
