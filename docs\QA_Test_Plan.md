# ImgCipherF1 QA Test Plan

This document outlines the manual QA testing process for the ImgCipherF1 application across different user roles and features.

## User Roles

The application has three user roles:

1. **Guest** - Unauthenticated users with limited access
2. **Registered** - Authenticated users with standard access
3. **Admin** - Authenticated users with full access to all features

## Test Environment Setup

Before running tests, ensure the following:

1. The application is running locally or on a staging server
2. Test data is available (sample images, PDFs, and other files)
3. Test accounts for each role are created
4. <PERSON>is and Celery workers are running for background tasks

## Test Cases by Role

### 1. Guest User Tests

| Test ID | Feature | Test Description | Expected Result |
|---------|---------|------------------|-----------------|
| G-01 | Homepage | Access the homepage | Homepage loads with limited options |
| G-02 | Authentication | Attempt to access restricted pages | Redirected to login page |
| G-03 | Image Conversion | Convert a single image | Image converts successfully with rate limiting |
| G-04 | File Conversion | Convert a single file | File converts successfully with rate limiting |
| G-05 | Batch Conversion | Attempt to access batch conversion | Redirected to login page |
| G-06 | Rate Limiting | Exceed rate limits for conversions | Receives 429 Too Many Requests error |
| G-07 | Registration | Register a new account | Account created successfully |
| G-08 | Login | Log in with valid credentials | Successfully logged in as registered user |

### 2. Registered User Tests

| Test ID | Feature | Test Description | Expected Result |
|---------|---------|------------------|-----------------|
| R-01 | Authentication | Log in with registered user | Successfully logged in |
| R-02 | Profile | Access and view profile | Profile page loads correctly |
| R-03 | Image Conversion | Convert multiple images | All images convert successfully |
| R-04 | File Conversion | Convert files with higher limits | Files convert successfully |
| R-05 | Batch Conversion | Submit batch conversion job | Job starts and progress page shown |
| R-06 | Task Progress | Monitor task progress | Progress updates in real-time |
| R-07 | Task Results | View and download task results | Results available and downloadable |
| R-08 | Logout | Log out of account | Successfully logged out |

### 3. Admin User Tests

| Test ID | Feature | Test Description | Expected Result |
|---------|---------|------------------|-----------------|
| A-01 | Authentication | Log in with admin credentials | Successfully logged in as admin |
| A-02 | Admin Features | Access admin-only features | All admin features accessible |
| A-03 | User Management | View and manage users | User management functions work correctly |
| A-04 | All Features | Access all application features | All features work correctly |

## Feature-Specific Test Cases

### 1. Image Conversion

| Test ID | Test Description | Test Data | Expected Result |
|---------|------------------|-----------|-----------------|
| IC-01 | Convert JPG to WebP | sample.jpg | WebP file created successfully |
| IC-02 | Convert PNG to JPG | sample.png | JPG file created successfully |
| IC-03 | Convert with quality settings | Any image | Image converted with specified quality |
| IC-04 | Convert multiple images | Multiple images | All images converted successfully |
| IC-05 | Convert oversized image | >16MB image | Error message shown |
| IC-06 | Convert invalid image | corrupt.jpg | Error message shown |

### 2. File Conversion

| Test ID | Test Description | Test Data | Expected Result |
|---------|------------------|-----------|-----------------|
| FC-01 | Convert CSV to Excel | sample.csv | Excel file created successfully |
| FC-02 | Convert Excel to JSON | sample.xlsx | JSON file created successfully |
| FC-03 | Convert PDF to DOCX | sample.pdf | DOCX file created successfully |
| FC-04 | Convert DOCX to PDF | sample.docx | PDF file created successfully |
| FC-05 | Convert invalid file | corrupt.csv | Error message shown |

### 3. Batch Processing

| Test ID | Test Description | Test Data | Expected Result |
|---------|------------------|-----------|-----------------|
| BP-01 | Batch convert multiple files | 5 CSV files | All files converted to Excel |
| BP-02 | Monitor task progress | Any batch job | Progress updates correctly |
| BP-03 | Download batch results | Completed batch | ZIP file with all results downloaded |
| BP-04 | Cancel running batch | Running batch | Batch job cancelled |
| BP-05 | Batch with mixed valid/invalid files | Mixed files | Valid files converted, errors for invalid |

### 4. Authentication & Authorization

| Test ID | Test Description | Test Steps | Expected Result |
|---------|------------------|-----------|-----------------|
| AA-01 | Register new account | Fill registration form | Account created successfully |
| AA-02 | Login with valid credentials | Enter username/password | Successfully logged in |
| AA-03 | Login with invalid credentials | Enter wrong password | Error message shown |
| AA-04 | Password reset request | Request password reset | Email sent (simulated) |
| AA-05 | Access control for guest | Try accessing restricted page | Redirected to login |
| AA-06 | Access control for registered | Try accessing admin page | Access denied message |
| AA-07 | Logout | Click logout | Successfully logged out |

## Cross-Browser Testing

Test the application in the following browsers:
- Chrome (latest)
- Firefox (latest)
- Edge (latest)
- Safari (if available)

## Mobile Responsiveness Testing

Test the application on:
- Desktop (1920x1080)
- Tablet (768x1024)
- Mobile (375x667)

## Performance Testing

| Test ID | Test Description | Expected Result |
|---------|------------------|-----------------|
| P-01 | Load homepage | Page loads in <2 seconds |
| P-02 | Convert single image | Process completes in <5 seconds |
| P-03 | Batch convert 10 files | All files processed in reasonable time |
| P-04 | Concurrent users (10) | System remains responsive |

## Security Testing

| Test ID | Test Description | Expected Result |
|---------|------------------|-----------------|
| S-01 | SQL Injection attempts | No vulnerabilities found |
| S-02 | XSS attempts | No vulnerabilities found |
| S-03 | CSRF protection | All forms protected |
| S-04 | Rate limiting | Limits enforced correctly |
| S-05 | File upload security | Only allowed files accepted |

## Test Execution Checklist

- [ ] All Guest user tests passed
- [ ] All Registered user tests passed
- [ ] All Admin user tests passed
- [ ] All Image Conversion tests passed
- [ ] All File Conversion tests passed
- [ ] All Batch Processing tests passed
- [ ] All Authentication & Authorization tests passed
- [ ] Cross-browser tests passed
- [ ] Mobile responsiveness tests passed
- [ ] Performance tests passed
- [ ] Security tests passed

## Bug Reporting Template

For any issues found, use the following template:

```
Bug ID: [Unique ID]
Feature: [Affected Feature]
Severity: [Critical/High/Medium/Low]
Description: [Detailed description of the issue]
Steps to Reproduce:
1. [Step 1]
2. [Step 2]
...
Expected Result: [What should happen]
Actual Result: [What actually happens]
Screenshots: [If applicable]
Environment: [Browser, OS, etc.]
```

## Test Results Summary

To be completed after test execution.
