from flask import Blueprint, render_template, make_response
from app.cache import cached_static_page
from app.utils import add_cache_headers

# Privacy policy blueprint
privacy_bp = Blueprint("privacy", __name__)


@privacy_bp.route("/privacy", methods=["GET"])
@cached_static_page("privacy_policy")
def privacy_policy():
    """Render the privacy policy page with caching."""
    response = make_response(render_template("privacy_policy.html"))
    return add_cache_headers(response, max_age=86400)  # Cache for 24 hours
