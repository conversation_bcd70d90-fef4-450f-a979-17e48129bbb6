/**
 * File Preview JavaScript Utilities
 * Provides functions for previewing different file types
 */

class FilePreview {
    /**
     * Initialize a file preview handler
     * @param {Object} options - Configuration options
     * @param {string} options.containerId - ID of the container element for the preview
     * @param {string} options.fileInputId - ID of the file input element
     * @param {Function} options.onPreviewReady - Callback function when preview is ready
     * @param {Function} options.onError - Callback function when preview fails
     * @param {Array} options.supportedTypes - Array of supported MIME types
     */
    constructor(options) {
        this.options = Object.assign({
            containerId: 'file-preview-container',
            fileInputId: 'file-input',
            onPreviewReady: null,
            onError: null,
            supportedTypes: [
                // Images
                'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/tiff', 'image/bmp',
                // PDFs
                'application/pdf',
                // Text
                'text/plain', 'text/csv', 'text/html', 'text/javascript', 'text/css',
                // Office
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
                'application/json',
                'application/xml'
            ],
            maxPreviewSize: 5 * 1024 * 1024 // 5MB max for preview
        }, options);

        this.container = document.getElementById(this.options.containerId);
        this.fileInput = document.getElementById(this.options.fileInputId);
        
        if (!this.container) {
            console.error(`Container with ID "${this.options.containerId}" not found.`);
            return;
        }
        
        if (!this.fileInput) {
            console.error(`File input with ID "${this.options.fileInputId}" not found.`);
            return;
        }
        
        this.currentFile = null;
        this.init();
    }
    
    /**
     * Initialize the file preview handler
     */
    init() {
        // Listen for file input changes
        this.fileInput.addEventListener('change', () => {
            if (this.fileInput.files.length > 0) {
                this.previewFile(this.fileInput.files[0]);
            } else {
                this.clearPreview();
            }
        });
    }
    
    /**
     * Preview a file based on its type
     * @param {File} file - The file to preview
     */
    previewFile(file) {
        this.currentFile = file;
        
        // Check if file is too large for preview
        if (file.size > this.options.maxPreviewSize) {
            this.showPreviewPlaceholder(file, 'File too large for preview');
            return;
        }
        
        // Check if file type is supported
        if (!this.options.supportedTypes.includes(file.type)) {
            this.showPreviewPlaceholder(file, 'Preview not available for this file type');
            return;
        }
        
        // Clear previous preview
        this.clearPreview();
        
        // Show loading state
        this.showLoading();
        
        // Handle different file types
        if (file.type.startsWith('image/')) {
            this.previewImage(file);
        } else if (file.type === 'application/pdf') {
            this.previewPDF(file);
        } else if (file.type.startsWith('text/') || 
                  file.type === 'application/json' || 
                  file.type === 'application/xml') {
            this.previewText(file);
        } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                  file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            this.showPreviewPlaceholder(file, 'Preview thumbnail available');
        } else {
            this.showPreviewPlaceholder(file, 'No preview available');
        }
    }
    
    /**
     * Preview an image file
     * @param {File} file - The image file to preview
     */
    previewImage(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const img = document.createElement('img');
            img.className = 'max-w-full max-h-64 rounded-lg';
            img.src = e.target.result;
            img.alt = file.name;
            
            this.clearPreview();
            this.container.appendChild(img);
            
            if (typeof this.options.onPreviewReady === 'function') {
                this.options.onPreviewReady(file, 'image');
            }
        };
        
        reader.onerror = () => {
            this.handlePreviewError('Failed to load image preview');
        };
        
        reader.readAsDataURL(file);
    }
    
    /**
     * Preview a PDF file
     * @param {File} file - The PDF file to preview
     */
    previewPDF(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            // Create PDF embed element
            const embed = document.createElement('embed');
            embed.className = 'w-full h-64 rounded-lg';
            embed.src = e.target.result;
            embed.type = 'application/pdf';
            
            // Create a container with toolbar
            const previewContainer = document.createElement('div');
            previewContainer.className = 'pdf-preview-container';
            
            // Add toolbar with download button
            const toolbar = document.createElement('div');
            toolbar.className = 'flex justify-between items-center bg-gray-800 p-2 rounded-t-lg';
            toolbar.innerHTML = `
                <span class="text-gray-300 text-sm truncate max-w-xs">${file.name}</span>
                <a href="${e.target.result}" download="${file.name}" class="text-blue-400 hover:text-blue-300 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                </a>
            `;
            
            previewContainer.appendChild(toolbar);
            previewContainer.appendChild(embed);
            
            this.clearPreview();
            this.container.appendChild(previewContainer);
            
            if (typeof this.options.onPreviewReady === 'function') {
                this.options.onPreviewReady(file, 'pdf');
            }
        };
        
        reader.onerror = () => {
            this.handlePreviewError('Failed to load PDF preview');
        };
        
        reader.readAsDataURL(file);
    }
    
    /**
     * Preview a text file
     * @param {File} file - The text file to preview
     */
    previewText(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            // Create text preview element
            const pre = document.createElement('pre');
            pre.className = 'bg-gray-800 p-3 rounded-lg text-gray-300 text-sm overflow-auto max-h-64';
            
            // Limit text preview to first 2000 characters
            let text = e.target.result;
            const isTruncated = text.length > 2000;
            if (isTruncated) {
                text = text.substring(0, 2000) + '...';
            }
            
            pre.textContent = text;
            
            // Create a container with info about truncation if needed
            const previewContainer = document.createElement('div');
            
            if (isTruncated) {
                const truncationInfo = document.createElement('div');
                truncationInfo.className = 'text-gray-500 text-xs mt-1';
                truncationInfo.textContent = 'Preview truncated. Download the file to view the complete content.';
                previewContainer.appendChild(truncationInfo);
            }
            
            previewContainer.appendChild(pre);
            
            this.clearPreview();
            this.container.appendChild(previewContainer);
            
            if (typeof this.options.onPreviewReady === 'function') {
                this.options.onPreviewReady(file, 'text');
            }
        };
        
        reader.onerror = () => {
            this.handlePreviewError('Failed to load text preview');
        };
        
        reader.readAsText(file);
    }
    
    /**
     * Show a placeholder for files that can't be previewed
     * @param {File} file - The file
     * @param {string} message - Message to display
     */
    showPreviewPlaceholder(file, message) {
        this.clearPreview();
        
        // Create placeholder element
        const placeholder = document.createElement('div');
        placeholder.className = 'flex flex-col items-center justify-center bg-gray-800 rounded-lg p-4 h-32';
        
        // Add appropriate icon based on file type
        let iconPath = '';
        if (file.type.includes('word')) {
            iconPath = 'M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z';
        } else if (file.type.includes('sheet')) {
            iconPath = 'M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2z';
        } else {
            iconPath = 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
        }
        
        placeholder.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${iconPath}" />
            </svg>
            <p class="text-gray-400 text-sm text-center">${message}</p>
            <p class="text-gray-500 text-xs mt-1">${file.name}</p>
        `;
        
        this.container.appendChild(placeholder);
        
        if (typeof this.options.onPreviewReady === 'function') {
            this.options.onPreviewReady(file, 'placeholder');
        }
    }
    
    /**
     * Show loading state
     */
    showLoading() {
        this.clearPreview();
        
        // Create loading element
        const loading = document.createElement('div');
        loading.className = 'flex items-center justify-center bg-gray-800 rounded-lg p-4 h-32';
        loading.innerHTML = `
            <svg class="animate-spin h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="ml-2 text-gray-400">Loading preview...</span>
        `;
        
        this.container.appendChild(loading);
    }
    
    /**
     * Handle preview error
     * @param {string} message - Error message
     */
    handlePreviewError(message) {
        this.clearPreview();
        
        // Create error element
        const error = document.createElement('div');
        error.className = 'bg-red-900 bg-opacity-50 text-red-300 p-3 rounded-lg text-sm';
        error.textContent = message;
        
        this.container.appendChild(error);
        
        if (typeof this.options.onError === 'function') {
            this.options.onError(message);
        }
    }
    
    /**
     * Clear the preview container
     */
    clearPreview() {
        while (this.container.firstChild) {
            this.container.removeChild(this.container.firstChild);
        }
    }
}

// Export to global scope
window.FilePreview = FilePreview;
