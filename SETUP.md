# ImgCipherF1 Setup Guide

This guide provides comprehensive instructions for setting up the ImgCipherF1 application across different platforms and environments.

## Quick Start

### Automated Setup (Recommended)

The easiest way to set up ImgCipherF1 is using the automated setup scripts:

#### Windows
```cmd
setup.bat
```

#### Unix/Linux/macOS
```bash
./setup.sh
```

#### Python (Cross-platform)
```bash
python setup.py --dev
```

## Setup Options

The setup script supports different environments and options:

### Environment Types

- `--dev` - Development environment (default)
  - Includes development dependencies
  - Enables debug mode
  - Runs tests after setup
  - Creates admin user with random password

- `--prod` - Production environment
  - Production-optimized configuration
  - No development dependencies
  - Debug mode disabled

- `--docker` - Docker environment
  - Configured for containerized deployment
  - Uses Redis container for caching

### Skip Options

- `--skip-venv` - Skip virtual environment creation
- `--skip-deps` - Skip dependency installation
- `--skip-db` - Skip database initialization
- `--force` - Force overwrite existing files

### Examples

```bash
# Development setup with all features
python setup.py --dev

# Production setup
python setup.py --prod

# Docker setup
python setup.py --docker

# Development setup without virtual environment
python setup.py --dev --skip-venv

# Force setup (overwrite existing files)
python setup.py --dev --force
```

## Prerequisites

### System Requirements

- **Python**: 3.9 or higher
- **Operating System**: Windows 10+, macOS 10.15+, or Linux
- **Memory**: 2GB RAM minimum, 4GB recommended
- **Storage**: 1GB free space

### Required Software

#### Tesseract OCR (Required for text extraction)

**Windows:**
1. Download from [UB Mannheim Tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
2. Install and add to PATH

**macOS:**
```bash
brew install tesseract
```

**Ubuntu/Debian:**
```bash
sudo apt-get install tesseract-ocr tesseract-ocr-eng
```

**CentOS/RHEL:**
```bash
sudo yum install tesseract tesseract-langpack-eng
```

#### Redis (Optional but recommended)

**Windows:**
- Use Docker: `docker run -d -p 6379:6379 redis:alpine`
- Or install via WSL

**macOS:**
```bash
brew install redis
brew services start redis
```

**Ubuntu/Debian:**
```bash
sudo apt-get install redis-server
sudo systemctl start redis-server
```

**CentOS/RHEL:**
```bash
sudo yum install redis
sudo systemctl start redis
```

## Manual Setup

If you prefer to set up manually or the automated script fails:

### 1. Clone Repository
```bash
git clone https://github.com/yourusername/ImgCipherF1.git
cd ImgCipherF1
```

### 2. Create Virtual Environment
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Unix/Linux/macOS
python3 -m venv venv
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install --upgrade pip
pip install -r requirements.txt

# For development
pip install -r requirements-dev.txt
```

### 4. Create Environment File
Create a `.env` file in the project root:

```env
# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
FLASK_DEBUG=true

# Security
SECRET_KEY=your-secret-key-here

# Database
DATABASE_URL=sqlite:///instance/app.db

# File Storage
UPLOAD_FOLDER=data/uploads
OUTPUT_FOLDER=data/outputs

# Redis (if available)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CACHE_REDIS_URL=redis://localhost:6379/1
```

### 5. Create Directories
```bash
mkdir -p data/uploads data/outputs logs instance
```

### 6. Initialize Database
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### 7. Create Admin User
```bash
flask create-admin --username admin --email <EMAIL> --password yourpassword
```

## Running the Application

### Development Mode

1. **Activate Virtual Environment:**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Unix/Linux/macOS
   source venv/bin/activate
   ```

2. **Start Redis (if installed):**
   ```bash
   redis-server
   ```

3. **Start Celery Worker (optional, for background tasks):**
   ```bash
   celery -A app.celery worker --loglevel=info
   ```

4. **Run the Application:**
   ```bash
   python run.py
   ```

5. **Open Browser:**
   Navigate to http://localhost:5000

### Production Mode

For production deployment, see [docs/deployment.md](docs/deployment.md).

### Docker Mode

```bash
docker-compose up -d
```

## Verification

### Test the Setup

```bash
# Run tests
pytest

# Check code quality
flake8 .
black --check .

# Test application import
python -c "from app import create_app; app, celery = create_app(); print('✓ Setup successful')"
```

### Access the Application

1. Open http://localhost:5000
2. Register a new account or login with admin credentials
3. Try uploading and converting a file
4. Check the Fun Zone features

## Troubleshooting

### Common Issues

#### Python Version Error
```
Error: Python 3.9+ required
```
**Solution:** Install Python 3.9 or higher from [python.org](https://python.org)

#### Tesseract Not Found
```
Error: tesseract-ocr not found
```
**Solution:** Install Tesseract OCR (see Prerequisites section)

#### Redis Connection Error
```
Error: Redis connection failed
```
**Solution:** 
- Install and start Redis server
- Or use memory storage by setting `CELERY_BROKER_URL=memory://` in .env

#### Permission Denied
```
Error: Permission denied creating directories
```
**Solution:** Run with appropriate permissions or change directory ownership

#### Database Migration Error
```
Error: Database migration failed
```
**Solution:**
```bash
# Reset database
rm instance/app.db
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### Getting Help

1. Check the [README.md](README.md) for basic information
2. Review [docs/API.md](docs/API.md) for API documentation
3. See [docs/deployment.md](docs/deployment.md) for deployment issues
4. Open an issue on GitHub for bugs or feature requests

## Development Workflow

After setup, use these commands for development:

```bash
# Activate environment
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Run tests
pytest

# Format code
black .
isort .

# Check code quality
flake8 .

# Run application
python run.py

# Database migrations
flask db migrate -m "Description of changes"
flask db upgrade
```

## Next Steps

- Read the [README.md](README.md) for usage instructions
- Check [docs/API.md](docs/API.md) for API documentation
- Review [docs/deployment.md](docs/deployment.md) for production deployment
- Explore the application features and tools
