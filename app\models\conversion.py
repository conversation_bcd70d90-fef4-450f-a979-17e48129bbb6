"""
Conversion models for the application.
Defines ConversionHistory model for tracking user conversions.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
import json
from flask import current_app
from app.extensions import db
from flask_login import current_user


class ConversionType:
    """Constants for conversion types."""

    FILE = "file"
    IMAGE = "image"
    PDF = "pdf"
    BATCH = "batch"
    BACKGROUND_REMOVAL = "background_removal"
    TEXT_EXTRACTION = "text_extraction"


class ConversionStatus:
    """Constants for conversion statuses."""

    SUCCESS = "success"
    FAILED = "failed"
    PROCESSING = "processing"
    CANCELED = "canceled"


class ConversionHistory(db.Model):
    """Model for tracking conversion history."""

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id", ondelete="SET NULL"), nullable=True)
    session_id = db.Column(db.String(64), nullable=False, index=True)

    # Conversion details
    conversion_type = db.Column(db.String(50), nullable=False)
    input_format = db.Column(db.String(20), nullable=False)
    output_format = db.Column(db.String(20), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    output_filename = db.Column(db.String(255), nullable=True)
    file_size = db.Column(db.Integer, nullable=True)  # Size in bytes

    # Status tracking
    status = db.Column(db.String(20), default=ConversionStatus.PROCESSING)
    error_message = db.Column(db.Text, nullable=True)

    # Metadata (stored as JSON)
    conversion_metadata = db.Column(db.Text, nullable=True)

    # Task ID for background jobs
    task_id = db.Column(db.String(64), nullable=True, index=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship("User", backref=db.backref("conversions", lazy="dynamic"))

    @property
    def metadata_dict(self) -> Dict[str, Any]:
        """Return metadata as a dictionary."""
        if not self.conversion_metadata:
            return {}
        try:
            return json.loads(self.conversion_metadata)
        except json.JSONDecodeError:
            current_app.logger.error(f"Invalid JSON in metadata for conversion {self.id}")
            return {}

    @metadata_dict.setter
    def metadata_dict(self, value: Dict[str, Any]) -> None:
        """Set metadata from a dictionary."""
        if not isinstance(value, dict):
            raise ValueError("Metadata must be a dictionary")
        self.conversion_metadata = json.dumps(value)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary for API responses."""
        return {
            "id": self.id,
            "conversion_type": self.conversion_type,
            "input_format": self.input_format,
            "output_format": self.output_format,
            "original_filename": self.original_filename,
            "output_filename": self.output_filename,
            "file_size": self.file_size,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "metadata": self.metadata_dict,
        }

    @classmethod
    def create_record(
        cls,
        conversion_type: str,
        input_format: str,
        output_format: str,
        original_filename: str,
        session_id: str,
        file_size: Optional[int] = None,
        output_filename: Optional[str] = None,
        status: str = ConversionStatus.PROCESSING,
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> "ConversionHistory":
        """
        Create and save a new conversion history record.

        Args:
            conversion_type: Type of conversion (file, image, etc.)
            input_format: Input file format
            output_format: Output file format
            original_filename: Original filename
            session_id: Session ID for guest users
            file_size: Size of the file in bytes
            output_filename: Output filename
            status: Conversion status
            task_id: Celery task ID for background jobs
            metadata: Additional metadata as dictionary

        Returns:
            The created ConversionHistory instance
        """
        record = cls(
            user_id=current_user.id if current_user.is_authenticated else None,
            session_id=session_id,
            conversion_type=conversion_type,
            input_format=input_format,
            output_format=output_format,
            original_filename=original_filename,
            output_filename=output_filename,
            file_size=file_size,
            status=status,
            task_id=task_id,
        )

        if metadata:
            record.metadata_dict = metadata

        db.session.add(record)
        db.session.commit()

        return record

    @classmethod
    def get_user_history(cls, limit: int = 20) -> List["ConversionHistory"]:
        """Get conversion history for the current user."""
        if current_user.is_authenticated:
            return cls.query.filter_by(user_id=current_user.id).order_by(cls.created_at.desc()).limit(limit).all()
        return []

    @classmethod
    def get_session_history(cls, session_id: str, limit: int = 10) -> List["ConversionHistory"]:
        """Get conversion history for a guest session."""
        return cls.query.filter_by(session_id=session_id).order_by(cls.created_at.desc()).limit(limit).all()

    def update_status(self, status: str, error_message: Optional[str] = None) -> None:
        """Update the status of a conversion."""
        self.status = status
        if error_message:
            self.error_message = error_message
        db.session.commit()
