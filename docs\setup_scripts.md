# ImgCipherF1 Setup Scripts Documentation

This document describes the various setup scripts and tools available for the ImgCipherF1 project.

## Overview

The ImgCipherF1 project includes several setup scripts to make installation and configuration as easy as possible across different platforms and environments:

- `setup.py` - Main Python setup script (cross-platform)
- `setup.bat` - Windows batch file wrapper
- `setup.sh` - Unix/Linux/macOS shell script wrapper
- `Makefile` - Development workflow automation
- `requirements-dev.txt` - Development dependencies

## Main Setup Script (setup.py)

The primary setup script is a comprehensive Python script that handles:

### Features

- **Cross-platform compatibility** (Windows, macOS, Linux)
- **Environment detection** and configuration
- **Prerequisite checking** (Python version, system packages)
- **Virtual environment management**
- **Dependency installation** with environment-specific packages
- **Database initialization** with migrations
- **Admin user creation** with secure password generation
- **Directory structure creation**
- **Environment file generation** with secure defaults
- **Test execution** for verification

### Usage

```bash
python setup.py [options]
```

### Options

#### Environment Types (mutually exclusive)
- `--dev` - Development environment (default)
  - Includes development dependencies (pytest, black, flake8, etc.)
  - Enables debug mode
  - Runs tests after setup
  - Creates admin user with random password

- `--prod` - Production environment
  - Production-optimized configuration
  - No development dependencies
  - Debug mode disabled
  - Secure defaults

- `--docker` - Docker environment
  - Configured for containerized deployment
  - Uses Redis container hostnames
  - Optimized for container environments

#### Skip Options
- `--skip-venv` - Skip virtual environment creation
- `--skip-deps` - Skip dependency installation
- `--skip-db` - Skip database initialization

#### Other Options
- `--force` - Force overwrite existing files
- `--help` - Show help message

### Examples

```bash
# Development setup (default)
python setup.py --dev

# Production setup
python setup.py --prod

# Docker setup
python setup.py --docker

# Development without virtual environment
python setup.py --dev --skip-venv

# Force overwrite existing configuration
python setup.py --dev --force
```

## Platform-Specific Wrappers

### Windows (setup.bat)

A Windows batch file that provides a user-friendly interface for Windows users:

```cmd
setup.bat [--dev|--prod|--docker|--help]
```

Features:
- Checks for Python installation
- Provides clear error messages
- Pauses for user interaction
- Handles Windows-specific paths

### Unix/Linux/macOS (setup.sh)

A shell script for Unix-like systems:

```bash
./setup.sh [--dev|--prod|--docker|--help]
```

Features:
- Colored output for better readability
- Python version validation
- Cross-platform package manager instructions
- Error handling with exit codes

## Development Workflow (Makefile)

The Makefile provides common development tasks:

### Setup Commands
```bash
make setup-dev      # Development environment setup
make setup-prod     # Production environment setup
make setup-docker   # Docker environment setup
make install        # Install dependencies only
```

### Development Commands
```bash
make run           # Run the application
make test          # Run test suite
make test-cov      # Run tests with coverage
make lint          # Code linting
make format        # Code formatting
make format-check  # Check code formatting
```

### Database Commands
```bash
make db-init       # Initialize database
make db-migrate    # Create migration
make db-upgrade    # Apply migrations
make db-reset      # Reset database (WARNING: destroys data)
```

### Docker Commands
```bash
make docker-build  # Build Docker images
make docker-run    # Run with Docker Compose
make docker-stop   # Stop containers
make docker-clean  # Clean Docker resources
```

### Utility Commands
```bash
make clean         # Clean temporary files
make deps-update   # Update dependencies
make security      # Run security checks
make quality       # Full quality check
```

## Development Dependencies (requirements-dev.txt)

Additional packages for development:

### Testing
- `pytest` - Testing framework
- `pytest-cov` - Coverage reporting
- `pytest-mock` - Mocking utilities
- `pytest-flask` - Flask testing utilities

### Code Quality
- `black` - Code formatting
- `flake8` - Linting
- `isort` - Import sorting
- `mypy` - Type checking

### Development Tools
- `pre-commit` - Git hooks
- `ipython` - Enhanced REPL
- `ipdb` - Debugger

### Documentation
- `sphinx` - Documentation generation
- `sphinx-rtd-theme` - Documentation theme

### Security
- `bandit` - Security linting
- `safety` - Dependency vulnerability checking

### Performance
- `locust` - Load testing

## Setup Process Flow

1. **Prerequisites Check**
   - Python version validation (3.9+)
   - System package detection (Tesseract, Redis)
   - Platform-specific instructions

2. **Virtual Environment**
   - Creation (if not skipped)
   - Activation for subsequent commands

3. **Dependencies**
   - pip upgrade
   - Core requirements installation
   - Development dependencies (if dev mode)

4. **Directory Structure**
   - Create data directories (uploads, outputs)
   - Create log directory
   - Create instance directory

5. **Configuration**
   - Generate secure .env file
   - Environment-specific settings
   - Secret key generation

6. **Database**
   - Initialize Flask-Migrate
   - Create initial migration
   - Apply migrations

7. **Admin User**
   - Check for existing admin
   - Create with secure random password
   - Display credentials

8. **Verification**
   - Import test
   - Test suite execution (dev mode)
   - Success confirmation

## Error Handling

The setup scripts include comprehensive error handling:

- **Graceful failures** with informative messages
- **Rollback capabilities** for partial failures
- **Manual recovery instructions** when automated setup fails
- **Platform-specific troubleshooting** guidance

## Security Considerations

- **Secure secret key generation** using cryptographically secure random
- **Random admin passwords** to prevent default credential attacks
- **Environment file protection** with appropriate permissions
- **Dependency verification** through requirements files
- **Security scanning** integration with development workflow

## Customization

### Environment Variables

The setup script generates a comprehensive .env file that can be customized:

```env
# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
FLASK_DEBUG=true

# Security
SECRET_KEY=<generated-secure-key>

# Database
DATABASE_URL=sqlite:///instance/app.db

# Redis Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CACHE_REDIS_URL=redis://localhost:6379/1

# File Storage
UPLOAD_FOLDER=data/uploads
OUTPUT_FOLDER=data/outputs
MAX_CONTENT_LENGTH=16777216

# Logging
LOG_FOLDER=logs
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5
```

### Configuration Override

For production deployments, override settings by:

1. Modifying the .env file
2. Setting environment variables
3. Using configuration files
4. Command-line arguments

## Troubleshooting

### Common Issues

1. **Python Version Mismatch**
   - Install Python 3.9+
   - Update PATH configuration

2. **Permission Errors**
   - Run with appropriate permissions
   - Check directory ownership

3. **Missing System Packages**
   - Install Tesseract OCR
   - Install Redis (optional)

4. **Network Issues**
   - Check internet connectivity
   - Configure proxy settings if needed

5. **Virtual Environment Issues**
   - Use `--skip-venv` flag
   - Manually create virtual environment

### Recovery Procedures

If setup fails:

1. **Clean and retry**:
   ```bash
   make clean
   python setup.py --dev --force
   ```

2. **Manual setup**:
   Follow the manual setup instructions in SETUP.md

3. **Partial setup**:
   Use skip flags to bypass problematic steps

## Best Practices

1. **Always use virtual environments** for isolation
2. **Review generated .env file** before production use
3. **Change default admin password** immediately
4. **Run tests** to verify setup
5. **Keep dependencies updated** regularly
6. **Use appropriate environment** for your use case

## Integration with CI/CD

The setup scripts integrate well with CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Setup Application
  run: python setup.py --prod --skip-venv

- name: Run Tests
  run: make test

- name: Security Check
  run: make security
```

This comprehensive setup system ensures that ImgCipherF1 can be easily deployed across different environments while maintaining security and reliability standards.
