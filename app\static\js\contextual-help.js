/**
 * Contextual Help Functionality
 * Provides tooltips and help panels for key UI elements
 */

class ContextualHelp {
  constructor(options = {}) {
    // Default configuration
    this.config = {
      tooltipSelector: '[data-tooltip]',
      helpPanelSelector: '[data-help-panel]',
      helpToggleSelector: '[data-help-toggle]',
      tooltipClass: 'contextual-tooltip',
      helpPanelClass: 'contextual-help-panel',
      activeClass: 'active',
      tooltipPosition: 'top',
      tooltipDelay: 300,
      ...options
    };

    // State
    this.tooltips = [];
    this.helpPanels = [];
    this.tooltipTimeout = null;
    this.activeTooltip = null;
    this.activeHelpPanel = null;

    // Bind methods
    this.init = this.init.bind(this);
    this.createTooltip = this.createTooltip.bind(this);
    this.showTooltip = this.showTooltip.bind(this);
    this.hideTooltip = this.hideTooltip.bind(this);
    this.positionTooltip = this.positionTooltip.bind(this);
    this.toggleHelpPanel = this.toggleHelpPanel.bind(this);
    this.handleEscapeKey = this.handleEscapeKey.bind(this);
    this.handleClickOutside = this.handleClickOutside.bind(this);
  }

  /**
   * Initialize contextual help
   */
  init() {
    // Initialize tooltips
    this.initTooltips();

    // Initialize help panels
    this.initHelpPanels();

    // Add global event listeners
    document.addEventListener('keydown', this.handleEscapeKey);
    document.addEventListener('click', this.handleClickOutside);
  }

  /**
   * Initialize tooltips
   */
  initTooltips() {
    // Find all elements with tooltip data attribute
    const tooltipElements = document.querySelectorAll(this.config.tooltipSelector);

    // Add event listeners to each tooltip element
    tooltipElements.forEach(element => {
      // Get tooltip content
      const tooltipContent = element.getAttribute('data-tooltip');
      if (!tooltipContent) return;

      // Get tooltip position (optional)
      const tooltipPosition = element.getAttribute('data-tooltip-position') || this.config.tooltipPosition;

      // Store tooltip data
      this.tooltips.push({
        element,
        content: tooltipContent,
        position: tooltipPosition
      });

      // Add event listeners
      element.addEventListener('mouseenter', () => this.showTooltip(element, tooltipContent, tooltipPosition));
      element.addEventListener('mouseleave', () => this.hideTooltip());
      element.addEventListener('focus', () => this.showTooltip(element, tooltipContent, tooltipPosition));
      element.addEventListener('blur', () => this.hideTooltip());
    });
  }

  /**
   * Initialize help panels
   */
  initHelpPanels() {
    // Find all help panel toggle elements
    const helpToggleElements = document.querySelectorAll(this.config.helpToggleSelector);

    // Add event listeners to each help toggle element
    helpToggleElements.forEach(element => {
      // Get target help panel ID
      const targetId = element.getAttribute('data-help-toggle');
      if (!targetId) return;

      // Find target help panel
      const helpPanel = document.getElementById(targetId);
      if (!helpPanel) return;

      // Store help panel data
      this.helpPanels.push({
        toggleElement: element,
        panel: helpPanel
      });

      // Add event listener
      element.addEventListener('click', (event) => {
        event.preventDefault();
        this.toggleHelpPanel(targetId);
      });

      // Add keyboard support
      element.addEventListener('keydown', (event) => {
        if (event.key === 'Enter' || event.key === ' ') {
          event.preventDefault();
          this.toggleHelpPanel(targetId);
        }
      });
    });
  }

  /**
   * Create tooltip element
   * @param {string} content Tooltip content
   * @returns {HTMLElement} Tooltip element
   */
  createTooltip(content) {
    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = this.config.tooltipClass;
    tooltip.setAttribute('role', 'tooltip');
    tooltip.innerHTML = content;

    // Add to DOM
    document.body.appendChild(tooltip);

    return tooltip;
  }

  /**
   * Show tooltip
   * @param {HTMLElement} element Target element
   * @param {string} content Tooltip content
   * @param {string} position Tooltip position
   */
  showTooltip(element, content, position) {
    // Clear any existing timeout
    if (this.tooltipTimeout) {
      clearTimeout(this.tooltipTimeout);
      this.tooltipTimeout = null;
    }

    // Hide any existing tooltip
    if (this.activeTooltip) {
      this.hideTooltip();
    }

    // Set timeout to show tooltip (prevents flashing on quick mouse movements)
    this.tooltipTimeout = setTimeout(() => {
      // Create tooltip
      this.activeTooltip = this.createTooltip(content);

      // Position tooltip
      this.positionTooltip(element, this.activeTooltip, position);

      // Add active class after a small delay for animation
      setTimeout(() => {
        this.activeTooltip.classList.add(this.config.activeClass);
      }, 10);
    }, this.config.tooltipDelay);
  }

  /**
   * Hide tooltip
   */
  hideTooltip() {
    // Clear any existing timeout
    if (this.tooltipTimeout) {
      clearTimeout(this.tooltipTimeout);
      this.tooltipTimeout = null;
    }

    // Remove active tooltip
    if (this.activeTooltip) {
      this.activeTooltip.classList.remove(this.config.activeClass);

      // Remove from DOM after animation completes
      setTimeout(() => {
        if (this.activeTooltip && this.activeTooltip.parentNode) {
          this.activeTooltip.parentNode.removeChild(this.activeTooltip);
        }
        this.activeTooltip = null;
      }, 200);
    }
  }

  /**
   * Position tooltip relative to target element
   * @param {HTMLElement} targetElement Target element
   * @param {HTMLElement} tooltipElement Tooltip element
   * @param {string} position Tooltip position
   */
  positionTooltip(targetElement, tooltipElement, position) {
    const targetRect = targetElement.getBoundingClientRect();
    const tooltipRect = tooltipElement.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    let top, left;

    // Calculate position based on specified position
    switch (position) {
      case 'top':
        top = targetRect.top + scrollTop - tooltipRect.height - 10;
        left = targetRect.left + scrollLeft + (targetRect.width / 2) - (tooltipRect.width / 2);
        tooltipElement.classList.add('position-top');
        break;
      case 'bottom':
        top = targetRect.bottom + scrollTop + 10;
        left = targetRect.left + scrollLeft + (targetRect.width / 2) - (tooltipRect.width / 2);
        tooltipElement.classList.add('position-bottom');
        break;
      case 'left':
        top = targetRect.top + scrollTop + (targetRect.height / 2) - (tooltipRect.height / 2);
        left = targetRect.left + scrollLeft - tooltipRect.width - 10;
        tooltipElement.classList.add('position-left');
        break;
      case 'right':
        top = targetRect.top + scrollTop + (targetRect.height / 2) - (tooltipRect.height / 2);
        left = targetRect.right + scrollLeft + 10;
        tooltipElement.classList.add('position-right');
        break;
      default:
        top = targetRect.top + scrollTop - tooltipRect.height - 10;
        left = targetRect.left + scrollLeft + (targetRect.width / 2) - (tooltipRect.width / 2);
        tooltipElement.classList.add('position-top');
    }

    // Ensure tooltip stays within viewport
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Adjust horizontal position if needed
    if (left < 10) {
      left = 10;
    } else if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10;
    }

    // Adjust vertical position if needed
    if (top < 10) {
      // If tooltip would go off the top of the screen, position it below the element instead
      if (position === 'top') {
        top = targetRect.bottom + scrollTop + 10;
        tooltipElement.classList.remove('position-top');
        tooltipElement.classList.add('position-bottom');
      } else {
        top = 10;
      }
    } else if (top + tooltipRect.height > viewportHeight + scrollTop - 10) {
      // If tooltip would go off the bottom of the screen, position it above the element instead
      if (position === 'bottom') {
        top = targetRect.top + scrollTop - tooltipRect.height - 10;
        tooltipElement.classList.remove('position-bottom');
        tooltipElement.classList.add('position-top');
      } else {
        top = viewportHeight + scrollTop - tooltipRect.height - 10;
      }
    }

    // Apply position
    tooltipElement.style.top = `${top}px`;
    tooltipElement.style.left = `${left}px`;
  }

  /**
   * Toggle help panel
   * @param {string} panelId Help panel ID
   */
  toggleHelpPanel(panelId) {
    // Find help panel
    const helpPanel = document.getElementById(panelId);
    if (!helpPanel) return;

    // Find toggle element
    const toggleElement = document.querySelector(`[data-help-toggle="${panelId}"]`);

    // Check if panel is already active
    const isActive = helpPanel.classList.contains(this.config.activeClass);

    // Close any other open help panels
    this.helpPanels.forEach(({ panel, toggleElement: toggle }) => {
      if (panel.id !== panelId && panel.classList.contains(this.config.activeClass)) {
        panel.classList.remove(this.config.activeClass);
        toggle.setAttribute('aria-expanded', 'false');
      }
    });

    // Toggle current panel
    if (isActive) {
      helpPanel.classList.remove(this.config.activeClass);
      if (toggleElement) {
        toggleElement.setAttribute('aria-expanded', 'false');
      }
      this.activeHelpPanel = null;
    } else {
      helpPanel.classList.add(this.config.activeClass);
      if (toggleElement) {
        toggleElement.setAttribute('aria-expanded', 'true');
      }
      this.activeHelpPanel = helpPanel;

      // Set focus to the help panel for accessibility
      helpPanel.setAttribute('tabindex', '-1');
      helpPanel.focus();
    }
  }

  /**
   * Handle Escape key press
   * @param {KeyboardEvent} event Keyboard event
   */
  handleEscapeKey(event) {
    if (event.key === 'Escape') {
      // Hide tooltip
      this.hideTooltip();

      // Close active help panel
      if (this.activeHelpPanel) {
        const panelId = this.activeHelpPanel.id;
        this.toggleHelpPanel(panelId);
      }
    }
  }

  /**
   * Handle click outside help panel
   * @param {MouseEvent} event Mouse event
   */
  handleClickOutside(event) {
    // Close active help panel if clicking outside
    if (this.activeHelpPanel) {
      const toggleElement = document.querySelector(`[data-help-toggle="${this.activeHelpPanel.id}"]`);
      
      // Check if click is outside both the panel and its toggle button
      if (!this.activeHelpPanel.contains(event.target) && 
          (!toggleElement || !toggleElement.contains(event.target))) {
        this.toggleHelpPanel(this.activeHelpPanel.id);
      }
    }
  }
}

// Initialize contextual help when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create and initialize contextual help
  window.contextualHelp = new ContextualHelp();
  window.contextualHelp.init();
});
