/* styles.css */
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f9;
    color: #333;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 500px;
    width: 100%;
    position: relative;
}

h1 {
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
    color: #007bff;
}

p {
    margin-bottom: 1.5rem;
    color: #666;
}

/* Logo */
.logo-container {
    margin-bottom: 1.5rem;
}

.logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 1px solid #007bff;
    padding: 5px;
}

/* File Upload Section */
.upload-section {
    margin-bottom: 1.5rem;
}

.upload-label {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.upload-label:hover {
    background: #0056b3;
}

#file-input {
    display: none;
}

#file-names {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

/* Quality Input Row */
.quality-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.quality-row input {
    width: 60px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

/* Separator Line */
.separator {
    width: 30%;
    height: 2px;
    background-color: #007bff;
    margin: 1.5rem auto;
}

/* Buttons */
button {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
}

button:hover {
    background: #218838;
}

/* Per-File Progress Bars */
.progress-item {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    background-color: #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.progress {
    width: 0%;
    height: 10px;
    background-color: #007bff;
    transition: width 0.3s ease;
}

/* Radial Progress Bar */
.radial-progress {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 1.5rem auto;
}

.radial-progress-bar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(#007bff 0%, transparent 0% 100%);
    position: relative;
}

.radial-progress-bar::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 95px;
    height: 95px;
    border-radius: 50%;
    background: white;
}

.radial-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    color: #007bff;
}

/* Footer */
footer {
    margin-top: 2rem;
    font-size: 0.8rem; /* 50% smaller than other text */
    color: #666;
}

footer a {
    color: #007bff;
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}