"""
File helper utilities for the application.
"""

import mimetypes
import uuid
from pathlib import Path
from werkzeug.utils import secure_filename
from typing import Op<PERSON>, Tuple, Set

from app.utils.constants import (
    ALLOWED_IMAGE_EXTENSIONS,
    ALLOWED_FILE_EXTENSIONS,
    MAX_IMAGE_SIZE,
    MAX_FILE_SIZE
)


def allowed_file(filename: str, allowed_extensions: set) -> bool:
    """Check if the file has an allowed extension and valid MIME type."""
    if not filename or "." not in filename:
        return False

    ext = filename.rsplit(".", 1)[1].lower()
    if ext not in allowed_extensions:
        print(f"Invalid extension: {ext}")
        return False

    # Skip MIME type check for certain extensions
    if ext in {"csv", "xlsx", "json", "xml", "parquet"}:
        return True

    mime_type, _ = mimetypes.guess_type(filename)
    if mime_type is None:
        return False

    # Check if the main type is 'image' for image files
    if ext in ALLOWED_IMAGE_EXTENSIONS and not mime_type.startswith("image/"):
        return False

    return True


def check_file_size(file, max_size: int) -> bool:
    """Check if the uploaded file size is within the allowed limit."""
    # Get the file size by seeking to end
    file.seek(0, 2)  # Seek to end of file
    file_size = file.tell()
    file.seek(0)  # Reset file pointer to beginning
    return file_size <= max_size


def secure_and_validate_filename(filename: str, allowed_extensions: set, max_length: int = 100) -> Optional[str]:
    """Secure and validate a filename based on length and allowed extensions."""
    if not filename:
        return None

    filename = secure_filename(filename)
    if len(filename) > max_length or not allowed_file(filename, allowed_extensions):
        return None
    return filename


def save_uploaded_file(file, upload_folder: str, file_type: str) -> Tuple[str, str]:
    """Securely saves an uploaded file and returns the stored path & original filename."""
    # Check file size
    if file_type == "image" and not check_file_size(file, MAX_IMAGE_SIZE):
        raise ValueError(f"Image file exceeds the {MAX_IMAGE_SIZE // (1024 * 1024)}MB size limit.")
    elif file_type == "file" and not check_file_size(file, MAX_FILE_SIZE):
        raise ValueError(f"File exceeds the {MAX_FILE_SIZE // (1024 * 1024)}MB size limit.")

    upload_folder = Path(upload_folder)
    upload_folder.mkdir(parents=True, exist_ok=True)  # Ensure directory exists

    original_filename = secure_filename(file.filename)
    unique_filename = f"{uuid.uuid4().hex}_{original_filename}"
    file_path = upload_folder / unique_filename

    file.save(str(file_path))
    return str(file_path), original_filename  # Return full file path and original name
