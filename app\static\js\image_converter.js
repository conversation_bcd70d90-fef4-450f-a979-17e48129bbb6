// DOM Elements
const dropArea = document.getElementById("drop-area");
const fileInput = document.getElementById("file-input");
const filePreviewContainer = document.getElementById("file-preview-container");
const fileInfo = document.getElementById("file-info");
const fileCount = document.getElementById("file-count");
const sizeWarning = document.getElementById("size-warning");
const progressContainer = document.getElementById("progress-container");
const submitButton = document.getElementById("submit-button");
const form = document.getElementById("image-convert-form");
const toastContainer = document.getElementById("toast-container");
const formContainer = document.querySelector(".form-container");
const filePreviewDisplay = document.getElementById("file-preview-display");

// Progress indicator
let progressIndicator;

// File preview handler
let filePreviewHandler;

// State
let files = [];
const MAX_FILES = 10;

// Helper Functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showToast(message, type = "success") {
    const toast = document.createElement("div");
    toast.className = `flex items-center p-4 w-full max-w-xs rounded-lg shadow text-white ${
        type === "error" ? "bg-red-600" : "bg-yellow-600"
    } transition-all duration-300`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', type === "error" ? 'assertive' : 'polite');

    toast.innerHTML = `
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${type === "error" ? "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" : "M5 13l4 4L19 7"}"></path>
        </svg>
        <span>${message}</span>
    `;

    // Update screen reader status
    const statusElement = document.getElementById('file-upload-status');
    if (statusElement) {
        statusElement.textContent = `${type === "error" ? "Error" : "Success"}: ${message}`;
    }

    toastContainer.appendChild(toast);
    setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-2");
        setTimeout(() => toast.remove(), 300);
    }, 5000);
}

function updateProgress(percent, message = null) {
    // Initialize progress indicator if it doesn't exist
    if (!progressIndicator) {
        // Clear the container first
        progressContainer.innerHTML = '';

        // Create the progress indicator
        progressIndicator = new ProgressIndicator({
            type: 'linear',
            containerId: 'progress-container',
            initialValue: percent
        });

        // Show the container
        progressContainer.classList.remove("hidden");
    } else {
        // Update existing progress indicator
        progressIndicator.update(percent, message);
    }
}

function resetProgress() {
    if (progressIndicator) {
        progressIndicator.update(0);
    }
    progressContainer.classList.add("hidden");
    progressIndicator = null;
}

function toggleProcessing(processing) {
    submitButton.disabled = processing;
    if (processing) {
        progressContainer.classList.remove("hidden");

        // Initialize progress indicator if starting processing
        if (!progressIndicator) {
            updateProgress(0, "Preparing files...");
        }
    } else {
        setTimeout(() => {
            progressContainer.classList.add("hidden");
            progressIndicator = null;
        }, 1000);
    }
}

// File Handling
function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    highlightDropArea(false);

    const dt = e.dataTransfer;
    const newFiles = Array.from(dt.files).filter(file => file.type.startsWith("image/"));

    if (newFiles.length === 0) {
        showToast("Only image files are accepted", "error");
        return;
    }

    addFiles(newFiles);
}

function handleFiles() {
    const newFiles = Array.from(fileInput.files).filter(file => file.type.startsWith("image/"));
    if (newFiles.length > 0) {
        addFiles(newFiles);
    }
}

function addFiles(newFiles) {
    const remainingSlots = MAX_FILES - files.length;
    if (newFiles.length > remainingSlots) {
        showToast(`You can only add ${remainingSlots} more image${remainingSlots === 1 ? '' : 's'} (max ${MAX_FILES})`, "error");
        newFiles = newFiles.slice(0, remainingSlots);
    }
    files = [...files, ...newFiles];
    updateFileDisplay();
}

function updateFileDisplay() {
    filePreviewContainer.innerHTML = "";
    let totalSize = 0;

    if (files.length === 0) {
        fileCount.textContent = 'No images selected.';
        sizeWarning.classList.add("hidden");
        submitButton.disabled = true;
        return;
    }

    fileCount.textContent = `${files.length} image${files.length > 1 ? 's' : ''} selected`;

    files.forEach((file, index) => {
        totalSize += file.size;
        const filePreview = createFilePreview(file, index);
        filePreviewContainer.appendChild(filePreview);
    });

    if (files.length > 0 && files.length < MAX_FILES) {
        const addMoreBtn = document.createElement("button");
        addMoreBtn.type = "button";
        addMoreBtn.className = "mt-2 inline-flex items-center text-sm text-yellow-400 hover:text-yellow-300 transition duration-300";
        addMoreBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add More Images
        `;
        addMoreBtn.addEventListener("click", addMoreFiles);
        filePreviewContainer.appendChild(addMoreBtn);
    }

    sizeWarning.classList.toggle("hidden", totalSize <= 50 * 1024 * 1024);
    submitButton.disabled = files.length === 0;

    // Update screen reader status
    const statusElement = document.getElementById('file-upload-status');
    if (statusElement) {
        if (files.length === 0) {
            statusElement.textContent = "No images selected";
        } else {
            statusElement.textContent = `${files.length} image${files.length > 1 ? 's' : ''} selected, total size: ${formatFileSize(totalSize)}`;
            if (totalSize > 50 * 1024 * 1024) {
                statusElement.textContent += ' (large file size may affect performance)';
            }
        }
    }
}

function createFilePreview(file, index) {
    const filePreview = document.createElement("div");
    filePreview.className = "flex items-center bg-gray-700 px-3 py-2 rounded-lg mb-2";
    filePreview.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
        </svg>
        <div class="flex-1 min-w-0">
            <p class="text-gray-300 truncate">${file.name}</p>
            <p class="text-gray-500 text-xs">${formatFileSize(file.size)}</p>
        </div>
        <button type="button" class="preview-button ml-2 text-gray-400 hover:text-yellow-300 transition-colors" data-index="${index}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
        </button>
        <button type="button" class="remove-button ml-2 text-gray-400 hover:text-yellow-500 transition-colors" data-index="${index}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
        </button>
    `;

    // Add event listeners
    const previewButton = filePreview.querySelector('.preview-button');
    previewButton.addEventListener('click', () => previewFile(index));

    const removeButton = filePreview.querySelector('.remove-button');
    removeButton.addEventListener('click', () => removeFile(index));

    return filePreview;
}

function removeFile(index) {
    files.splice(index, 1);
    updateFileDisplay();

    // Clear preview if it was showing the removed file
    if (filePreviewDisplay && filePreviewDisplay.getAttribute('data-index') == index) {
        closePreview();
    }
}

function highlightDropArea(highlight) {
    dropArea.classList.toggle("drag-active", highlight);

    // Update screen reader status
    const statusElement = document.getElementById('file-upload-status');
    if (statusElement) {
        statusElement.textContent = highlight ?
            "Images are being dragged over the drop area" :
            files.length > 0 ? `${files.length} image${files.length > 1 ? 's' : ''} selected` : "No images selected";
    }
}

// Form Submission
async function handleSubmit(e) {
    e.preventDefault();

    if (files.length === 0) {
        showToast("Please select at least 1 image to convert", "error");
        return;
    }

    const formData = new FormData(form);
    files.forEach(file => formData.append('files', file));

    toggleProcessing(true);

    // Simulate a more detailed progress sequence
    updateProgress(10, "Preparing files...");

    // Use setTimeout to create a more realistic progress animation
    setTimeout(() => updateProgress(20, "Uploading files..."), 300);
    setTimeout(() => updateProgress(40, "Processing images..."), 600);

    try {
        const response = await fetch(form.action, {
            method: "POST",
            body: formData,
        });

        // Update progress after server response starts
        updateProgress(70, "Receiving converted files...");

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Conversion failed");
        }

        const data = await response.json();

        // Update progress after parsing response
        updateProgress(85, "Finalizing conversion...");

        // Add more robust validation of the response
        if (!data || !Array.isArray(data.files)) {
            throw new Error("Invalid server response format");
        }

        // Filter out any invalid file entries
        const validFiles = data.files.filter(file =>
            file && file.filename && file.download_url
        );

        if (validFiles.length === 0) {
            throw new Error("No valid converted files received");
        }

        // Short delay before showing results for better UX
        setTimeout(() => {
            updateProgress(100, "Conversion complete!");
            showResultsContainer(validFiles);
            showToast("Images converted successfully!", "success");
        }, 300);
    } catch (error) {
        console.error("Conversion error:", error);
        showToast(error.message, "error");
    } finally {
        setTimeout(() => {
            toggleProcessing(false);
            resetProgress();
        }, 1000);
    }
}

function showResultsContainer(convertedFiles) {
    // First, check if there's an existing results container and remove it
    const existingContainer = document.querySelector(".results-container");
    if (existingContainer) {
        existingContainer.remove();
    }

    const resultsContainer = document.createElement("div");
    resultsContainer.className = "result-container w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 mt-6";

    let resultsHTML = `
        <div class="mb-6 text-center">
            <svg class="success-icon mx-auto h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <h3 class="mt-4 text-2xl font-semibold text-white">Conversion Successful!</h3>
            <p class="mt-2 text-gray-400">${convertedFiles.length} file${convertedFiles.length > 1 ? 's' : ''} converted</p>
        </div>
        <div class="space-y-4">
    `;

    // Create a Set to track unique filenames to prevent duplicates
    const uniqueFiles = new Set();

    // Get the output format from the form
    const outputFormat = document.getElementById('output-format').value;
    const quality = document.getElementById('quality').value;

    convertedFiles.forEach((file, index) => {
        // Skip if we've already seen this filename
        if (uniqueFiles.has(file.filename)) {
            console.warn(`Duplicate file skipped: ${file.filename}`);
            return;
        }
        uniqueFiles.add(file.filename);

        // Extract original filename and extension
        const originalFilename = file.original_filename || file.filename.split('_').slice(1).join('_');
        const fileExtension = file.filename.split('.').pop();

        resultsHTML += `
            <div class="file-card p-4 bg-gray-700 rounded-lg success">
                <div class="flex justify-between items-center">
                    <div>
                        <span class="text-gray-200 font-medium">${originalFilename}</span>
                        <div class="text-xs text-gray-400 mt-1">Converted to ${fileExtension.toUpperCase()}</div>
                    </div>
                    <a href="${file.download_url}" id="download-link-${index}"
                       class="download-button flex items-center px-3 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition"
                       download="${file.filename}" data-filename="${file.filename}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download
                    </a>
                </div>
                <div class="file-details mt-3 pt-3 border-t border-gray-600 text-sm">
                    <div class="file-details-row">
                        <span class="file-details-label">Format</span>
                        <span class="file-details-value">${fileExtension.toUpperCase()}</span>
                    </div>
                    <div class="file-details-row">
                        <span class="file-details-label">Quality</span>
                        <span class="file-details-value">${quality}%</span>
                    </div>
                    <div class="file-details-row">
                        <span class="file-details-label">Filename</span>
                        <span class="file-details-value">${file.filename}</span>
                    </div>
                </div>
            </div>
        `;
    });

    resultsHTML += `
        </div>

        <!-- Next Steps Section -->
        <div class="next-steps mt-6 pt-6 border-t border-gray-700">
            <h4 class="next-steps-title text-lg font-semibold text-white mb-4">Next Steps</h4>
            <div class="next-steps-list space-y-4">
                <div class="next-step-item flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="next-step-icon h-6 w-6 text-yellow-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <div>
                        <h5 class="text-white font-medium">Remove Background</h5>
                        <p class="text-gray-400 text-sm">Need transparent backgrounds? Try our background removal tool.</p>
                        <a href="/tools/remove_background" class="text-yellow-500 hover:text-yellow-400 text-sm inline-block mt-1">Remove Background →</a>
                    </div>
                </div>
                <div class="next-step-item flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" class="next-step-icon h-6 w-6 text-yellow-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <div>
                        <h5 class="text-white font-medium">Extract Text</h5>
                        <p class="text-gray-400 text-sm">Need to extract text from your images? Try our text extraction tool.</p>
                        <a href="/tools/extract_text" class="text-yellow-500 hover:text-yellow-400 text-sm inline-block mt-1">Extract Text →</a>
                    </div>
                </div>
            </div>
        </div>

        <button id="new-convert-btn" class="reset-button mt-6 w-full px-4 py-3 bg-yellow-600 text-white font-semibold rounded-lg hover:bg-yellow-700 transition">
            Convert More Images
        </button>
    `;

    resultsContainer.innerHTML = resultsHTML;

    // Clear any existing results before adding new ones
    const oldResults = document.querySelectorAll(".result-container");
    oldResults.forEach(container => container.remove());

    formContainer.insertAdjacentElement("afterend", resultsContainer);
    formContainer.classList.add("hidden");

    // Initialize the result page enhancer
    new ResultPageEnhancer({
        containerSelector: '.result-container',
        toolType: 'image',
        onReset: () => {
            resetForm();
            resultsContainer.remove();
        }
    });
}

function resetForm() {
    form.reset();
    fileInput.value = "";
    files = [];
    filePreviewContainer.innerHTML = "";
    fileCount.textContent = "No images selected.";
    sizeWarning.classList.add("hidden");
    submitButton.disabled = true;
    formContainer.classList.remove("hidden");

    // Update screen reader status
    const statusElement = document.getElementById('file-upload-status');
    if (statusElement) {
        statusElement.textContent = "No images selected";
    }
}

// Event Listeners
function initEventListeners() {
    // Prevent default drag behaviors
    ["dragenter", "dragover", "dragleave", "drop"].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ["dragenter", "dragover"].forEach(eventName => {
        dropArea.addEventListener(eventName, () => highlightDropArea(true));
    });

    // Remove highlight when item leaves or is dropped
    ["dragleave", "drop"].forEach(eventName => {
        dropArea.addEventListener(eventName, () => highlightDropArea(false));
    });

    // Handle file drop
    dropArea.addEventListener("drop", handleDrop);

    // Handle file selection via input
    fileInput.addEventListener("change", handleFiles);

    // Handle form submission
    form.addEventListener("submit", handleSubmit);

    // Make drop area clickable
    dropArea.addEventListener("click", () => fileInput.click());

    // Add keyboard support for drop area
    dropArea.addEventListener("keydown", (e) => {
        if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            fileInput.click();
        }
    });
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function addMoreFiles() {
    fileInput.click();
}

function previewFile(index) {
    const file = files[index];
    if (!file) return;

    // Initialize file preview handler if not already done
    if (!filePreviewHandler && filePreviewDisplay) {
        filePreviewHandler = new FilePreview({
            containerId: 'file-preview-display',
            onPreviewReady: (file, type) => {
                // Show the preview container
                filePreviewDisplay.classList.remove('hidden');
                filePreviewDisplay.setAttribute('data-index', index);

                // Add close button if not already present
                if (!document.getElementById('preview-close-button')) {
                    const closeButton = document.createElement('button');
                    closeButton.id = 'preview-close-button';
                    closeButton.className = 'absolute top-2 right-2 bg-gray-800 bg-opacity-75 rounded-full p-1 text-gray-300 hover:text-white transition-colors';
                    closeButton.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    `;
                    closeButton.addEventListener('click', closePreview);
                    filePreviewDisplay.appendChild(closeButton);
                }
            },
            onError: (message) => {
                showToast(message, 'error');
            }
        });
    }

    // Preview the file
    if (filePreviewHandler) {
        filePreviewHandler.previewFile(file);
    }
}

function closePreview() {
    if (filePreviewDisplay) {
        filePreviewDisplay.classList.add('hidden');
        filePreviewDisplay.removeAttribute('data-index');

        // Clear the preview
        if (filePreviewHandler) {
            filePreviewHandler.clearPreview();
        }
    }
}

// Initialize
document.addEventListener("DOMContentLoaded", () => {
    initEventListeners();
    window.removeFile = removeFile;
});