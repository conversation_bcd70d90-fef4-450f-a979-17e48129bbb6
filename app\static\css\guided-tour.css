/**
 * Guided Tour CSS
 * Styles for the first-time user onboarding tour overlays
 */

/* Overlay that covers the entire page */
.guided-tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9000;
  pointer-events: none;
}

/* Tooltip container */
.guided-tour-tooltip {
  position: absolute;
  z-index: 9001;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 1.5rem;
  max-width: 350px;
  width: calc(100% - 40px);
  pointer-events: auto;
  border: 1px solid var(--border-primary);
  animation: tooltip-fade-in 0.3s ease-out;
}

/* Tooltip title */
.guided-tour-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: var(--text-primary);
}

/* Tooltip content */
.guided-tour-content {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.25rem;
  color: var(--text-secondary);
}

/* Buttons container */
.guided-tour-buttons {
  display: flex;
  justify-content: space-between;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

/* Button styling */
.guided-tour-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  border: 1px solid var(--border-primary);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.guided-tour-button:hover {
  background-color: var(--bg-accent);
  color: white;
}

.guided-tour-button:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

.guided-tour-button:active {
  transform: scale(0.98);
}

/* Next button */
.guided-tour-next {
  background-color: var(--bg-accent);
  color: white;
}

.guided-tour-next:hover {
  opacity: 0.9;
}

/* Skip button */
.guided-tour-skip {
  background-color: transparent;
  color: var(--text-tertiary);
}

.guided-tour-skip:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Step indicator */
.guided-tour-step-indicator {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  text-align: center;
}

/* Highlight effect for target elements */
.guided-tour-highlight {
  position: relative;
  z-index: 9002;
  pointer-events: auto;
  box-shadow: 0 0 0 4px var(--bg-accent), 0 0 0 8px rgba(29, 78, 216, 0.3);
  border-radius: 4px;
  animation: highlight-pulse 2s infinite;
}





/* Animations */
@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 4px var(--bg-accent), 0 0 0 8px rgba(29, 78, 216, 0.3);
  }
  50% {
    box-shadow: 0 0 0 4px var(--bg-accent), 0 0 0 12px rgba(29, 78, 216, 0.2);
  }
  100% {
    box-shadow: 0 0 0 4px var(--bg-accent), 0 0 0 8px rgba(29, 78, 216, 0.3);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .guided-tour-tooltip {
    max-width: calc(100% - 40px);
    width: calc(100% - 40px);
    left: 20px !important;
    right: 20px !important;
    padding: 1.25rem;
  }
  
  .guided-tour-title {
    font-size: 1.125rem;
  }
  
  .guided-tour-content {
    font-size: 0.9375rem;
  }
  
  .guided-tour-buttons {
    flex-wrap: wrap;
  }
  
  .guided-tour-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.9375rem;
  }
  
  .guided-tour-skip {
    order: -1;
    width: 100%;
    margin-bottom: 0.5rem;
  }
  

}

/* Accessibility focus styles */
.guided-tour-tooltip:focus {
  outline: none;
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .guided-tour-highlight {
    outline: 3px solid CanvasText;
    box-shadow: none;
  }
  
  .guided-tour-button {
    border: 1px solid CanvasText;
  }
}
