{% extends "base.html" %}

{% block title %}Data File Converter - Essential Tool for Data Scientists | File Processing Hub{% endblock %}
{% block meta %}
    <meta name="description" content="Convert between data science file formats (CSV, JSON, Parquet, Excel) - transform datasets for analysis with our free online conversion tool">
    <meta name="keywords" content="data conversion, CSV to JSON, JSON to CSV, Excel to Parquet, data format converter, data science tools, dataset conversion, pandas file conversion">
    <meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}
{% block content %}
<style>
    /* Style the dropdown options */
    select option {
        background-color: #1F2937; /* gray-800 */
        color: #F3F4F6; /* gray-100 */
    }

    /* Style the dropdown when opened */
    select:focus option:checked {
        background-color: #047857; /* green-700 */
    }

    /* Style the dropdown hover state */
    select option:hover {
        background-color: #065F46; /* green-800 */
    }
</style>
<div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center px-4 py-8">
    <div class="text-center max-w-xl mb-8 relative">
        <h1 class="text-3xl md:text-4xl font-bold text-white mb-4 inline-block relative">
            File Converter
        </h1>
        <p class="text-gray-400">Convert between data formats, documents, and more.</p>

        {% if is_guest %}
        <div class="mt-2 mb-2 bg-blue-900 text-blue-200 p-3 rounded-lg text-sm">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>You're using the guest mode with limited features. <a href="{{ url_for('auth.register') }}" class="text-blue-300 underline hover:text-blue-100">Register</a> for higher limits.</span>
            </div>
            <div class="mt-2 grid grid-cols-2 gap-2 text-xs">
                <div>
                    <span class="font-semibold">Guest Limits:</span>
                    <ul class="list-disc list-inside pl-2">
                        <li>5 conversions per minute</li>
                        <li>Max file size: 5MB</li>
                    </ul>
                </div>
                <div>
                    <span class="font-semibold">Registered Users:</span>
                    <ul class="list-disc list-inside pl-2">
                        <li>Higher conversion limits</li>
                        <li>Max file size: 16MB</li>
                        <li>Conversion history</li>
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4 text-left">
            <div class="bg-gray-700 p-3 rounded-lg">
                <h3 class="text-green-400 text-lg font-semibold mb-2">Data Formats</h3>
                <ul class="text-gray-300 text-sm space-y-1">
                    <li>• CSV ↔ Excel, JSON, XML, Parquet</li>
                    <li>• Excel ↔ CSV, JSON, XML, Parquet</li>
                </ul>
            </div>
            <div class="bg-gray-700 p-3 rounded-lg">
                <h3 class="text-green-400 text-lg font-semibold mb-2">Document Formats</h3>
                <ul class="text-gray-300 text-sm space-y-1">
                    <li>• PDF ↔ Word (DOCX)</li>
                    <li>• PDF ↔ Excel (XLSX)</li>
                    <li>• Word ↔ PDF</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 form-container">
        <div id="error-container" class="hidden"></div>

        <form id="convert-file-form" method="POST" enctype="multipart/form-data">
            {{ form.hidden_tag() }}

            <!-- Enhanced Drag-and-Drop Area -->
            <div id="drop-area" class="drop-area convert-files" tabindex="0" role="button" aria-label="Drop file here or click to browse">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-green-300 mt-2">Drag & Drop your file here</p>
                <p class="text-gray-400">or</p>
                {{ form.input_file(class="hidden", id="file-input") }}
                <button type="button" onclick="document.getElementById('file-input').click()"
                        class="mt-2 bg-green-600 px-4 py-2 rounded hover:bg-green-500 transition duration-300 text-white"
                        aria-label="Choose file">
                    Choose File
                </button>
                <div class="sr-only" aria-live="polite" id="file-upload-status">No file selected</div>
            </div>

            <!-- File Info -->
            <div id="file-details" class="hidden text-gray-400 text-sm mt-4">
                <div class="flex items-center bg-gray-700 px-3 py-2 rounded-lg mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <div class="flex-1 min-w-0">
                        <p id="file-name" class="text-gray-300 truncate"></p>
                        <p id="file-size" class="text-gray-500 text-xs"></p>
                    </div>
                </div>
                <div id="size-warning" class="hidden text-yellow-400 mt-1">
                    {% if is_guest %}
                    Note: Guest users are limited to 5MB files. <a href="{{ url_for('auth.register') }}" class="underline">Register</a> for larger uploads.
                    {% else %}
                    Note: File size should be under 16MB for best performance
                    {% endif %}
                </div>
            </div>
            <div id="no-file-selected" class="text-gray-400 text-sm mt-4">No file selected.</div>

            <!-- File Preview Display (initially hidden) -->
            <div id="file-preview-display" class="hidden relative mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700"></div>

            <!-- Presets -->
            <div class="mt-4" id="presets-container">
                <!-- Will be populated by JavaScript -->
            </div>

            <!-- Conversion Options - Improved for mobile -->
            <div class="mt-4">
                <div class="form-row">
                    <label for="output_format" class="block text-sm font-medium text-gray-300 mb-1">Output Format</label>
                    {{ form.output_format(class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-green-600 focus:border-transparent",
                                  id="output_format") }}
                    <p class="mt-1 text-xs text-gray-400">Select target format</p>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="submit-button"
                    class="w-full px-4 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                    disabled>
                Convert File
            </button>

            <!-- Progress Bar -->
            <div id="progress-container" class="hidden mt-4">
                <div class="flex justify-between text-sm text-gray-400 mb-1">
                    <span>Processing...</span>
                    <span id="progress-text">0%</span>
                </div>
                <div id="progress-bar" class="h-2 bg-green-600 rounded-full transition-all duration-300" style="width: 0%;"></div>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script src="{{ url_for('static', filename='js/presets.js') }}"></script>
<script src="{{ url_for('static', filename='js/convert_files.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize presets manager
    const presetsManager = new PresetsManager({
        presetType: 'file',
        containerSelector: '#presets-container',
        onPresetSelected: (preset) => {
            console.log('Preset selected:', preset);
        },
        getFormValues: () => {
            return {
                output_format: document.getElementById('output_format').value
            };
        },
        setFormValues: (settings) => {
            if (settings.output_format) {
                document.getElementById('output_format').value = settings.output_format;
            }
        }
    });

    // Add class to body if user is authenticated
    {% if current_user.is_authenticated %}
    document.body.classList.add('user-authenticated');
    {% endif %}
});
{% endblock %}