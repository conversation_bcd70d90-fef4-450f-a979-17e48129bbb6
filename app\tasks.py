import os
import time
import uuid
from typing import Dict, List, Any
from werkzeug.utils import secure_filename
from flask import current_app
from celery import shared_task, states
from celery.exceptions import Ignore
from app.tools.convert_files import FileConverter
from app.tools.progress import ProgressTracker


@shared_task
def delete_file(file_path: str) -> str:
    """Deletes a file after 3 minutes."""
    time.sleep(180)  # Simulating a 3-minute delay
    if os.path.exists(file_path):
        os.remove(file_path)
        return f"Deleted: {file_path}"
    else:
        return f"File not found: {file_path}"


@shared_task(bind=True)
def convert_file_task(self, file_path: str, output_format: str, user_id: int) -> Dict[str, Any]:
    """
    Convert a single file as a Celery task.

    Args:
        self: Celery task instance
        file_path: Path to the input file
        output_format: Desired output format
        user_id: ID of the user who initiated the conversion (for tracking purposes)

    Returns:
        Dict containing conversion result information
    """
    # Log the user_id for auditing purposes
    current_app.logger.info(f"File conversion initiated by user_id: {user_id}")
    try:
        # Extract filename from path
        filename = os.path.basename(file_path)

        # Generate secure output filename with UUID to prevent conflicts
        base_filename = secure_filename(filename.rsplit(".", 1)[0])
        output_filename = f"{uuid.uuid4().hex[:8]}_{base_filename}.{output_format}"
        output_path = os.path.join(current_app.config["OUTPUT_FOLDER"], output_filename)

        # Open the file
        with open(file_path, "rb") as f:
            # Create a FileStorage-like object
            from werkzeug.datastructures import FileStorage

            file_storage = FileStorage(stream=f, filename=filename, content_type="application/octet-stream")

            # Perform conversion
            FileConverter.convert(input_file=file_storage, output_path=output_path, output_format=output_format)

        # Schedule file deletion
        delete_file.apply_async(args=[file_path], countdown=1800)  # Delete input after 30 minutes
        delete_file.apply_async(args=[output_path], countdown=86400)  # Delete output after 24 hours

        return {"success": True, "output_filename": output_filename, "output_path": output_path}
    except Exception as e:
        self.update_state(
            state=states.FAILURE, meta={"exc_type": type(e).__name__, "exc_message": str(e), "file_path": file_path}
        )
        raise Ignore()


@shared_task(bind=True)
def batch_convert_files(self, file_paths: List[str], output_format: str, user_id: int) -> Dict[str, Any]:
    """
    Convert multiple files in batch mode.

    Args:
        self: Celery task instance
        file_paths: List of paths to input files
        output_format: Desired output format for all files
        user_id: ID of the user who initiated the conversion

    Returns:
        Dict containing batch conversion results
    """
    total_files = len(file_paths)
    results = []
    failed = []
    skipped = []

    # Create a progress tracker
    tracker = ProgressTracker(self.request.id)

    # Track progress more granularly with sub-steps
    for i, file_path in enumerate(file_paths):
        try:
            # Check if task has been canceled
            if tracker.is_canceled():
                # Mark remaining files as skipped
                for j in range(i, total_files):
                    skipped_path = file_paths[j]
                    skipped.append({"filename": os.path.basename(skipped_path), "reason": "Task canceled"})

                # Update progress to show cancellation
                tracker.set_progress(
                    progress=100,
                    message=f"Canceled: {len(results)} successful, {len(failed)} failed, {len(skipped)} skipped",
                    status="canceled",
                )

                # Return early with partial results
                return {
                    "task_id": self.request.id,
                    "total": total_files,
                    "successful": len(results),
                    "failed": len(failed),
                    "skipped": len(skipped),
                    "results": results,
                    "failed_files": failed,
                    "skipped_files": skipped,
                    "status": "canceled",
                }

            # Calculate base progress for this file (each file has 4 sub-steps)
            # This ensures smoother progress updates
            base_progress = int((i / total_files) * 100)
            file_progress_step = 100 / total_files / 4  # Each file has 4 progress steps

            # Update file-level progress
            file_info = {"index": i, "filename": os.path.basename(file_path), "status": "processing", "progress": 0}
            tracker.update_file_progress(i, file_info)

            # Step 1: Starting file
            progress = base_progress
            tracker.set_progress(
                progress=progress,
                message=f"Starting file {i+1} of {total_files}: {os.path.basename(file_path)}",
                current_file_index=i,
            )

            # Update task state
            self.update_state(
                state=states.STARTED,
                meta={"current": i, "total": total_files, "progress": progress, "filename": os.path.basename(file_path)},
            )

            # Update file progress
            file_info["progress"] = 25
            tracker.update_file_progress(i, file_info)

            # Step 2: Reading file
            progress = base_progress + file_progress_step
            tracker.set_progress(
                progress=progress,
                message=f"Reading file {i+1} of {total_files}: {os.path.basename(file_path)}",
                current_file_index=i,
            )

            # Update file progress
            file_info["progress"] = 50
            tracker.update_file_progress(i, file_info)

            # Step 3: Converting file
            progress = base_progress + (file_progress_step * 2)
            tracker.set_progress(
                progress=progress,
                message=f"Converting file {i+1} of {total_files}: {os.path.basename(file_path)}",
                current_file_index=i,
            )

            # Convert the file
            result = convert_file_task.apply_async(
                args=[file_path, output_format, user_id], queue="convert"
            ).get()  # Wait for the result

            # Update file progress
            file_info["progress"] = 75
            tracker.update_file_progress(i, file_info)

            # Step 4: Finishing file
            progress = base_progress + (file_progress_step * 3)
            tracker.set_progress(
                progress=progress,
                message=f"Finishing file {i+1} of {total_files}: {os.path.basename(file_path)}",
                current_file_index=i,
            )

            # Update file status to success
            file_info["status"] = "success"
            file_info["progress"] = 100
            file_info["output_filename"] = result["output_filename"]
            tracker.update_file_progress(i, file_info)

            results.append(
                {"filename": os.path.basename(file_path), "output_filename": result["output_filename"], "success": True}
            )

        except Exception as e:
            # Update file status to failed
            file_info = {
                "index": i,
                "filename": os.path.basename(file_path),
                "status": "failed",
                "progress": 100,
                "error": str(e),
            }
            tracker.update_file_progress(i, file_info)

            failed.append({"filename": os.path.basename(file_path), "error": str(e)})

    # Ensure we don't jump to 100% too abruptly
    if total_files > 0:
        final_progress = min(99, int(((total_files - 1) / total_files) * 100) + 3)
        tracker.set_progress(
            progress=final_progress,
            message=f"Finalizing batch: {len(results)} successful, {len(failed)} failed",
            status="finalizing",
        )

        # Short delay before final 100% to make progress visible
        time.sleep(0.5)

    # Final progress update
    status = "completed"
    if len(failed) > 0 and len(results) == 0:
        status = "failed"
    elif len(failed) > 0:
        status = "partial"

    tracker.set_progress(progress=100, message=f"Completed: {len(results)} successful, {len(failed)} failed", status=status)

    return {
        "task_id": self.request.id,
        "total": total_files,
        "successful": len(results),
        "failed": len(failed),
        "skipped": len(skipped),
        "results": results,
        "failed_files": failed,
        "skipped_files": skipped,
        "status": status,
        "original_file_paths": file_paths,  # Include original file paths for retry functionality
    }
