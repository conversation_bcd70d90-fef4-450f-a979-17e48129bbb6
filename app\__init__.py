import os
import logging
from logging.handlers import RotatingFileHand<PERSON>
from flask import Flask, request
from flask_migrate import Migrate
from dotenv import load_dotenv
from .config import Config
from .routes.main import main_bp
from .routes.tools import tools_bp, privacy_bp, terms_bp
from .routes.auth import auth_bp
from .routes.history import history_bp
from .routes.presets import presets_bp
from .routes.feedback import feedback_bp
from .extensions import db, login_manager, limiter
from .config import make_celery
from flask_wtf.csrf import CSRFProtect
from .cache import cache

# Initialize extensions
csrf = CSRFProtect()
load_dotenv()


def create_app(config_class=Config):
    """Factory function to create and configure the Flask application."""
    app = Flask(__name__, instance_relative_config=True)

    # Load configuration with defaults
    app.config.from_object(config_class)
    app.config.setdefault("ENV", "production")
    app.config.setdefault("DEBUG", app.config["ENV"] == "development")

    # Ensure critical directories exist
    required_dirs = [
        app.config["UPLOAD_FOLDER"],
        app.config["OUTPUT_FOLDER"],
        app.instance_path,
        os.path.join(app.root_path, "migrations"),
        app.config.get("LOG_FOLDER", "logs"),
    ]

    for folder in required_dirs:
        try:
            os.makedirs(folder, exist_ok=True)
        except OSError as e:
            app.logger.error(f"Directory creation failed for {folder}: {str(e)}")
            if folder in required_dirs[:3]:  # Critical directories
                raise RuntimeError(f"Critical directory {folder} could not be created")

    # Configure logging
    setup_logging(app)

    # Initialize database
    db.init_app(app)

    # Initialize Flask-Login
    login_manager.init_app(app)

    # Set up user loader
    @login_manager.user_loader
    def load_user(user_id):
        from app.models.auth import User

        return User.query.get(int(user_id))

    # Initialize Migrate only if migrations folder exists or we're in development
    migrations_dir = os.path.join(app.root_path, "migrations")
    if os.path.exists(migrations_dir) or app.config["ENV"] == "development":
        # Initialize migrations
        Migrate(app, db)
        app.logger.info("Database migrations initialized")

        if app.config["ENV"] == "development":
            try:
                with app.app_context():
                    from flask_migrate import upgrade

                    upgrade()
                    app.logger.info("Database migrations applied")
                    init_db_data()  # Initialize default data after migration
            except Exception as e:
                app.logger.warning(f"Migration failed: {str(e)}")
                if not os.path.exists(migrations_dir):
                    app.logger.warning("Migrations directory missing. Run 'flask db init'")
    else:
        app.logger.warning(
            "Migrations system not initialized. For database migrations, run:\n"
            "1. flask db init\n"
            "2. flask db migrate -m 'Initial migration'\n"
            "3. flask db upgrade"
        )

    # Initialize CSRF protection
    csrf.init_app(app)

    # Initialize rate limiter
    limiter.init_app(app)

    # Initialize cache
    cache.init_app(app)
    app.logger.info("Cache initialized")

    # Register blueprints
    blueprints = [
        (main_bp, ""),
        (tools_bp, "/tools"),
        (privacy_bp, "/privacy"),
        (terms_bp, "/terms"),
        (auth_bp, "/auth"),
        (history_bp, "/history"),
        (presets_bp, "/presets"),
        (feedback_bp, "/feedback"),
    ]

    for bp, url_prefix in blueprints:
        app.register_blueprint(bp, url_prefix=url_prefix)
        app.logger.info(f"Registered blueprint: {bp.name} at {url_prefix or '/'}")

    # Initialize Celery
    celery = make_celery(app)
    app.logger.info("Celery initialized")

    # Register CLI commands
    register_cli_commands(app)

    # Register custom URL handler for versioned static files
    from app.utils.performance import versioned_url_for

    @app.context_processor
    def inject_utilities():
        """Inject utility functions into templates."""
        from app.utils.performance import (
            versioned_url_for,
            should_load_module,
            get_critical_css,
            get_non_critical_css,
            get_current_page_modules
        )

        return {
            'versioned_url_for': versioned_url_for,
            'should_load_module': should_load_module,
            'get_critical_css': get_critical_css,
            'get_non_critical_css': get_non_critical_css,
            'get_current_page_modules': get_current_page_modules
        }

    # Security headers middleware
    @app.after_request
    def add_security_headers(response):
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "SAMEORIGIN"

        # Add cache control headers for static files
        if request.endpoint == 'static':
            # Set cache control for static assets
            if any(request.path.endswith(ext) for ext in ['.css', '.js', '.jpg', '.png', '.webp', '.ico']):
                # Cache for 1 week (604800 seconds)
                response.headers['Cache-Control'] = 'public, max-age=604800'

        if app.config["ENV"] == "production":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        return response

    app.logger.info(f"Application initialized in {app.config['ENV']} mode")
    return app, celery


def init_db_data():
    """Initialize default database data"""
    # Initialize default vibes
    from .tools.funzone import Vibe

    default_vibes = ["productive", "tired", "debugging", "procrastinating"]
    for vibe in default_vibes:
        if not Vibe.query.filter_by(name=vibe).first():
            db.session.add(Vibe(name=vibe, count=0))

    # Initialize default roles
    from .models.auth import Role

    default_roles = [
        {"name": "guest", "description": "Limited access for non-registered users"},
        {"name": "registered", "description": "Standard access for registered users"},
        {"name": "admin", "description": "Full access to all features"},
    ]
    for role_data in default_roles:
        if not Role.query.filter_by(name=role_data["name"]).first():
            db.session.add(Role(**role_data))

    db.session.commit()


def register_cli_commands(app):
    """Register custom CLI commands for the application."""
    import click
    from flask.cli import with_appcontext
    from werkzeug.security import generate_password_hash

    @app.cli.command("create-admin")
    @click.option("--username", required=True, help="Admin username")
    @click.option("--email", required=True, help="Admin email")
    @click.option("--password", required=True, help="Admin password")
    @with_appcontext
    def create_admin(username, email, password):
        """Create an admin user."""
        from app.models.auth import User, Role

        # Check if user already exists
        if User.query.filter_by(username=username).first() or User.query.filter_by(email=email).first():
            click.echo("Error: User with that username or email already exists")
            return

        # Get admin role
        admin_role = Role.query.filter_by(name="admin").first()
        if not admin_role:
            click.echo("Error: Admin role not found. Make sure database is initialized.")
            return

        # Create user
        user = User(username=username, email=email, password_hash=generate_password_hash(password), active=True)
        user.roles.append(admin_role)

        # Save to database
        db.session.add(user)
        db.session.commit()

        click.echo(f"Admin user '{username}' created successfully")


def setup_logging(app):
    """Configures structured logging for the application."""
    log_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s " "[%(filename)s:%(lineno)d]")

    # Ensure log directory exists
    log_dir = app.config.get("LOG_FOLDER", "logs")
    os.makedirs(log_dir, exist_ok=True)

    # File handler with rotation
    file_handler = RotatingFileHandler(
        filename=os.path.join(log_dir, "app.log"),
        maxBytes=app.config.get("LOG_MAX_BYTES", 10 * 1024 * 1024),
        backupCount=app.config.get("LOG_BACKUP_COUNT", 5),
        encoding="utf-8",
    )
    file_handler.setFormatter(log_formatter)
    file_handler.setLevel(logging.INFO)

    # Console handler
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(log_formatter)
    stream_handler.setLevel(logging.DEBUG if app.debug else logging.WARNING)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    root_logger.addHandler(file_handler)
    root_logger.addHandler(stream_handler)
    root_logger.setLevel(logging.DEBUG)

    # Configure third-party loggers
    for logger_name in ["werkzeug", "celery", "sqlalchemy"]:
        logging.getLogger(logger_name).setLevel(logging.INFO if app.debug else logging.WARNING)
