"""
Authentication models for the application.
Defines User and Role models for SQLAlchemy.
"""

from datetime import datetime
import secrets
import enum
from typing import List, Optional, Union
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

from app.extensions import db

# Association table for many-to-many relationship between users and roles
user_roles = db.Table(
    "user_roles",
    db.<PERSON>umn("user_id", db.Integer, db.<PERSON>ey("user.id"), primary_key=True),
    db.<PERSON>umn("role_id", db.Inte<PERSON>, db.<PERSON><PERSON>("role.id"), primary_key=True),
)


class UserStatus(enum.Enum):
    """Enum for user account status."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


class Role(db.Model):
    """Role model for user permissions."""

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(255))

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    users = db.relationship("User", secondary=user_roles, back_populates="roles")

    def __repr__(self) -> str:
        return f"<Role {self.name}>"


class User(UserMixin, db.Model):
    """User model for authentication and authorization."""

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    first_name = db.Column(db.String(64))
    last_name = db.Column(db.String(64))

    # Account status
    status = db.Column(db.Enum(UserStatus), default=UserStatus.PENDING)
    email_verified = db.Column(db.Boolean, default=False)

    # Security
    api_key = db.Column(db.String(64), unique=True, index=True)
    last_login = db.Column(db.DateTime)
    login_count = db.Column(db.Integer, default=0)
    remember_token = db.Column(db.String(128))

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    roles = db.relationship("Role", secondary=user_roles, back_populates="users")

    # Flask-Login properties
    @property
    def is_active(self) -> bool:
        """Return True if user is active."""
        return self.status == UserStatus.ACTIVE

    @property
    def is_authenticated(self) -> bool:
        """Return True if user is authenticated."""
        return True

    @property
    def is_anonymous(self) -> bool:
        """Return False as anonymous users aren't supported."""
        return False

    def get_id(self) -> str:
        """Return the user ID as a unicode string."""
        return str(self.id)

    def __repr__(self) -> str:
        return f"<User {self.username}>"

    @property
    def password(self) -> None:
        """Prevent password from being accessed."""
        raise AttributeError("password is not a readable attribute")

    @password.setter
    def password(self, password: str) -> None:
        """Set password hash."""
        self.password_hash = generate_password_hash(password)

    def verify_password(self, password: str) -> bool:
        """Check if password matches the hash."""
        return check_password_hash(self.password_hash, password)

    def generate_api_key(self) -> str:
        """Generate a new API key for the user."""
        self.api_key = secrets.token_hex(32)
        return self.api_key

    def has_role(self, role_name: str) -> bool:
        """Check if user has a specific role."""
        return any(role.name == role_name for role in self.roles)

    def add_role(self, role: Union[Role, str]) -> None:
        """Add a role to the user."""
        if isinstance(role, str):
            role_obj = Role.query.filter_by(name=role).first()
            if not role_obj:
                role_obj = Role(name=role)
                db.session.add(role_obj)
        else:
            role_obj = role

        if role_obj not in self.roles:
            self.roles.append(role_obj)

    def remove_role(self, role: Union[Role, str]) -> None:
        """Remove a role from the user."""
        if isinstance(role, str):
            role_obj = Role.query.filter_by(name=role).first()
            if not role_obj:
                return
        else:
            role_obj = role

        if role_obj in self.roles:
            self.roles.remove(role_obj)

    @classmethod
    def create_user(cls, username: str, email: str, password: str, roles: Optional[List[str]] = None) -> "User":
        """Create a new user with specified roles."""
        user = cls(username=username, email=email)
        user.password = password
        user.generate_api_key()

        if roles:
            for role_name in roles:
                user.add_role(role_name)

        db.session.add(user)
        db.session.commit()
        return user
