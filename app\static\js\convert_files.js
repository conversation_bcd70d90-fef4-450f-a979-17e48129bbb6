class FileConverterUI {
    constructor() {
        this.elements = {
            dropArea: document.getElementById("drop-area"),
            fileInput: document.getElementById("file-input"),
            fileDetails: document.getElementById("file-details"),
            noFileSelected: document.getElementById("no-file-selected"),
            fileName: document.getElementById("file-name"),
            fileSize: document.getElementById("file-size"),
            sizeWarning: document.getElementById("size-warning"),
            outputFormat: document.getElementById("output_format"),
            submitButton: document.getElementById("submit-button"),
            form: document.getElementById("convert-file-form"),
            progressContainer: document.getElementById("progress-container"),
            progressBar: document.getElementById("progress-bar"),
            progressText: document.getElementById("progress-text"),
            resultsContainer: document.getElementById("results-container"),
            errorContainer: document.getElementById("error-container"),
            formContainer: document.querySelector('.form-container'),
            filePreviewDisplay: document.getElementById("file-preview-display")
        };

        // File preview handler
        this.filePreviewHandler = null;

        // Check if user is a guest by looking for the guest info banner
        this.isGuest = document.querySelector('.bg-blue-900.text-blue-200') !== null;

        // Set file size limit based on user type
        this.MAX_FILE_SIZE_MB = this.isGuest ? 5 : 16;
        this.selectedFile = null;
        this.supportedFormats = ['csv', 'xlsx', 'json', 'parquet', 'xml', 'pdf', 'docx'];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.populateOutputFormats();
    }

    setupEventListeners() {
        ["dragenter", "dragover", "dragleave", "drop"].forEach(eventName => {
            this.elements.dropArea.addEventListener(eventName, this.preventDefaults.bind(this));
        });

        ["dragenter", "dragover"].forEach(eventName => {
            this.elements.dropArea.addEventListener(eventName, () => this.highlightDropArea(true));
        });

        ["dragleave", "drop"].forEach(eventName => {
            this.elements.dropArea.addEventListener(eventName, () => this.highlightDropArea(false));
        });

        this.elements.dropArea.addEventListener("drop", this.handleDrop.bind(this));
        this.elements.fileInput.addEventListener("change", this.handleFileSelect.bind(this));
        this.elements.form.addEventListener("submit", this.handleSubmit.bind(this));
    }

    populateOutputFormats() {
        const select = this.elements.outputFormat;
        select.innerHTML = '';

        // Add custom styling to the select element
        select.className = 'w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-green-600 focus:border-transparent appearance-none';

        this.supportedFormats.forEach(format => {
            const option = document.createElement('option');
            option.value = format;
            option.textContent = format.toUpperCase();

            // Add custom styling to each option
            option.style.backgroundColor = '#1F2937'; // bg-gray-800
            option.style.color = '#F3F4F6'; // text-gray-100
            option.style.padding = '0.5rem 1rem';

            // Change hover color (works in some browsers)
            option.addEventListener('mouseover', () => {
                option.style.backgroundColor = '#065F46'; // green-800
            });
            option.addEventListener('mouseout', () => {
                option.style.backgroundColor = '#1F2937'; // bg-gray-800
            });

            select.appendChild(option);
        });

        // Add custom dropdown arrow
        const dropdownArrow = document.createElement('div');
        dropdownArrow.className = 'pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400';
        dropdownArrow.innerHTML = `
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
        `;

        // Wrap select in a relative div if not already done
        if (!select.parentElement.classList.contains('relative')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'relative';
            select.parentNode.insertBefore(wrapper, select);
            wrapper.appendChild(select);
            wrapper.appendChild(dropdownArrow);
        }
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlightDropArea(highlight) {
        this.elements.dropArea.classList.toggle("border-green-500", highlight);
        this.elements.dropArea.classList.toggle("bg-gray-700", highlight);
    }

    handleDrop(e) {
        const dt = e.dataTransfer;
        if (dt.files.length > 0) {
            this.elements.fileInput.files = dt.files;
            this.handleFileSelect();
        }
    }

    handleFileSelect() {
        if (this.elements.fileInput.files.length > 0) {
            const file = this.elements.fileInput.files[0];
            this.selectedFile = file;

            if (file.size > this.MAX_FILE_SIZE_MB * 1024 * 1024) {
                this.showError(`File size exceeds ${this.MAX_FILE_SIZE_MB}MB limit`);
                this.resetFileInput();
                return;
            }

            this.elements.fileDetails.classList.remove("hidden");
            this.elements.noFileSelected.classList.add("hidden");
            this.elements.fileName.textContent = file.name;
            this.elements.fileSize.textContent = this.formatFileSize(file.size);

            // Show size warning if file is approaching the limit (80% of max)
            const warningThreshold = this.MAX_FILE_SIZE_MB * 0.8 * 1024 * 1024;
            this.elements.sizeWarning.classList.toggle("hidden", file.size <= warningThreshold);

            this.elements.submitButton.disabled = false;

            // Update available output formats based on input file type
            this.updateOutputFormats(file);

            // Add preview and delete buttons
            this.addFileButtons();

            // Initialize and show file preview
            this.initializeFilePreview(file);
        } else {
            this.resetFileInput();
        }
    }

    updateOutputFormats(file) {
        const fileExt = file.name.split('.').pop().toLowerCase();
        const select = this.elements.outputFormat;
        select.innerHTML = '';

        // Define available output formats based on input file type
        let availableFormats = [];

        if (fileExt === 'csv') {
            availableFormats = ['xlsx', 'json', 'xml', 'parquet', 'pdf'];
        } else if (fileExt === 'xlsx') {
            availableFormats = ['csv', 'json', 'xml', 'parquet', 'pdf'];
        } else if (fileExt === 'pdf') {
            availableFormats = ['docx', 'xlsx'];
        } else if (fileExt === 'docx') {
            availableFormats = ['pdf'];
        } else if (['json', 'xml', 'parquet'].includes(fileExt)) {
            availableFormats = ['csv', 'xlsx'];
        } else {
            // Default to all formats except the current one
            availableFormats = this.supportedFormats.filter(fmt => fmt !== fileExt);
        }

        // Add options to select
        availableFormats.forEach(format => {
            const option = document.createElement('option');
            option.value = format;

            // Use more descriptive labels
            switch(format) {
                case 'csv': option.textContent = 'CSV'; break;
                case 'xlsx': option.textContent = 'Excel (XLSX)'; break;
                case 'json': option.textContent = 'JSON'; break;
                case 'xml': option.textContent = 'XML'; break;
                case 'parquet': option.textContent = 'Parquet'; break;
                case 'pdf': option.textContent = 'PDF'; break;
                case 'docx': option.textContent = 'Word (DOCX)'; break;
                default: option.textContent = format.toUpperCase();
            }

            // Add custom styling to each option
            option.style.backgroundColor = '#1F2937'; // bg-gray-800
            option.style.color = '#F3F4F6'; // text-gray-100
            option.style.padding = '0.5rem 1rem';

            select.appendChild(option);
        });
    }

    addFileButtons() {
        // Remove existing buttons
        const existingButtons = this.elements.fileDetails.querySelectorAll('.file-action-button');
        existingButtons.forEach(button => button.remove());

        // Create button container if it doesn't exist
        let buttonContainer = this.elements.fileDetails.querySelector('.file-buttons');
        if (!buttonContainer) {
            buttonContainer = document.createElement('div');
            buttonContainer.className = 'file-buttons flex mt-2 space-x-2';
            this.elements.fileDetails.appendChild(buttonContainer);
        } else {
            buttonContainer.innerHTML = '';
        }

        // Add preview button
        const previewButton = document.createElement('button');
        previewButton.textContent = 'Preview File';
        previewButton.className = 'file-action-button preview-button px-4 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center';
        previewButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            Preview
        `;
        previewButton.addEventListener('click', () => this.toggleFilePreview());
        buttonContainer.appendChild(previewButton);

        // Add delete button
        const deleteButton = document.createElement('button');
        deleteButton.className = 'file-action-button delete-button px-4 py-1 bg-red-600 text-white rounded hover:bg-red-700 flex items-center';
        deleteButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
        `;
        deleteButton.addEventListener('click', () => this.resetFileInput());
        buttonContainer.appendChild(deleteButton);
    }

    resetFileInput() {
        this.elements.fileInput.value = '';
        this.selectedFile = null;
        this.elements.fileDetails.classList.add("hidden");
        this.elements.noFileSelected.classList.remove("hidden");
        this.elements.sizeWarning.classList.add("hidden");
        this.elements.submitButton.disabled = true;

        // Hide and clear preview
        this.closeFilePreview();

        // Remove action buttons
        const buttons = this.elements.fileDetails.querySelectorAll('.file-action-button');
        buttons.forEach(button => button.remove());
    }

    initializeFilePreview(file) {
        // Initialize file preview handler if not already done
        if (!this.filePreviewHandler && this.elements.filePreviewDisplay) {
            this.filePreviewHandler = new FilePreview({
                containerId: 'file-preview-display',
                fileInputId: 'file-input',
                onPreviewReady: (file, type) => {
                    // Add close button if not already present
                    if (!document.getElementById('preview-close-button')) {
                        const closeButton = document.createElement('button');
                        closeButton.id = 'preview-close-button';
                        closeButton.className = 'absolute top-2 right-2 bg-gray-800 bg-opacity-75 rounded-full p-1 text-gray-300 hover:text-white transition-colors';
                        closeButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        `;
                        closeButton.addEventListener('click', () => this.closeFilePreview());
                        this.elements.filePreviewDisplay.appendChild(closeButton);
                    }
                },
                onError: (message) => {
                    this.showError(message);
                }
            });
        }

        // Preview the file
        if (this.filePreviewHandler && file) {
            this.filePreviewHandler.previewFile(file);
        }
    }

    toggleFilePreview() {
        if (!this.selectedFile) return;

        if (this.elements.filePreviewDisplay.classList.contains('hidden')) {
            // Show preview
            this.elements.filePreviewDisplay.classList.remove('hidden');
            this.initializeFilePreview(this.selectedFile);
        } else {
            // Hide preview
            this.closeFilePreview();
        }
    }

    closeFilePreview() {
        if (this.elements.filePreviewDisplay) {
            this.elements.filePreviewDisplay.classList.add('hidden');

            // Clear the preview
            if (this.filePreviewHandler) {
                this.filePreviewHandler.clearPreview();
            }
        }
    }

    resetForm() {
        this.resetFileInput();
        this.elements.formContainer.classList.remove('hidden');
        if (this.elements.resultsContainer) {
            this.elements.resultsContainer.remove();
            this.elements.resultsContainer = null;
        }
        this.clearErrors();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateProgress(percent) {
        this.elements.progressBar.style.width = `${percent}%`;
        this.elements.progressText.textContent = `${percent}%`;
    }

    showError(message) {
        if (!this.elements.errorContainer) {
            this.elements.errorContainer = document.createElement('div');
            this.elements.errorContainer.id = 'error-container';
            this.elements.errorContainer.className = 'mt-6';
            this.elements.errorContainer.innerHTML = `
                <h3 class="text-xl font-semibold text-red-400">Error</h3>
                <div class="mt-2 p-4 bg-red-900/30 rounded-md text-red-300">
                    ${message}
                </div>
            `;
            this.elements.form.parentNode.insertBefore(this.elements.errorContainer, this.elements.form.nextSibling);
        } else {
            const errorDiv = this.elements.errorContainer.querySelector('div');
            errorDiv.textContent = message;
        }
    }

    clearErrors() {
        if (this.elements.errorContainer) {
            this.elements.errorContainer.remove();
            this.elements.errorContainer = null;
        }
    }

    async handleSubmit(e) {
        e.preventDefault();
        this.clearErrors();

        if (!this.selectedFile) {
            this.showError("Please select a file to convert");
            return;
        }

        const outputFormat = this.elements.outputFormat.value;
        if (!outputFormat) {
            this.showError("Please select an output format");
            return;
        }

        const formData = new FormData(this.elements.form);
        this.setLoadingState(true);

        try {
            this.updateProgress(20);

            const response = await fetch(this.elements.form.action, {
                method: "POST",
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || "Server error occurred");
            }

            this.handleSuccessResponse(result);
        } catch (error) {
            console.error("Error:", error);
            this.showError(error.message);
            this.setLoadingState(false);
        }
    }

    setLoadingState(isLoading) {
        if (isLoading) {
            this.elements.submitButton.disabled = true;
            this.elements.submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Converting...
            `;
            this.elements.progressContainer.classList.remove("hidden");
        } else {
            this.elements.submitButton.disabled = false;
            this.elements.submitButton.textContent = "Convert File";
            this.elements.progressContainer.classList.add("hidden");
            this.updateProgress(0);
        }
    }

    handleSuccessResponse(result) {
        this.updateProgress(100);

        if (result.download_url) {
            this.elements.formContainer.classList.add('hidden');
            this.showResultsContainer(result.download_url, result.output_filename);
        }

        setTimeout(() => {
            this.setLoadingState(false);
        }, 1000);
    }

    showResultsContainer(url, filename) {
        // Remove existing results container if present
        if (this.elements.resultsContainer) {
            this.elements.resultsContainer.remove();
        }

        // Create new results container
        this.elements.resultsContainer = document.createElement('div');
        this.elements.resultsContainer.id = 'results-container';
        this.elements.resultsContainer.className = 'result-container w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6';

        // Insert after form container
        this.elements.formContainer.insertAdjacentElement('afterend', this.elements.resultsContainer);

        // Get the original filename and output format
        const originalFilename = this.selectedFile ? this.selectedFile.name : 'file';
        const outputFormat = this.elements.outputFormat.value.toUpperCase();

        // Set the content
        this.elements.resultsContainer.innerHTML = `
            <div class="mb-6 text-center">
                <svg class="success-icon mx-auto h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-4 text-2xl font-semibold text-white">Conversion Successful!</h3>
                <p class="mt-2 text-gray-400">Your file has been successfully converted to ${outputFormat} format.</p>
            </div>

            <div class="file-card success p-4 bg-gray-700 rounded-lg">
                <div class="flex justify-between items-center">
                    <div>
                        <span class="text-gray-200 font-medium">${filename || 'converted_file'}</span>
                        <div class="text-xs text-gray-400 mt-1">Ready to download</div>
                    </div>
                    <a href="${url}" class="download-button flex items-center px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
                       download="${filename || 'converted_file'}" data-filename="${filename || 'converted_file'}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download File
                    </a>
                </div>
                <div class="file-details">
                    <div class="file-details-row">
                        <span class="file-details-label">Original File</span>
                        <span class="file-details-value">${originalFilename}</span>
                    </div>
                    <div class="file-details-row">
                        <span class="file-details-label">Converted Format</span>
                        <span class="file-details-value">${outputFormat}</span>
                    </div>
                    <div class="file-details-row">
                        <span class="file-details-label">Output Filename</span>
                        <span class="file-details-value">${filename || 'converted_file'}</span>
                    </div>
                </div>
            </div>

            <!-- Next Steps Section -->
            <div class="next-steps mt-6 pt-6 border-t border-gray-700">
                <h4 class="next-steps-title text-lg font-semibold text-white mb-4">Next Steps</h4>
                <div class="next-steps-list space-y-4">
                    <div class="next-step-item flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="next-step-icon h-6 w-6 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        <div>
                            <h5 class="text-white font-medium">Batch Conversion</h5>
                            <p class="text-gray-400 text-sm">Need to convert multiple files at once? Try our batch conversion tool.</p>
                            <a href="/tools/batch_convert" class="text-green-500 hover:text-green-400 text-sm inline-block mt-1">Batch Convert →</a>
                        </div>
                    </div>
                    <div class="next-step-item flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="next-step-icon h-6 w-6 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                        </svg>
                        <div>
                            <h5 class="text-white font-medium">Share Your Conversion</h5>
                            <p class="text-gray-400 text-sm">Download your file and share it with others.</p>
                        </div>
                    </div>
                </div>
            </div>

            <button id="new-convert-btn" class="reset-button mt-6 w-full px-4 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition">
                Convert Another File
            </button>
        `;

        // Initialize the result page enhancer
        new ResultPageEnhancer({
            containerSelector: '#results-container',
            toolType: 'file',
            onReset: () => this.resetForm()
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
    new FileConverterUI();
});