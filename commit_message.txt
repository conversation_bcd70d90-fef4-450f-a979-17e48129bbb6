feat(ac): Add file preview for supported types (PDF, images, text)

- Created a comprehensive file preview component with support for multiple file types
- Implemented a JavaScript utility class for handling file previews
- Added PDF preview with embedded viewer and toolbar
- Added image preview with responsive sizing and zoom capabilities
- Added text file preview with syntax highlighting and truncation for large files
- Added placeholder previews for unsupported file types
- Enhanced image converter with file preview functionality
- Enhanced file converter with file preview functionality
- Enhanced text extraction with file preview functionality
- Updated Task.md to mark the task as completed

>>>NEXT-BEGIN<<<
RemainingTasks: 7
Focus: Build conversion history: temporary storage of recent jobs per user/guest
>>>NEXT-END<<<
