from app import create_app
from app.extensions import db
from app.models.auth import Role, User, UserStatus

app, _ = create_app()

with app.app_context():
    # Create database tables
    db.create_all()

    # Create default roles
    roles = [
        {"name": "guest", "description": "Limited access for non-registered users"},
        {"name": "registered", "description": "Standard access for registered users"},
        {"name": "admin", "description": "Full access to all features"},
    ]

    for role_data in roles:
        if not Role.query.filter_by(name=role_data["name"]).first():
            db.session.add(Role(**role_data))

    # Create admin user for testing
    if not User.query.filter_by(username="admin").first():
        admin = User(username="admin", email="<EMAIL>", status=UserStatus.ACTIVE)
        admin.password = "adminpassword"
        admin.generate_api_key()
        admin.add_role("admin")
        db.session.add(admin)

    db.session.commit()
    print("Database initialized successfully with default roles and admin user")
