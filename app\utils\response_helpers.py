"""
Response helper utilities for the application.
"""

import hashlib
import json
from typing import Any
from flask import Response


def generate_etag(data: Any) -> str:
    """
    Generate an ETag for the given data.

    Args:
        data: The data to generate an ETag for

    Returns:
        A string containing the ETag
    """
    # Convert data to JSON string and encode to bytes
    json_str = json.dumps(data, sort_keys=True)
    # Generate MD5 hash of the JSON string
    etag = hashlib.md5(json_str.encode()).hexdigest()
    return f'"{etag}"'


def add_cache_headers(response: Response, max_age: int = 300) -> Response:
    """
    Add cache control headers to a response.

    Args:
        response: The Flask response object
        max_age: The maximum age of the cache in seconds (default: 5 minutes)

    Returns:
        The modified response object
    """
    response.headers["Cache-Control"] = f"max-age={max_age}, public"
    return response


def add_etag_header(response: Response, data: Any) -> Response:
    """
    Add an ETag header to a response.

    Args:
        response: The Flask response object
        data: The data to generate an ETag for

    Returns:
        The modified response object
    """
    response.headers["ETag"] = generate_etag(data)
    return response
