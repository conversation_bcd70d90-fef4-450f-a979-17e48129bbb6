/**
 * Presets CSS
 * Styles for the presets functionality
 */

/* Presets Container */
.presets-wrapper {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: rgba(55, 65, 81, 0.5);
    border-radius: 0.5rem;
    border: 1px solid rgba(75, 85, 99, 0.5);
}

/* Preset Select Dropdown */
#preset-select {
    background-color: #374151;
    color: #e5e7eb;
    border: 1px solid #4b5563;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    width: 100%;
    transition: all 0.2s ease-in-out;
}

#preset-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 211, 153, 0.5);
    border-color: #34d399;
}

#preset-select option {
    background-color: #1f2937;
    color: #e5e7eb;
}

/* Preset Action Buttons */
#save-preset-btn,
#delete-preset-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
}

#save-preset-btn {
    background-color: #059669;
    color: white;
}

#save-preset-btn:hover {
    background-color: #047857;
}

#delete-preset-btn {
    background-color: #dc2626;
    color: white;
}

#delete-preset-btn:hover {
    background-color: #b91c1c;
}

#save-preset-btn:focus,
#delete-preset-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* Preset Dialog */
.preset-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.preset-dialog-content {
    background-color: #1f2937;
    border-radius: 0.5rem;
    padding: 1.5rem;
    width: 100%;
    max-width: 28rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.preset-dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.preset-dialog-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #e5e7eb;
}

.preset-dialog-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
}

.preset-dialog-close:hover {
    color: #e5e7eb;
}

.preset-dialog-body {
    margin-bottom: 1.5rem;
}

.preset-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

.preset-dialog-button {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.preset-dialog-button-cancel {
    background-color: #4b5563;
    color: white;
}

.preset-dialog-button-cancel:hover {
    background-color: #374151;
}

.preset-dialog-button-save {
    background-color: #059669;
    color: white;
}

.preset-dialog-button-save:hover {
    background-color: #047857;
}

/* Status Badge */
.status-badge {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.success {
    background-color: rgba(5, 150, 105, 0.2);
    color: #34d399;
}

.status-badge.error {
    background-color: rgba(220, 38, 38, 0.2);
    color: #f87171;
}

.status-badge.processing {
    background-color: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
}

.status-badge.canceled {
    background-color: rgba(107, 114, 128, 0.2);
    color: #9ca3af;
}
