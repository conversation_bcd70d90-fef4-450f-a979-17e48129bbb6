from app import create_app
from app.extensions import db
from app.models.auth import Role, User, UserStatus
import os
import traceback
import re

app, _ = create_app()

with app.test_client() as client:
    # Test the registration page
    response = client.get("/auth/register")
    print(f"GET /auth/register status code: {response.status_code}")

    # Extract CSRF token from the response
    csrf_token = None
    html = response.data.decode("utf-8")
    csrf_match = re.search(r'<input[^>]*name="csrf_token"[^>]*value="([^"]*)"', html)
    if csrf_match:
        csrf_token = csrf_match.group(1)
        print(f"Found CSRF token: {csrf_token[:10]}...")
    else:
        print("CSRF token not found in the response")

    # Test the registration form submission
    with app.app_context():
        # Make sure the registered role exists
        if not Role.query.filter_by(name="registered").first():
            role = Role(name="registered", description="Standard access for registered users")
            db.session.add(role)
            db.session.commit()
            print("Created 'registered' role")

        # Delete test user if it exists
        test_user = User.query.filter_by(username="testuser").first()
        if test_user:
            db.session.delete(test_user)
            db.session.commit()
            print("Deleted existing test user")

    # Submit the registration form with CSRF token
    form_data = {"username": "testuser", "email": "<EMAIL>", "password": "password123", "password2": "password123"}

    if csrf_token:
        form_data["csrf_token"] = csrf_token

    response = client.post("/auth/register", data=form_data, follow_redirects=True)

    print(f"POST /auth/register status code: {response.status_code}")
    print(f"Response data: {response.data.decode('utf-8')[:200]}...")

    # Check if the user was created
    with app.app_context():
        user = User.query.filter_by(username="testuser").first()
        if user:
            print(f"User created: {user.username}, {user.email}")
            print(f"User has roles: {[role.name for role in user.roles]}")
        else:
            print("User was not created")
