{% extends "base.html" %}

{% block title %}Split PDF - File Processing Hub{% endblock %}

{% block content %}
<!-- Add these in your head section -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
<script>
  // Initialize PDF.js worker
  pdfjsLib = pdfjsLib || {};
  pdfjsLib.GlobalWorkerOptions = {
    workerSrc: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js'
  };
</script>
<div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center px-4 py-8">
    <div class="text-center max-w-xl mb-8 relative">
        <h1 class="text-3xl md:text-4xl font-bold text-white mb-4 inline-block relative">
            Split PDF Files
        </h1>
        <p class="text-gray-400">Extract pages from PDF documents into a new file.</p>
    </div>

    <!-- Upload Form -->
    <div class="w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 form-container">
        <div id="error-container" class="hidden" aria-live="assertive"></div>

        <form id="split-pdf-form" method="POST" enctype="multipart/form-data">
            {{ form.hidden_tag() }}

            <!-- Enhanced Drag-and-Drop Area -->
            <div id="drop-area" class="drop-area pdf-split" tabindex="0" role="button" aria-label="Drop PDF file here or click to browse">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-blue-300 mt-2">Drag & Drop your PDF file here</p>
                <p class="text-gray-400">or</p>
                {{ form.pdf_file(class="hidden", id="file-input", accept=".pdf") }}
                <button type="button" onclick="document.getElementById('file-input').click()"
                        class="mt-2 bg-blue-600 px-4 py-2 rounded hover:bg-blue-500 transition duration-300 text-white"
                        aria-label="Choose PDF">
                    Choose PDF File
                </button>
                <div class="sr-only" aria-live="polite" id="file-upload-status">No file selected</div>
            </div>

            <!-- File Info -->
            <div id="file-details" class="hidden text-gray-400 text-sm mt-4">
                <div class="flex items-center bg-gray-700 px-3 py-2 rounded-lg mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <div class="flex-1 min-w-0">
                        <p id="file-name" class="text-gray-300 truncate"></p>
                        <p id="file-size" class="text-gray-500 text-xs"></p>
                    </div>
                </div>
                <div id="page-count" class="text-blue-400 text-xs"></div>
                <div id="size-warning" class="hidden text-yellow-400 mt-1">
                    Note: File size should be under 10MB for best performance
                </div>
            </div>
            <div id="no-file-selected" class="text-gray-400 text-sm mt-4">No PDF file selected.</div>

            <!-- Split Options - Improved for mobile -->
            <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4 split-options-grid">
                <div class="form-row">
                    <label for="pages" class="block text-sm font-medium text-gray-300 mb-1">Pages to Extract</label>
                    {{ form.pages(class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent",
                                  placeholder="e.g., 1,3-5 or 1-5", id="pages") }}
                    <p class="mt-1 text-xs text-gray-400">Use commas (1,3,5) or hyphens (2-4)</p>
                </div>
                <div class="form-row">
                    <label for="output-filename" class="block text-sm font-medium text-gray-300 mb-1">Output Filename</label>
                    {{ form.output_filename(class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent",
                                          value="split.pdf", id="output-filename") }}
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="submit-button"
                    class="w-full px-4 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                    disabled>
                Split PDF
            </button>

            <!-- Progress Bar -->
            <div id="progress-container" class="hidden mt-4">
                <div class="flex justify-between text-sm text-gray-400 mb-1">
                    <span>Processing...</span>
                    <span id="progress-text">0%</span>
                </div>
                <div id="progress-bar" class="h-2 bg-blue-600 rounded-full transition-all duration-300" style="width: 0%;"></div>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script src="{{ url_for('static', filename='js/split_pdf.js') }}"></script>
{% endblock %}