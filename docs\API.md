# ImgCipherF1 API Documentation

This document provides detailed information about the API endpoints available in the ImgCipherF1 application.

## Authentication

Most API endpoints require authentication. The application uses token-based authentication.

To authenticate:

1. Log in via the web interface
2. Use the session cookie for subsequent requests

## Common Response Format

All API responses follow a standard format:

```json
{
  "success": true|false,
  "data": { ... },  // For successful responses
  "error": {        // For error responses
    "message": "Error description",
    "code": 400,
    "details": { ... }  // Optional additional error details
  }
}
```

## API Endpoints

## Authentication API

### POST /auth/login

Log in to the application.

**Request Body:**

```json
{
  "username": "user123",
  "password": "securepassword",
  "remember_me": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Login successful",
  "redirect": "/"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Invalid username or password",
    "code": 401
  }
}
```

### POST /auth/register

Register a new user account.

**Request Body:**

```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "securepassword",
  "password2": "securepassword"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Registration successful! You can now log in.",
  "redirect": "/auth/login"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Username already taken",
    "code": 400,
    "details": {
      "username": ["Username already taken. Please choose a different one."]
    }
  }
}
```

### GET /auth/logout

Log out the current user.

**Response:**

```json
{
  "success": true,
  "message": "You have been logged out",
  "redirect": "/"
}
```

### POST /auth/reset-password-request

Request a password reset link.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Check your email for instructions to reset your password"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "No account found with that email address",
    "code": 404
  }
}
```

### POST /auth/reset-password/{token}

Reset password using a token.

**Request Body:**

```json
{
  "password": "newpassword",
  "password2": "newpassword"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Your password has been reset",
  "redirect": "/auth/login"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Invalid or expired token",
    "code": 400
  }
}
```

## Image Conversion API

### POST /tools/convert

Convert one or more images to a different format.

**Request Body (multipart/form-data):**

- `files`: One or more image files (max 10)
- `format`: Output format (webp, jpg, png, gif, ico)
- `quality`: Quality setting for lossy formats (0-100)

**Response:**

```json
{
  "success": true,
  "files": [
    {
      "filename": "abc123_image.webp",
      "download_url": "https://example.com/download/abc123_image.webp"
    }
  ]
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Maximum 10 files allowed",
    "code": 400
  }
}
```

### POST /tools/convert_file

Convert a file to a different format. Rate limited for guests.

**Request Body (multipart/form-data):**

- `file`: The file to convert
- `output_format`: Desired output format (csv, xlsx, json, xml, pdf, docx)

**Response:**

```json
{
  "success": true,
  "download_url": "https://example.com/download/abc123_document.pdf",
  "output_filename": "abc123_document.pdf"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Unsupported file format",
    "code": 400
  }
}
```

### POST /tools/batch_convert

Convert multiple files in batch mode (registered users only).

**Request Body (multipart/form-data):**

- `files`: Multiple files to convert (max 20)
- `output_format`: Desired output format for all files

**Response:**

```json
{
  "success": true,
  "task_id": "abc123def456",
  "redirect": "/tools/batch_progress/abc123def456"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Maximum 20 files allowed per batch",
    "code": 400
  }
}
```

## Task Management API

### GET /tools/api/task_progress/{task_id}

Get the progress of a background task.

**Response:**

```json
{
  "success": true,
  "progress": 75,
  "message": "Processing file 3 of 4",
  "timestamp": 1621234567,
  "estimated_time": "10s remaining"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Task not found",
    "code": 404
  }
}
```

### GET /tools/api/task_result/{task_id}

Get the result of a completed task.

**Response:**

```json
{
  "success": true,
  "state": "SUCCESS",
  "task_id": "abc123def456",
  "total": 4,
  "successful": 4,
  "failed": 0,
  "results": [
    {
      "filename": "document1.docx",
      "output_filename": "document1.pdf",
      "success": true
    },
    {
      "filename": "document2.docx",
      "output_filename": "document2.pdf",
      "success": true
    }
  ],
  "failed_files": []
}
```

**Error Response:**

```json
{
  "success": false,
  "error": {
    "message": "Task failed: Conversion error",
    "code": 500,
    "state": "FAILURE"
  }
}
```

## Utility APIs

### Confession API

#### GET /tools/api/confession

Retrieve a random confession.

**Response:**

```json
{
  "success": true,
  "confession": "This is a random confession"
}
```

#### POST /tools/api/confession

Add a new confession.

**Request Body:**

```json
{
  "confession": "This is my confession"
}
```

**Response:**

```json
{
  "success": true
}
```

### Poll API

#### GET /tools/api/poll

Get current poll results.

**Response:**

```json
{
  "success": true,
  "results": {
    "tabs": 10,
    "spaces": 5
  }
}
```

#### POST /tools/api/poll

Vote in the poll.

**Request Body:**

```json
{
  "option": "tabs"
}
```

**Response:**

```json
{
  "success": true,
  "results": {
    "tabs": 11,
    "spaces": 5
  }
}
```

### Vibe API

#### GET /tools/api/vibe

Get current vibe counts.

**Response:**

```json
{
  "success": true,
  "vibes": {
    "productive": 5,
    "tired": 3,
    "debugging": 7,
    "procrastinating": 2
  }
}
```

#### POST /tools/api/vibe

Update your vibe.

**Request Body:**

```json
{
  "vibe": "debugging"
}
```

**Response:**

```json
{
  "success": true,
  "vibes": {
    "productive": 5,
    "tired": 3,
    "debugging": 8,
    "procrastinating": 2
  }
}
```

### History API

#### GET /tools/api/history

Get a random tech history fact.

**Response:**

```json
{
  "success": true,
  "text": "The first computer bug was an actual bug...",
  "link": "https://en.wikipedia.org/wiki/Bug_(engineering)"
}
```
```

## Error Codes

- `400` - Bad Request (invalid input)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate-limited to prevent abuse. The current limits are:

- 200 requests per day
- 50 requests per hour

When a rate limit is exceeded, the API will return a 429 status code with a message indicating when the limit will reset.
