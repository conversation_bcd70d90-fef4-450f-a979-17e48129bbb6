import os
import logging
from logging.handlers import RotatingFileH<PERSON><PERSON>
from pathlib import Path
from datetime import datetime, timedelta
from dotenv import load_dotenv
from celery import Celery
from flask import Flask
from flask_wtf.csrf import CSRFProtect

# Initialize CSRF protection globally
csrf = CSRFProtect()

# Load environment variables from .env file
load_dotenv()


class Config:
    """Enhanced application configuration with security checks and validation."""

    def __init__(self):
        # Validate essential configurations on initialization
        self._validate_config()

    def _validate_config(self):
        """Validate critical configuration values."""
        if not self.SECRET_KEY or len(self.SECRET_KEY) < 16:
            raise ValueError("Invalid SECRET_KEY - must be at least 16 characters")
        if not Path(self.UPLOAD_FOLDER).parent.exists():
            raise OSError(f"Parent directory for UPLOAD_FOLDER does not exist: {self.UPLOAD_FOLDER}")

    # Environment Variables
    ENV = os.getenv("FLASK_ENV", "production")
    DEBUG = ENV == "development"
    # Security Configuration
    SECRET_KEY = os.getenv("SECRET_KEY", os.urandom(24).hex())
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour token expiration

    # Debug Mode (secure default)
    DEBUG = os.getenv("FLASK_DEBUG", "False").lower() == "true"

    # Base Directory
    BASE_DIR = Path(__file__).resolve().parent.parent  # Points to project root

    # File Handling
    UPLOAD_FOLDER = BASE_DIR / "data" / "uploads"  # More organized structure
    OUTPUT_FOLDER = BASE_DIR / "data" / "outputs"
    ALLOWED_EXTENSIONS = {"csv", "xlsx", "json", "xml", "parquet"}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB per file
    MAX_FOLDER_SIZE = 2 * 1024 * 1024 * 1024  # 2GB total storage
    UPLOAD_CLEANUP_MINUTES = 3

    # Database
    SQLALCHEMY_DATABASE_URI = os.getenv("DATABASE_URL", f"sqlite:///{BASE_DIR}/data/app.db")
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False

    # Celery
    CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
    CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", CELERY_BROKER_URL)

    # Caching
    CACHE_TYPE = os.getenv("CACHE_TYPE", "RedisCache")
    CACHE_REDIS_URL = os.getenv("CACHE_REDIS_URL", "redis://localhost:6379/1")
    CACHE_DEFAULT_TIMEOUT = 300  # 5 minutes
    CACHE_KEY_PREFIX = "imgcipherf1:"

    # Logging
    LOG_DIR = BASE_DIR / "logs"
    LOG_FILE = LOG_DIR / "app.log"
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB per log file
    LOG_BACKUP_COUNT = 5
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s [%(filename)s:%(lineno)d]"

    @property
    def database_uri(self):
        """Get validated database URI."""
        if not self.SQLALCHEMY_DATABASE_URI:
            raise ValueError("Database configuration is required")
        return self.SQLALCHEMY_DATABASE_URI

    @staticmethod
    def ensure_path(path: Path) -> Path:
        """Ensure directory exists and return resolved path."""
        path.mkdir(parents=True, exist_ok=True)
        return path.resolve()

    @classmethod
    def cleanup_old_files(cls, folder: Path, minutes: int = UPLOAD_CLEANUP_MINUTES):
        """Safely delete files older than specified minutes."""
        try:
            folder = cls.ensure_path(folder)
            cutoff = datetime.now() - timedelta(minutes=minutes)

            for item in folder.iterdir():
                if item.is_file() and datetime.fromtimestamp(item.stat().st_mtime) < cutoff:
                    item.unlink()
                    logging.info(f"Cleaned up old file: {item}")
        except Exception as e:
            logging.error(f"Cleanup failed for {folder}: {str(e)}")


# Initialize Flask App
app = Flask(__name__)
app.config.from_object(Config())

# Enhanced Logging Configuration


def configure_logging():
    """Configure structured, production-ready logging."""
    config = app.config

    # Ensure log directory exists
    Config.ensure_path(config["LOG_DIR"])

    # Create formatter
    formatter = logging.Formatter(config["LOG_FORMAT"])

    # File handler with rotation
    file_handler = RotatingFileHandler(
        config["LOG_FILE"], maxBytes=config["LOG_MAX_BYTES"], backupCount=config["LOG_BACKUP_COUNT"], encoding="utf-8"
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)

    # Console handler (different levels for dev/prod)
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG if app.debug else logging.WARNING)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()  # Remove any existing handlers
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    root_logger.setLevel(logging.DEBUG)

    # Configure noisy loggers
    for logger_name in ["werkzeug", "celery", "sqlalchemy"]:
        logging.getLogger(logger_name).setLevel(logging.WARNING if not app.debug else logging.INFO)


# Initialize Celery with enhanced configuration


def make_celery(flask_app):
    """Create configured Celery instance with Flask context."""
    celery = Celery(
        flask_app.import_name, backend=flask_app.config["CELERY_RESULT_BACKEND"], broker=flask_app.config["CELERY_BROKER_URL"]
    )

    celery.conf.update(
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        enable_utc=True,
        task_track_started=True,
        broker_connection_retry_on_startup=True,
    )

    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with flask_app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
    return celery


# Initialize extensions
csrf.init_app(app)
celery = make_celery(app)


# Application startup routines
def startup():
    """Run initialization tasks."""
    config = app.config

    # Ensure data directories exist
    for folder in [config["UPLOAD_FOLDER"], config["OUTPUT_FOLDER"]]:
        Config.ensure_path(folder)

    # Configure logging
    configure_logging()

    # Initial cleanup
    Config.cleanup_old_files(config["UPLOAD_FOLDER"])
    Config.cleanup_old_files(config["OUTPUT_FOLDER"])

    logging.info("Application startup completed")


# Execute startup routines
startup()
