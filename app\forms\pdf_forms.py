"""
Form classes for PDF operations.
"""

from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON><PERSON>, FileRequired, FileAllowed
from wtforms import <PERSON>Field, SubmitField
from wtforms.validators import DataRequired, Length, Regexp


class SplitPDFForm(FlaskForm):
    """Form for splitting PDF files."""
    
    pdf_file = FileField(
        "PDF File",
        validators=[
            FileRequired(),
            FileAllowed(["pdf"], "PDF files only!"),
        ],
    )
    
    pages = StringField(
        "Pages to Extract",
        validators=[
            DataRequired(),
            Length(min=1, max=100),
            Regexp(
                r"^[0-9,\-\s]+$",
                message="Please enter valid page numbers (e.g., 1-3,5,7-9)",
            ),
        ],
    )

    output_filename = StringField(
        "Output Filename",
        validators=[
            Length(max=100),
        ],
    )

    submit = SubmitField("Split PDF")


class MergePDFForm(FlaskForm):
    """Form for merging PDF files."""
    
    pdf_files = <PERSON><PERSON>ield(
        "PDF Files",
        validators=[
            FileRequired(),
            FileAllowed(["pdf"], "PDF files only!"),
        ],
    )
    
    submit = SubmitField("Merge PDFs")
