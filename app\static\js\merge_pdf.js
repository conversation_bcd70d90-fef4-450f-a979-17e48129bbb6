// DOM Elements
const dropArea = document.getElementById("drop-area");
const fileInput = document.getElementById("file-input");
const filePreviewContainer = document.getElementById("file-preview-container");
const fileInfo = document.getElementById("file-info");
const fileCount = document.getElementById("file-count");
const sizeWarning = document.getElementById("size-warning");
const progressContainer = document.getElementById("progress-container");
const progressBar = document.getElementById("progress-bar");
const progressText = document.getElementById("progress-text");
const submitButton = document.getElementById("submit-button");
const form = document.getElementById("pdf-merge-form");
const toastContainer = document.getElementById("toast-container");
const formContainer = document.querySelector(".form-container");

// State
let files = [];

// Helper Functions
const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

const showToast = (message, type = "success") => {
    const toast = document.createElement("div");
    toast.className = `flex items-center p-4 w-full max-w-xs rounded-lg shadow text-white ${
        type === "error" ? "bg-red-600" : "bg-blue-600"
    } transition-all duration-300`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', type === "error" ? 'assertive' : 'polite');

    toast.innerHTML = `
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${
                type === "error" ? "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" : "M5 13l4 4L19 7"
            }"></path>
        </svg>
        <span>${message}</span>
    `;

    // Update screen reader status
    const statusElement = document.getElementById('file-upload-status');
    if (statusElement) {
        statusElement.textContent = `${type === "error" ? "Error" : "Success"}: ${message}`;
    }

    toastContainer.appendChild(toast);
    setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-2");
        setTimeout(() => toast.remove(), 300);
    }, 5000);
};

const updateProgress = (percent) => {
    progressBar.style.width = `${percent}%`;
    progressText.textContent = `${percent}%`;
};

const resetProgress = () => {
    updateProgress(0);
    progressContainer.classList.add("hidden");
};

const toggleProcessing = (processing) => {
    submitButton.disabled = processing;
    progressContainer.classList.toggle("hidden", !processing);
};

// File Handling
const handleDrop = (e) => {
    e.preventDefault();
    highlightDropArea(false);
    const newFiles = Array.from(e.dataTransfer.files).filter((file) => file.type === "application/pdf");
    if (!newFiles.length) return showToast("Only PDF files are accepted", "error");
    addFiles(newFiles);
};

const handleFiles = () => {
    const newFiles = Array.from(fileInput.files).filter((file) => file.type === "application/pdf");
    if (newFiles.length) addFiles(newFiles);
};

const addFiles = (newFiles) => {
    files = [...files, ...newFiles];
    updateFileDisplay();
};

const updateFileDisplay = () => {
    filePreviewContainer.innerHTML = "";
    let totalSize = 0;

    if (!files.length) {
        fileCount.textContent = "No PDF files selected.";
        sizeWarning.classList.add("hidden");
        submitButton.disabled = true;

        // Update screen reader status
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = "No files selected";
        }
        return;
    }

    fileCount.textContent = `${files.length} PDF file${files.length > 1 ? "s" : ""} selected`;

    // Update screen reader status
    const statusElement = document.getElementById('file-upload-status');
    if (statusElement) {
        statusElement.textContent = `${files.length} PDF file${files.length > 1 ? 's' : ''} selected`;
    }
    files.forEach((file, index) => {
        totalSize += file.size;
        filePreviewContainer.appendChild(createFilePreview(file, index));
    });

    if (files.length) {
        const addMoreBtn = document.createElement("button");
        addMoreBtn.type = "button";
        addMoreBtn.className = "mt-2 inline-flex items-center text-sm text-red-400 hover:text-red-300 transition duration-300";
        addMoreBtn.innerHTML = `
            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add More PDFs
        `;
        addMoreBtn.addEventListener("click", () => fileInput.click());
        filePreviewContainer.appendChild(addMoreBtn);
    }

    sizeWarning.classList.toggle("hidden", totalSize <= 50 * 1024 * 1024);
    submitButton.disabled = files.length < 2;
};

const createFilePreview = (file, index) => {
    const div = document.createElement("div");
    div.className = "flex items-center bg-gray-700 px-3 py-2 rounded-lg mb-2";
    div.innerHTML = `
        <svg class="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        <div class="flex-1 min-w-0">
            <p class="text-gray-300 truncate">${file.name}</p>
            <p class="text-gray-500 text-xs">${formatFileSize(file.size)}</p>
        </div>
        <button type="button" class="ml-2 text-gray-400 hover:text-red-500 transition-colors" onclick="removeFile(${index})">
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
        </button>
    `;
    return div;
};

const removeFile = (index) => {
    files.splice(index, 1);
    updateFileDisplay();
};

const highlightDropArea = (highlight) => {
    dropArea.classList.toggle("drag-active", highlight);

    // Update screen reader status
    const statusElement = document.getElementById('file-upload-status');
    if (statusElement) {
        statusElement.textContent = highlight ?
            "Files are being dragged over the drop area" :
            files.length ? `${files.length} PDF file${files.length > 1 ? 's' : ''} selected` : "No files selected";
    }
};

// Form Submission
const handleSubmit = async (e) => {
    e.preventDefault();
    if (files.length < 2) return showToast("Please select at least 2 PDF files to merge", "error");

    const formData = new FormData();
    files.forEach((file) => formData.append("files", file));
    formData.append("output_filename", document.getElementById("output-filename").value || "merged.pdf");
    formData.append("optimize", document.getElementById("optimize").checked);
    formData.append("csrf_token", document.querySelector("[name='csrf_token']").value);

    toggleProcessing(true);
    updateProgress(20);

    try {
        const response = await fetch(form.action, {
            method: "POST",
            body: formData,
        });

        const contentType = response.headers.get("content-type");
        const disposition = response.headers.get("content-disposition");

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(errorText || "Merge failed");
        }

        if (contentType?.includes("application/json")) {
            const result = await response.json();
            if (result.download_url) {
                showResultsContainer(result.download_url, formData.get("output_filename"));
                showToast("PDFs merged successfully!", "success");
            } else {
                throw new Error(result.error || "Invalid server response");
            }
        } else {
            const blob = await response.blob();
            const filename = disposition?.match(/filename="(.+)"/)?.[1] || formData.get("output_filename");
            const url = URL.createObjectURL(blob);
            showResultsContainer(url, filename.endsWith(".pdf") ? filename : `${filename}.pdf`);
            showToast("PDFs merged successfully!", "success");
        }

        updateProgress(100);
    } catch (error) {
        console.error("Merge error:", error);
        showToast(error.message.includes("JSON") ? "Server error occurred" : error.message, "error");
    } finally {
        setTimeout(() => {
            toggleProcessing(false);
            resetProgress();
        }, 1000);
    }
};

const showResultsContainer = (url, filename) => {
    const resultsContainer = document.createElement("div");
    resultsContainer.className = "w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 mt-6";
    resultsContainer.innerHTML = `
        <div class="mb-6 text-center">
            <svg class="mx-auto h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <h3 class="mt-4 text-2xl font-semibold text-white">Merge Successful!</h3>
            <p class="mt-2 text-gray-400">Your PDFs have been merged.</p>
        </div>
        <div class="p-4 bg-gray-700 rounded-lg">
            <div class="flex justify-between items-center">
                <span class="text-gray-300">${filename}</span>
                <a href="${url}" id="download-link" class="text-red-500 hover:text-red-400 hover:underline" download="${filename}">
                    Download Merged PDF
                </a>
            </div>
        </div>
        <button id="new-merge-btn" class="mt-6 w-full px-4 py-3 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 transition">
            Merge More PDFs
        </button>
    `;

    formContainer.insertAdjacentElement("afterend", resultsContainer);
    formContainer.classList.add("hidden");

    resultsContainer.querySelector("#download-link").addEventListener("click", () => {
        setTimeout(() => {
            resetForm();
            resultsContainer.remove();
            URL.revokeObjectURL(url);
        }, 500);
    });

    resultsContainer.querySelector("#new-merge-btn").addEventListener("click", () => {
        resetForm();
        resultsContainer.remove();
        URL.revokeObjectURL(url);
    });
};

const resetForm = () => {
    form.reset();
    fileInput.value = "";
    files = [];
    filePreviewContainer.innerHTML = "";
    fileCount.textContent = "No PDF files selected.";
    sizeWarning.classList.add("hidden");
    submitButton.disabled = true;
    formContainer.classList.remove("hidden");
};

// Event Listeners
const initEventListeners = () => {
    // Prevent default drag behaviors
    ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ["dragenter", "dragover"].forEach((eventName) =>
        dropArea.addEventListener(eventName, () => highlightDropArea(true))
    );

    // Remove highlight when item leaves or is dropped
    ["dragleave", "drop"].forEach((eventName) =>
        dropArea.addEventListener(eventName, () => highlightDropArea(false))
    );

    // Handle file drop
    dropArea.addEventListener("drop", handleDrop);

    // Handle file selection via input
    fileInput.addEventListener("change", handleFiles);

    // Handle form submission
    form.addEventListener("submit", handleSubmit);

    // Make drop area clickable
    dropArea.addEventListener("click", () => fileInput.click());

    // Add keyboard support for drop area
    dropArea.addEventListener("keydown", (e) => {
        if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            fileInput.click();
        }
    });
};

const preventDefaults = (e) => {
    e.preventDefault();
    e.stopPropagation();
};

// Initialize
document.addEventListener("DOMContentLoaded", () => {
    initEventListeners();
    window.removeFile = removeFile;
});