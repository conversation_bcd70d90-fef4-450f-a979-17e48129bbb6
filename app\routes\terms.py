from flask import Blueprint, render_template, make_response
from app.cache import cached_static_page
from app.utils import add_cache_headers

# Terms and conditions blueprint
terms_bp = Blueprint("terms", __name__)


@terms_bp.route("/terms", methods=["GET"])
@cached_static_page("terms_and_conditions")
def terms_and_conditions():
    """Render the terms and conditions page with caching."""
    response = make_response(render_template("terms_and_conditions.html"))
    return add_cache_headers(response, max_age=86400)  # Cache for 24 hours
