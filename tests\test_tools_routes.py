"""
Smoke tests for tools routes.
"""

# flake8: noqa
import pytest
from flask import url_for


def test_split_pdf_page(client):
    """Test that the split PDF page loads successfully."""
    response = client.get("/tools/split_pdf")
    assert response.status_code == 200
    assert b"<!DOCTYPE html>" in response.data


def test_merge_pdf_page(client, monkeypatch):
    """Test that the merge PDF page loads successfully."""
    # Monkeypatch the render_template function to avoid form issues
    from flask import render_template as original_render

    def mock_render_template(*args, **kwargs):
        if "merge_pdf.html" in args:
            kwargs["form"] = type("obj", (object,), {"hidden_tag": lambda: ""})
        return original_render(*args, **kwargs)

    monkeypatch.setattr("flask.render_template", mock_render_template)

    response = client.get("/tools/merge_pdf")
    assert response.status_code == 200
    assert b"<!DOCTYPE html>" in response.data


def test_privacy_page(client):
    """Test that the privacy page loads successfully."""
    response = client.get("/privacy/privacy")
    assert response.status_code == 200
    assert b"<!DOCTYPE html>" in response.data


def test_terms_page(client):
    """Test that the terms page loads successfully."""
    response = client.get("/terms/terms")
    assert response.status_code == 200
    assert b"<!DOCTYPE html>" in response.data
