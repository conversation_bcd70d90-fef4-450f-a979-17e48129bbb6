"""
Tests for authentication functionality.
"""

# pytest is used as a fixture by the test runner
# flake8: noqa
import pytest
from flask import url_for
from flask_login import current_user
from app.models.auth import User, Role, UserStatus
from app.extensions import db


def test_login_page(client):
    """Test that the login page loads successfully."""
    response = client.get("/auth/login")
    assert response.status_code == 200
    assert b"Sign In" in response.data
    assert b"Username" in response.data
    assert b"Password" in response.data
    assert b"Remember Me" in response.data


def test_register_page(client):
    """Test that the register page loads successfully."""
    response = client.get("/auth/register")
    assert response.status_code == 200
    assert b"Create an Account" in response.data
    assert b"Username" in response.data
    assert b"Email" in response.data
    assert b"Password" in response.data
    assert b"Confirm Password" in response.data


def test_user_registration(client, app):
    """Test user registration."""
    with app.app_context():
        # Make sure the user doesn't exist
        user = User.query.filter_by(username="testuser").first()
        if user:
            db.session.delete(user)
            db.session.commit()

        # Make sure the registered role exists
        if not Role.query.filter_by(name="registered").first():
            role = Role(name="registered", description="Standard access for registered users")
            db.session.add(role)
            db.session.commit()

    # Register a new user
    response = client.post(
        "/auth/register",
        data={"username": "testuser", "email": "<EMAIL>", "password": "password123", "password2": "password123"},
        follow_redirects=True,
    )

    assert response.status_code == 200
    assert b"Registration successful!" in response.data

    # Check that the user was created
    with app.app_context():
        user = User.query.filter_by(username="testuser").first()
        assert user is not None
        assert user.email == "<EMAIL>"
        assert user.verify_password("password123")
        assert user.status == UserStatus.ACTIVE
        assert len(user.roles) == 1
        assert user.roles[0].name == "registered"


def test_login_logout(client, app):
    """Test login and logout functionality."""
    with app.app_context():
        # Make sure the user exists
        user = User.query.filter_by(username="testuser").first()
        if not user:
            user = User(username="testuser", email="<EMAIL>", status=UserStatus.ACTIVE)
            user.password = "password123"
            db.session.add(user)
            db.session.commit()

    # Login
    response = client.post(
        "/auth/login", data={"username": "testuser", "password": "password123", "remember_me": False}, follow_redirects=True
    )

    assert response.status_code == 200
    assert b"testuser" in response.data  # Username should appear in the navbar

    # Check that the user is logged in
    with client:
        client.get("/")
        assert current_user.is_authenticated
        assert current_user.username == "testuser"

    # Logout
    response = client.get("/auth/logout", follow_redirects=True)
    assert response.status_code == 200
    assert b"You have been logged out." in response.data

    # Check that the user is logged out
    with client:
        client.get("/")
        assert not current_user.is_authenticated


def test_profile_page(client, app):
    """Test that the profile page requires login and displays user info."""
    # Try to access profile page without login
    response = client.get("/auth/profile")
    assert response.status_code == 302  # Should redirect to login

    with app.app_context():
        # Make sure the user exists
        user = User.query.filter_by(username="testuser").first()
        if not user:
            user = User(username="testuser", email="<EMAIL>", status=UserStatus.ACTIVE)
            user.password = "password123"
            db.session.add(user)
            db.session.commit()

    # Login
    client.post("/auth/login", data={"username": "testuser", "password": "password123"})

    # Access profile page
    response = client.get("/auth/profile")
    assert response.status_code == 200
    assert b"User Profile" in response.data
    assert b"testuser" in response.data
    assert b"<EMAIL>" in response.data
