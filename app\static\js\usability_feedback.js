/**
 * Usability Testing and Feedback Collection
 * Handles user interaction with the usability test interface and submits feedback
 */

document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const startTestBtn = document.getElementById('start-test-btn');
  const testIntro = document.querySelector('.test-intro');
  const testProgress = document.getElementById('test-progress');
  const testScenarios = document.getElementById('test-scenarios');
  const progressIndicator = document.getElementById('progress-indicator');
  const progressBar = document.getElementById('progress-bar');
  const testComplete = document.getElementById('test-complete');
  const feedbackSubmitted = document.getElementById('feedback-submitted');
  const submitTestBtn = document.getElementById('submit-test-btn');
  
  // Test data
  const testData = {
    testId: null,
    currentScenario: 0,
    totalScenarios: document.querySelectorAll('.scenario-card').length,
    results: [],
    overallSatisfaction: null,
    finalComments: '',
    startTime: null,
    browser: getBrowserInfo(),
    deviceType: getDeviceType(),
    screenSize: `${window.innerWidth}x${window.innerHeight}`
  };
  
  // Initialize feedback widget on all pages
  initFeedbackWidget();
  
  // Start test button click handler
  if (startTestBtn) {
    startTestBtn.addEventListener('click', () => {
      testIntro.classList.add('hidden');
      testProgress.classList.remove('hidden');
      testScenarios.classList.remove('hidden');
      testData.startTime = new Date();
      testData.testId = generateTestId();
      
      // Start the test session in the backend
      startTestSession();
      
      // Update progress indicator
      updateProgress();
    });
  }
  
  // Next scenario button click handlers
  document.querySelectorAll('.next-scenario-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      // Save current scenario data
      saveCurrentScenarioData();
      
      // Hide current scenario
      const currentScenarioEl = document.querySelector(`.scenario-card:not(.hidden)`);
      currentScenarioEl.classList.add('hidden');
      
      // Show next scenario
      testData.currentScenario++;
      const nextScenarioEl = document.getElementById(`scenario-${testData.currentScenario + 1}`);
      if (nextScenarioEl) {
        nextScenarioEl.classList.remove('hidden');
      }
      
      // Update progress indicator
      updateProgress();
    });
  });
  
  // Previous scenario button click handlers
  document.querySelectorAll('.prev-scenario-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      // Save current scenario data
      saveCurrentScenarioData();
      
      // Hide current scenario
      const currentScenarioEl = document.querySelector(`.scenario-card:not(.hidden)`);
      currentScenarioEl.classList.add('hidden');
      
      // Show previous scenario
      testData.currentScenario--;
      const prevScenarioEl = document.getElementById(`scenario-${testData.currentScenario + 1}`);
      if (prevScenarioEl) {
        prevScenarioEl.classList.remove('hidden');
      }
      
      // Update progress indicator
      updateProgress();
    });
  });
  
  // Complete test button click handler
  document.querySelectorAll('.complete-test-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      // Save current scenario data
      saveCurrentScenarioData();
      
      // Hide scenarios and show completion screen
      testScenarios.classList.add('hidden');
      testComplete.classList.remove('hidden');
      
      // Update progress
      progressIndicator.textContent = 'Test Complete';
      progressBar.style.width = '100%';
    });
  });
  
  // Task result buttons click handlers
  document.querySelectorAll('.task-result-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      // Remove active class from all buttons in this group
      e.target.closest('.task-feedback').querySelectorAll('.task-result-btn').forEach(b => {
        b.classList.remove('bg-opacity-100');
        b.classList.add('bg-opacity-70');
      });
      
      // Add active class to clicked button
      btn.classList.remove('bg-opacity-70');
      btn.classList.add('bg-opacity-100');
      
      // Store the result
      const scenarioId = btn.closest('.scenario-card').dataset.scenarioId;
      const result = btn.dataset.result;
      
      // Find or create scenario data
      let scenarioData = testData.results.find(r => r.scenarioId === scenarioId);
      if (!scenarioData) {
        scenarioData = { scenarioId };
        testData.results.push(scenarioData);
      }
      
      // Update result
      scenarioData.result = result;
    });
  });
  
  // Difficulty stars click handlers
  document.querySelectorAll('.difficulty-star').forEach(star => {
    star.addEventListener('click', (e) => {
      const value = parseInt(star.dataset.value);
      const starsContainer = star.closest('.difficulty-stars');
      const label = star.closest('.difficulty-rating').querySelector('.difficulty-label');
      
      // Update stars
      starsContainer.querySelectorAll('.difficulty-star').forEach(s => {
        const starValue = parseInt(s.dataset.value);
        if (starValue <= value) {
          s.classList.remove('text-gray-500');
          s.classList.add('text-yellow-500');
        } else {
          s.classList.remove('text-yellow-500');
          s.classList.add('text-gray-500');
        }
      });
      
      // Update label
      const labels = ['Very Easy', 'Easy', 'Moderate', 'Difficult', 'Very Difficult'];
      label.textContent = labels[value - 1];
      
      // Store the difficulty
      const scenarioId = star.closest('.scenario-card').dataset.scenarioId;
      
      // Find or create scenario data
      let scenarioData = testData.results.find(r => r.scenarioId === scenarioId);
      if (!scenarioData) {
        scenarioData = { scenarioId };
        testData.results.push(scenarioData);
      }
      
      // Update difficulty
      scenarioData.difficulty = value;
    });
  });
  
  // Satisfaction stars click handlers
  document.querySelectorAll('.satisfaction-star').forEach(star => {
    star.addEventListener('click', (e) => {
      const value = parseInt(star.dataset.value);
      const starsContainer = star.closest('.satisfaction-stars');
      const label = star.closest('.overall-satisfaction').querySelector('.satisfaction-label');
      
      // Update stars
      starsContainer.querySelectorAll('.satisfaction-star').forEach(s => {
        const starValue = parseInt(s.dataset.value);
        if (starValue <= value) {
          s.classList.remove('text-gray-500');
          s.classList.add('text-yellow-500');
        } else {
          s.classList.remove('text-yellow-500');
          s.classList.add('text-gray-500');
        }
      });
      
      // Update label
      const labels = ['Very Dissatisfied', 'Dissatisfied', 'Neutral', 'Satisfied', 'Very Satisfied'];
      label.textContent = labels[value - 1];
      
      // Store the satisfaction
      testData.overallSatisfaction = value;
    });
  });
  
  // Submit test button click handler
  if (submitTestBtn) {
    submitTestBtn.addEventListener('click', () => {
      // Get final comments
      testData.finalComments = document.getElementById('final-comments').value;
      
      // Submit all test data
      submitTestData();
      
      // Show submitted screen
      testComplete.classList.add('hidden');
      feedbackSubmitted.classList.remove('hidden');
    });
  }
  
  // Helper functions
  function updateProgress() {
    progressIndicator.textContent = `Task ${testData.currentScenario + 1} of ${testData.totalScenarios}`;
    const progressPercentage = ((testData.currentScenario + 1) / testData.totalScenarios) * 100;
    progressBar.style.width = `${progressPercentage}%`;
    
    // Update backend with progress
    updateTestProgress(testData.currentScenario + 1, testData.totalScenarios);
  }
  
  function saveCurrentScenarioData() {
    const currentScenarioEl = document.querySelector(`.scenario-card:not(.hidden)`);
    if (!currentScenarioEl) return;
    
    const scenarioId = currentScenarioEl.dataset.scenarioId;
    const comment = currentScenarioEl.querySelector('.task-comment').value;
    
    // Find or create scenario data
    let scenarioData = testData.results.find(r => r.scenarioId === scenarioId);
    if (!scenarioData) {
      scenarioData = { scenarioId };
      testData.results.push(scenarioData);
    }
    
    // Update comment
    scenarioData.comment = comment;
  }
  
  function startTestSession() {
    fetch('/feedback/start-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCSRFToken()
      },
      body: JSON.stringify({
        test_name: 'ImgCipherF1 Usability Test',
        browser_info: testData.browser,
        device_type: testData.deviceType,
        screen_size: testData.screenSize,
        tasks_total: testData.totalScenarios
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        console.log('Test session started:', data.test_id);
      } else {
        console.error('Error starting test session:', data.error);
      }
    })
    .catch(error => {
      console.error('Error starting test session:', error);
    });
  }
  
  function updateTestProgress(tasksCompleted, tasksTotal) {
    fetch('/feedback/update-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCSRFToken()
      },
      body: JSON.stringify({
        tasks_completed: tasksCompleted,
        tasks_total: tasksTotal
      })
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        console.error('Error updating test progress:', data.error);
      }
    })
    .catch(error => {
      console.error('Error updating test progress:', error);
    });
  }
  
  function submitTestData() {
    // First, complete the test session
    fetch('/feedback/update-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCSRFToken()
      },
      body: JSON.stringify({
        status: 'completed',
        satisfaction_score: testData.overallSatisfaction
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        console.log('Test session completed');
        
        // Then submit individual task feedback
        testData.results.forEach(scenarioData => {
          submitFeedback({
            page_url: window.location.pathname,
            feedback_type: 'usability_test',
            rating: scenarioData.difficulty,
            comment: scenarioData.comment,
            metadata: {
              scenario_id: scenarioData.scenarioId,
              result: scenarioData.result
            }
          });
        });
        
        // Submit final feedback
        submitFeedback({
          page_url: window.location.pathname,
          feedback_type: 'usability_test_summary',
          rating: testData.overallSatisfaction,
          comment: testData.finalComments,
          metadata: {
            test_duration: (new Date() - testData.startTime) / 1000,
            scenarios_completed: testData.results.length
          }
        });
      } else {
        console.error('Error completing test session:', data.error);
      }
    })
    .catch(error => {
      console.error('Error completing test session:', error);
    });
  }
  
  function submitFeedback(feedbackData) {
    // Add browser and device info
    feedbackData.browser_info = testData.browser;
    feedbackData.device_type = testData.deviceType;
    feedbackData.screen_size = testData.screenSize;
    
    fetch('/feedback/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCSRFToken()
      },
      body: JSON.stringify(feedbackData)
    })
    .then(response => response.json())
    .then(data => {
      if (!data.success) {
        console.error('Error submitting feedback:', data.error);
      }
    })
    .catch(error => {
      console.error('Error submitting feedback:', error);
    });
  }
  
  function getCSRFToken() {
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
  }
  
  function getBrowserInfo() {
    const userAgent = navigator.userAgent;
    let browserName;
    
    if (userAgent.match(/chrome|chromium|crios/i)) {
      browserName = "Chrome";
    } else if (userAgent.match(/firefox|fxios/i)) {
      browserName = "Firefox";
    } else if (userAgent.match(/safari/i)) {
      browserName = "Safari";
    } else if (userAgent.match(/opr\//i)) {
      browserName = "Opera";
    } else if (userAgent.match(/edg/i)) {
      browserName = "Edge";
    } else {
      browserName = "Unknown";
    }
    
    return browserName;
  }
  
  function getDeviceType() {
    const userAgent = navigator.userAgent;
    
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
      return 'Mobile';
    } else if (/iPad|Tablet|PlayBook/i.test(userAgent)) {
      return 'Tablet';
    } else {
      return 'Desktop';
    }
  }
  
  function generateTestId() {
    return 'test_' + Math.random().toString(36).substring(2, 15);
  }
  
  function initFeedbackWidget() {
    // Create feedback widget if it doesn't exist
    if (!document.getElementById('feedback-widget')) {
      const widget = document.createElement('div');
      widget.id = 'feedback-widget';
      widget.className = 'feedback-widget fixed right-5 bottom-36 z-50';
      widget.innerHTML = `
        <button id="feedback-widget-btn" class="bg-blue-600 hover:bg-blue-500 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-110">
          <i class="fas fa-comment-alt"></i>
        </button>
        <div id="feedback-widget-panel" class="hidden absolute bottom-14 right-0 w-72 bg-gray-800 rounded-lg shadow-xl p-4">
          <h3 class="text-lg font-semibold mb-3">Send Feedback</h3>
          <div class="mb-3">
            <label class="block text-sm font-medium mb-1">How would you rate this page?</label>
            <div class="flex space-x-1">
              <button class="feedback-rating-btn text-2xl text-gray-500 hover:text-yellow-500" data-value="1">★</button>
              <button class="feedback-rating-btn text-2xl text-gray-500 hover:text-yellow-500" data-value="2">★</button>
              <button class="feedback-rating-btn text-2xl text-gray-500 hover:text-yellow-500" data-value="3">★</button>
              <button class="feedback-rating-btn text-2xl text-gray-500 hover:text-yellow-500" data-value="4">★</button>
              <button class="feedback-rating-btn text-2xl text-gray-500 hover:text-yellow-500" data-value="5">★</button>
            </div>
          </div>
          <div class="mb-3">
            <label class="block text-sm font-medium mb-1">Feedback type</label>
            <select id="feedback-type" class="w-full p-2 bg-gray-700 border border-gray-600 rounded">
              <option value="general">General Feedback</option>
              <option value="bug_report">Bug Report</option>
              <option value="feature_request">Feature Request</option>
            </select>
          </div>
          <div class="mb-3">
            <label class="block text-sm font-medium mb-1">Comments</label>
            <textarea id="feedback-comment" class="w-full p-2 bg-gray-700 border border-gray-600 rounded" rows="3" placeholder="Tell us what you think..."></textarea>
          </div>
          <div class="flex justify-end space-x-2">
            <button id="feedback-cancel-btn" class="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm transition-colors">Cancel</button>
            <button id="feedback-submit-btn" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 rounded text-sm transition-colors">Submit</button>
          </div>
        </div>
      `;
      document.body.appendChild(widget);
      
      // Add event listeners
      const widgetBtn = document.getElementById('feedback-widget-btn');
      const widgetPanel = document.getElementById('feedback-widget-panel');
      const cancelBtn = document.getElementById('feedback-cancel-btn');
      const submitBtn = document.getElementById('feedback-submit-btn');
      
      widgetBtn.addEventListener('click', () => {
        widgetPanel.classList.toggle('hidden');
      });
      
      cancelBtn.addEventListener('click', () => {
        widgetPanel.classList.add('hidden');
      });
      
      submitBtn.addEventListener('click', () => {
        const rating = document.querySelector('.feedback-rating-btn.active')?.dataset.value;
        const type = document.getElementById('feedback-type').value;
        const comment = document.getElementById('feedback-comment').value;
        
        if (comment) {
          submitFeedback({
            page_url: window.location.pathname,
            feedback_type: type,
            rating: rating ? parseInt(rating) : null,
            comment: comment
          });
          
          // Reset and hide
          document.querySelectorAll('.feedback-rating-btn').forEach(btn => {
            btn.classList.remove('active', 'text-yellow-500');
            btn.classList.add('text-gray-500');
          });
          document.getElementById('feedback-type').value = 'general';
          document.getElementById('feedback-comment').value = '';
          widgetPanel.classList.add('hidden');
          
          // Show success message
          alert('Thank you for your feedback!');
        } else {
          alert('Please enter a comment before submitting.');
        }
      });
      
      // Rating buttons
      document.querySelectorAll('.feedback-rating-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const value = parseInt(btn.dataset.value);
          
          document.querySelectorAll('.feedback-rating-btn').forEach(b => {
            const btnValue = parseInt(b.dataset.value);
            if (btnValue <= value) {
              b.classList.remove('text-gray-500');
              b.classList.add('text-yellow-500', 'active');
            } else {
              b.classList.remove('text-yellow-500', 'active');
              b.classList.add('text-gray-500');
            }
          });
        });
      });
    }
  }
});
