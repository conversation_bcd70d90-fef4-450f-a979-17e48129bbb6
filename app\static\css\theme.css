/* Theme CSS Variables for ImgCipherF1
 * This file defines CSS variables for dark mode only
 * Updated to meet WCAG AA contrast standards (4.5:1 for normal text, 3:1 for large text)
 */

:root {
  /* Dark mode theme (only theme) */

  /* Background colors */
  --bg-primary: #111827;       /* gray-900 */
  --bg-secondary: #1F2937;     /* gray-800 */
  --bg-tertiary: #374151;      /* gray-700 */
  --bg-accent: #1D4ED8;        /* blue-700 - improved contrast */

  /* Text colors */
  --text-primary: #F9FAFB;     /* gray-50 */
  --text-secondary: #E5E7EB;   /* gray-200 */
  --text-tertiary: #D1D5DB;    /* gray-300 - improved from gray-400 for better contrast */
  --text-accent: #60A5FA;      /* blue-400 - brighter for better visibility */

  /* Border colors */
  --border-primary: #4B5563;   /* gray-600 */
  --border-secondary: #6B7280; /* gray-500 */
  --border-accent: #60A5FA;    /* blue-400 - improved visibility */

  /* Button colors */
  --btn-primary-bg: #1D4ED8;   /* blue-700 - improved contrast */
  --btn-primary-text: #FFFFFF; /* white */
  --btn-secondary-bg: #4B5563; /* gray-600 */
  --btn-secondary-text: #F9FAFB; /* gray-50 */

  /* Tool-specific colors - improved for better contrast */
  --tool-split-pdf: #1D4ED8;   /* blue-700 - improved contrast */
  --tool-merge-pdf: #B91C1C;   /* red-700 - improved contrast */
  --tool-convert-image: #B45309; /* yellow-700 - improved contrast */
  --tool-extract-text: #6D28D9; /* purple-700 - improved contrast */
  --tool-convert-files: #047857; /* green-700 - improved contrast */

  /* Fun zone colors - improved for better contrast */
  --vibe-productive: #2563EB;  /* blue-600 - improved contrast */
  --vibe-tired: #3B82F6;       /* blue-500 - improved contrast */
  --vibe-debugging: #DC2626;   /* red-600 - improved contrast */
  --vibe-procrastinating: #D97706; /* yellow-600 - improved contrast */

  /* Focus outline for accessibility */
  --focus-ring: 0 0 0 3px rgba(96, 165, 250, 0.5); /* blue-400 with opacity */

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Animation timing variables */
  --ease-default: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Hover/active state transforms */
  --hover-scale: scale(1.05);
  --active-scale: scale(0.98);
  --hover-brightness: brightness(1.1);
  --active-brightness: brightness(0.9);
}



/* Base element styling using CSS variables */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

nav, footer {
  background-color: var(--bg-secondary);
}

/* Improved link styling with focus states for accessibility */
a {
  color: var(--text-accent);
  text-decoration: none;
  transition: color 0.2s ease, text-decoration 0.2s ease;
}



a:hover {
  color: var(--text-primary);
  text-decoration: underline;
}

/* Focus styles for keyboard navigation */
a:focus, button:focus, input:focus, select:focus, textarea:focus, [tabindex]:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* Skip to content link for accessibility */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--bg-accent);
  color: white;
  padding: 8px;
  z-index: 100;
  transition: top 0.3s ease;
}

.skip-to-content:focus {
  top: 0;
}

/* Button styling with enhanced animations and accessibility improvements */
.btn-primary {
  background-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-weight: 500;
  transition:
    background-color 0.2s var(--ease-out),
    transform 0.2s var(--ease-out),
    filter 0.2s var(--ease-out),
    box-shadow 0.2s var(--ease-out);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background-color: var(--btn-primary-bg);
  filter: var(--hover-brightness);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: var(--active-scale);
  filter: var(--active-brightness);
  box-shadow: var(--shadow-sm);
}

/* Button ripple effect */
.btn-primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.btn-primary:focus:not(:active)::after {
  animation: ripple 0.6s var(--ease-out);
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

.btn-secondary {
  background-color: var(--btn-secondary-bg);
  color: var(--btn-secondary-text);
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-weight: 500;
  transition:
    background-color 0.2s var(--ease-out),
    transform 0.2s var(--ease-out),
    filter 0.2s var(--ease-out),
    box-shadow 0.2s var(--ease-out);
  position: relative;
  overflow: hidden;
}

.btn-secondary:hover {
  background-color: var(--btn-secondary-bg);
  filter: var(--hover-brightness);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary:active {
  transform: var(--active-scale);
  filter: var(--active-brightness);
  box-shadow: var(--shadow-sm);
}

/* Disabled button state with improved visual feedback */
button:disabled, .btn-primary:disabled, .btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
}

button:disabled:hover, .btn-primary:disabled:hover, .btn-secondary:disabled:hover {
  filter: none;
  transform: none;
  box-shadow: none;
}







/* Form element accessibility improvements with enhanced animations */
input, select, textarea {
  border: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition:
    border-color 0.3s var(--ease-out),
    box-shadow 0.3s var(--ease-out),
    transform 0.2s var(--ease-out);
  position: relative;
}

input:hover, select:hover, textarea:hover {
  border-color: var(--border-accent);
  transform: translateY(-1px);
}

input:focus, select:focus, textarea:focus {
  border-color: var(--border-accent);
  box-shadow: var(--focus-ring);
  transform: translateY(-2px);
}

/* Animated label for form inputs */
.form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-group label {
  position: absolute;
  left: 0.5rem;
  top: 0.5rem;
  padding: 0 0.25rem;
  color: var(--text-tertiary);
  font-size: 1rem;
  transition:
    top 0.3s var(--ease-out),
    font-size 0.3s var(--ease-out),
    color 0.3s var(--ease-out),
    background-color 0.3s var(--ease-out);
  pointer-events: none;
  z-index: 1;
}

.form-group input:focus ~ label,
.form-group input:not(:placeholder-shown) ~ label,
.form-group textarea:focus ~ label,
.form-group textarea:not(:placeholder-shown) ~ label,
.form-group select:focus ~ label,
.form-group select:not([value=""]):not(:focus) ~ label {
  top: -0.75rem;
  font-size: 0.75rem;
  color: var(--text-accent);
  background-color: var(--bg-primary);
}

/* Checkbox and radio button animations */
input[type="checkbox"], input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition:
    border-color 0.3s var(--ease-out),
    background-color 0.3s var(--ease-out),
    box-shadow 0.3s var(--ease-out),
    transform 0.2s var(--ease-bounce);
}

input[type="radio"] {
  border-radius: 50%;
}

input[type="checkbox"]:checked, input[type="radio"]:checked {
  background-color: var(--border-accent);
  border-color: var(--border-accent);
}

input[type="checkbox"]:checked::before {
  content: '✓';
  color: white;
  font-size: 0.75rem;
  transform: scale(0);
  animation: checkmark 0.2s var(--ease-bounce) forwards;
}

input[type="radio"]:checked::before {
  content: '';
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: white;
  transform: scale(0);
  animation: checkmark 0.2s var(--ease-bounce) forwards;
}

@keyframes checkmark {
  0% { transform: scale(0); }
  100% { transform: scale(1); }
}

input[type="checkbox"]:hover, input[type="radio"]:hover {
  transform: scale(1.1);
}

input[type="checkbox"]:active, input[type="radio"]:active {
  transform: scale(0.9);
}

/* Transition for theme changes and general elements */
* {
  transition:
    background-color 0.3s var(--ease-out),
    color 0.3s var(--ease-out),
    border-color 0.3s var(--ease-out),
    opacity 0.3s var(--ease-out),
    transform 0.3s var(--ease-out);
}

/* Visually hidden class for screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
