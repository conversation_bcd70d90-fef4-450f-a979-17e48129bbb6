{% extends "base.html" %}

{% block title %}Remove Background - File Processing Hub{% endblock %}

{% block content %}
<style>
    /* Drop area styles */
    .drop-area {
        border: 2px dashed #4B5563;
        border-radius: 0.5rem;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .drop-area:hover, .drop-area.drag-active {
        border-color: #9333EA;
        background-color: rgba(147, 51, 234, 0.1);
    }

    /* Loading animation */
    .loading-spinner {
        display: inline-block;
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255,255,255,.3);
        border-radius: 50%;
        border-top-color: #9333EA; /* purple-600 */
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>

<div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center px-4 py-8">
    <div class="text-center max-w-xl mb-8 relative">
        <h1 class="text-3xl md:text-4xl font-bold text-white mb-4 inline-block relative">
            Remove Background
        </h1>
        <p class="text-gray-400">Remove the background from your images with AI-powered technology.</p>
    </div>

    <!-- Upload Form -->
    <div class="w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 form-container">
        <div id="toast-container" class="fixed bottom-4 right-4 space-y-2 z-50"></div>

        {% if error_message %}
        <div class="mb-6 p-4 bg-red-800 text-red-200 rounded-lg flex items-center">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ error_message }}
        </div>
        {% endif %}

        <form method="POST" action="{{ url_for('tools.remove_background_page') }}" enctype="multipart/form-data" id="remove-bg-form">
            {{ form.hidden_tag() }}

            <!-- Enhanced Drag-and-Drop Area -->
            <div id="drop-area" class="drop-area remove-bg" tabindex="0" role="button" aria-label="Drop image files here or click to browse">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-purple-300 mt-2">Drag & Drop your images here (max 5)</p>
                <p class="text-gray-400">or</p>
                {{ form.files(class="hidden", id="file-input", accept="image/*", multiple=True) }}
                <button type="button" onclick="document.getElementById('file-input').click()"
                        class="mt-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
                    Browse Files
                </button>
            </div>

            <div id="file-list" class="mt-4 space-y-2 hidden">
                <h3 class="text-sm font-medium text-gray-300">Selected Files:</h3>
                <ul class="list-disc list-inside text-gray-400"></ul>
            </div>

            <!-- Conversion Options - Improved for mobile -->
            <div class="space-y-4 mt-4">
                <div class="form-row">
                    <label for="output-format" class="block text-sm font-medium text-gray-300 mb-1">Output Format</label>
                    <select name="format" id="output-format" required
                            class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                        <option value="png">PNG (Recommended)</option>
                        <option value="webp">WebP</option>
                        <option value="jpg">JPG (No Transparency)</option>
                    </select>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="submit-button"
                    class="w-full px-4 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                    disabled>
                Remove Background
            </button>

            <!-- Progress Bar -->
            <div id="progress-container" class="hidden mt-4">
                <div class="flex justify-between text-sm text-gray-400 mb-1">
                    <span>Processing...</span>
                    <span id="progress-text">0%</span>
                </div>
                <div id="progress-bar" class="h-2 bg-purple-600 rounded-full transition-all duration-300" style="width: 0%;"></div>
            </div>
        </form>

        <!-- Results Section -->
        {% if results %}
        <div class="mt-8 border-t border-gray-700 pt-6">
            <h2 class="text-xl font-semibold text-white mb-4">Processed Images</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {% for result in results %}
                <div class="bg-gray-700 rounded-lg overflow-hidden">
                    <div class="p-4">
                        <h3 class="text-white font-medium truncate" title="{{ result.original_name }}">{{ result.original_name }}</h3>
                        <p class="text-gray-400 text-sm">{{ (result.size / 1024)|round(1) }} KB - {{ result.format|upper }}</p>
                        <a href="{{ result.download_url }}" download="{{ result.processed_name }}"
                           class="mt-2 inline-block px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 text-sm">
                            <i class="fas fa-download mr-1"></i> Download
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- JavaScript -->
<script src="{{ url_for('static', filename='js/remove_background.js') }}"></script>
{% endblock %}
