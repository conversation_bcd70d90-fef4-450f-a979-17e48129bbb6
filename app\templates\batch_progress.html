{% extends "base.html" %}

{% block title %}Batch Conversion Progress | File Processing Hub{% endblock %}
{% block meta %}
    <meta name="description" content="Track your batch file conversion progress in real-time">
    <meta name="keywords" content="batch conversion, file conversion progress, data processing">
    <meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}
{% block content %}
<style>
    /* File list styles */
    .file-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .file-item {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem;
        border-bottom: 1px solid #374151;
        transition: all 0.2s ease-in-out;
    }

    .file-item:hover {
        background-color: rgba(55, 65, 81, 0.5);
    }

    .file-item:last-child {
        border-bottom: none;
    }

    .file-item.success {
        border-left: 3px solid #10B981;
    }

    .file-item.error {
        border-left: 3px solid #EF4444;
    }

    .file-item.processing {
        border-left: 3px solid #F59E0B;
    }

    .file-item.canceled {
        border-left: 3px solid #6B7280;
    }

    .file-item.skipped {
        border-left: 3px solid #6B7280;
        opacity: 0.7;
    }

    /* File progress bar */
    .file-progress-bar {
        height: 4px;
        background-color: #374151;
        border-radius: 2px;
        overflow: hidden;
        margin-top: 0.5rem;
    }

    .file-progress-bar-inner {
        height: 100%;
        background-color: #3B82F6;
        transition: width 0.3s ease;
    }

    .file-progress-bar-inner.success {
        background-color: #10B981;
    }

    .file-progress-bar-inner.error {
        background-color: #EF4444;
    }

    .file-progress-bar-inner.processing {
        background-color: #F59E0B;
        background-image: linear-gradient(
            -45deg,
            rgba(255, 255, 255, 0.2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.2) 75%,
            transparent 75%,
            transparent
        );
        background-size: 30px 30px;
        animation: progress-bar-stripes 1s linear infinite;
    }

    @keyframes progress-bar-stripes {
        from { background-position: 30px 0; }
        to { background-position: 0 0; }
    }

    /* Step progress indicator labels */
    .step-label {
        font-size: 0.75rem;
        color: #9CA3AF;
        text-align: center;
        margin-top: 0.5rem;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .status-badge.success {
        background-color: rgba(16, 185, 129, 0.2);
        color: #10B981;
    }

    .status-badge.error {
        background-color: rgba(239, 68, 68, 0.2);
        color: #EF4444;
    }

    .status-badge.processing {
        background-color: rgba(245, 158, 11, 0.2);
        color: #F59E0B;
    }

    .status-badge.canceled {
        background-color: rgba(107, 114, 128, 0.2);
        color: #9CA3AF;
    }

    /* Action buttons */
    .action-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .action-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .action-button.retry {
        color: #3B82F6;
    }

    .action-button.cancel {
        color: #EF4444;
    }

    /* Tooltip */
    .tooltip {
        position: relative;
    }

    .tooltip:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
    }

    .tooltip-text {
        visibility: hidden;
        opacity: 0;
        width: 120px;
        background-color: #1F2937;
        color: #F3F4F6;
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        margin-left: -60px;
        transition: opacity 0.3s;
        font-size: 0.75rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .tooltip-text::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #1F2937 transparent transparent transparent;
    }
</style>

<div class="min-h-screen py-8 px-4">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">Batch Conversion Progress</h1>
        <p class="text-gray-400 max-w-2xl mx-auto">
            Your files are being processed. This page will update automatically.
        </p>
    </div>

    <!-- Progress Container -->
    <div class="w-full max-w-3xl mx-auto bg-gray-800 rounded-lg shadow-lg p-6">
        <!-- Progress Status -->
        <div id="progress-status" class="mb-4 text-center">
            <div id="progress-message" class="text-lg font-medium text-green-400">Initializing...</div>
            <div id="progress-detail" class="text-sm text-gray-400 mt-1">Please wait while we process your files</div>
        </div>

        <!-- Action Buttons (initially hidden) -->
        <div id="action-buttons" class="mb-4 flex justify-center space-x-4 hidden">
            <button id="cancel-button" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                Cancel Conversion
            </button>
            <button id="retry-all-button" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition flex items-center hidden">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Retry Failed Files
            </button>
        </div>

        <!-- Circular Progress Indicator -->
        <div id="circular-progress-container" class="mb-6"></div>

        <!-- Step Progress Indicator -->
        <div id="step-progress-container" class="mb-6"></div>

        <!-- Linear Progress Bar -->
        <div class="relative pt-1">
            <div class="flex mb-2 items-center justify-between">
                <div>
                    <span id="progress-percentage" class="text-xs font-semibold inline-block text-green-400">
                        0%
                    </span>
                </div>
                <div>
                    <span id="progress-time" class="text-xs font-semibold inline-block text-gray-400">
                        Estimating time...
                    </span>
                </div>
            </div>
            <div id="linear-progress-container"></div>
        </div>

        <!-- File Progress List (initially hidden) -->
        <div id="file-progress-container" class="mt-6 hidden">
            <h3 class="text-white font-medium mb-2">File Progress</h3>
            <div id="file-progress-list" class="bg-gray-700 rounded-lg file-list">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Results Container (initially hidden) -->
        <div id="results-container" class="result-container mt-6 hidden">
            <div class="mb-6 text-center">
                <svg class="success-icon mx-auto h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h2 class="mt-4 text-2xl font-semibold text-white">Conversion Complete</h2>
                <p id="conversion-summary" class="mt-2 text-gray-400">Processing complete</p>
            </div>

            <!-- Conversion Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div id="total-files-count" class="text-2xl font-bold text-white">0</div>
                    <div class="text-sm text-gray-400">Total Files</div>
                </div>
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div id="successful-files-count" class="text-2xl font-bold text-green-500">0</div>
                    <div class="text-sm text-gray-400">Successful</div>
                </div>
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div id="failed-files-count" class="text-2xl font-bold text-red-500">0</div>
                    <div class="text-sm text-gray-400">Failed</div>
                </div>
            </div>

            <!-- Successful Files -->
            <div id="successful-files-container" class="mb-4">
                <h3 class="text-green-400 font-medium mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Successful Conversions
                </h3>
                <div id="successful-files" class="bg-gray-700 rounded-lg p-2 file-list">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- Failed Files -->
            <div id="failed-files-container" class="mb-4 hidden">
                <h3 class="text-red-400 font-medium mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Failed Conversions
                </h3>
                <div id="failed-files" class="bg-gray-700 rounded-lg p-2 file-list">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>

            <!-- Next Steps Section -->
            <div class="next-steps mt-6 pt-6 border-t border-gray-700">
                <h4 class="next-steps-title text-lg font-semibold text-white mb-4">Next Steps</h4>
                <div class="next-steps-list space-y-4">
                    <div class="next-step-item flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="next-step-icon h-6 w-6 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                        </svg>
                        <div>
                            <h5 class="text-white font-medium">Download All Files</h5>
                            <p class="text-gray-400 text-sm">Download all successfully converted files as a single ZIP archive.</p>
                            <a id="download-all-btn" href="#" class="text-green-500 hover:text-green-400 text-sm inline-block mt-1">Download ZIP Archive →</a>
                        </div>
                    </div>
                    <div class="next-step-item flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="next-step-icon h-6 w-6 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        <div>
                            <h5 class="text-white font-medium">Convert More Files</h5>
                            <p class="text-gray-400 text-sm">Start a new batch conversion with different files or settings.</p>
                            <a href="{{ url_for('tools.batch_convert_page') }}" class="text-green-500 hover:text-green-400 text-sm inline-block mt-1">New Batch Conversion →</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                <a id="download-all-button" href="#" class="download-button text-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download All Files
                </a>
                <a href="{{ url_for('tools.batch_convert_page') }}" class="reset-button text-center px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Convert More Files
                </a>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const taskId = "{{ task_id }}";
    const progressPercentage = document.getElementById('progress-percentage');
    const progressMessage = document.getElementById('progress-message');
    const progressDetail = document.getElementById('progress-detail');
    const progressTime = document.getElementById('progress-time');
    const resultsContainer = document.getElementById('results-container');
    const successfulFiles = document.getElementById('successful-files');
    const failedFiles = document.getElementById('failed-files');
    const failedFilesContainer = document.getElementById('failed-files-container');
    const cancelButton = document.getElementById('cancel-button');
    const retryAllButton = document.getElementById('retry-all-button');

    let pollInterval;
    let completed = false;
    let failedFileIndices = [];

    // Set up event handlers for cancel and retry buttons
    cancelButton.addEventListener('click', function() {
        if (confirm('Are you sure you want to cancel this batch conversion?')) {
            cancelTask();
        }
    });

    retryAllButton.addEventListener('click', function() {
        if (failedFileIndices.length > 0) {
            retryFiles(failedFileIndices);
        } else {
            alert('No failed files to retry.');
        }
    });

    // Initialize progress indicators
    const circularProgress = new ProgressIndicator({
        type: 'circular',
        containerId: 'circular-progress-container',
        initialValue: 0
    });

    const linearProgress = new ProgressIndicator({
        type: 'linear',
        containerId: 'linear-progress-container',
        initialValue: 0
    });

    // Define the steps for batch processing
    const steps = [
        'Uploading',
        'Validating',
        'Processing',
        'Finalizing'
    ];

    const stepsIndicator = new ProgressIndicator({
        type: 'steps',
        containerId: 'step-progress-container',
        steps: steps.length,
        initialValue: 0
    });

    // Add step labels
    const stepContainer = document.getElementById('step-progress-container');
    const stepsDiv = document.createElement('div');
    stepsDiv.className = 'flex justify-between mt-2';

    steps.forEach(step => {
        const stepLabel = document.createElement('div');
        stepLabel.className = 'step-label';
        stepLabel.textContent = step;
        stepsDiv.appendChild(stepLabel);
    });

    stepContainer.appendChild(stepsDiv);

    // Function to update the progress UI
    function updateProgress(data) {
        const progress = data.progress || 0;
        const status = data.status || 'processing';
        const fileProgress = data.file_progress || {};

        // Update all progress indicators
        circularProgress.update(progress);
        linearProgress.update(progress);

        // Calculate which step we're on based on progress
        const currentStep = Math.ceil((progress / 100) * steps.length);
        const stepProgressValue = Math.min(100, Math.max(0, progress));

        // Update step progress
        stepsIndicator.update(stepProgressValue);

        // Update text displays
        progressPercentage.textContent = `${progress}%`;

        // Update message
        if (data.message) {
            progressMessage.textContent = data.message;

            // Update message color based on status
            progressMessage.className = 'text-lg font-medium';
            if (status === 'processing' || status === 'pending') {
                progressMessage.classList.add('text-green-400');
            } else if (status === 'canceling') {
                progressMessage.classList.add('text-yellow-400');
            } else if (status === 'canceled') {
                progressMessage.classList.add('text-gray-400');
            } else if (status === 'failed') {
                progressMessage.classList.add('text-red-400');
            } else if (status === 'completed') {
                progressMessage.classList.add('text-green-400');
            }
        }

        // Update estimated time
        if (data.estimated_time) {
            progressTime.textContent = data.estimated_time;
        }

        // Show/hide action buttons based on status
        const actionButtons = document.getElementById('action-buttons');
        const cancelButton = document.getElementById('cancel-button');
        const retryAllButton = document.getElementById('retry-all-button');

        if (status === 'processing' || status === 'pending') {
            actionButtons.classList.remove('hidden');
            cancelButton.classList.remove('hidden');
            retryAllButton.classList.add('hidden');
        } else if (status === 'failed' || status === 'partial') {
            actionButtons.classList.remove('hidden');
            cancelButton.classList.add('hidden');
            retryAllButton.classList.remove('hidden');
        } else if (status === 'canceling') {
            actionButtons.classList.remove('hidden');
            cancelButton.classList.remove('hidden');
            cancelButton.disabled = true;
            cancelButton.classList.add('opacity-50', 'cursor-not-allowed');
            retryAllButton.classList.add('hidden');
        } else {
            actionButtons.classList.add('hidden');
        }

        // Update file progress list if available
        if (Object.keys(fileProgress).length > 0) {
            updateFileProgressList(fileProgress);
        }

        // Check if task is complete
        if (progress >= 100 || status === 'completed' || status === 'failed' || status === 'canceled') {
            completed = true;
            clearInterval(pollInterval);
            fetchResults();
        }
    }

    // Function to update the file progress list
    function updateFileProgressList(fileProgress) {
        const fileProgressContainer = document.getElementById('file-progress-container');
        const fileProgressList = document.getElementById('file-progress-list');

        // Show the container
        fileProgressContainer.classList.remove('hidden');

        // Clear the list
        fileProgressList.innerHTML = '';

        // Sort files by index
        const sortedFiles = Object.entries(fileProgress)
            .sort((a, b) => parseInt(a[0]) - parseInt(b[0]));

        // Add each file to the list
        sortedFiles.forEach(([index, file]) => {
            const fileItem = document.createElement('div');
            fileItem.className = `file-item ${file.status || 'processing'}`;

            // Create status badge
            let statusBadge = '';
            if (file.status === 'success') {
                statusBadge = '<span class="status-badge success">Success</span>';
            } else if (file.status === 'failed') {
                statusBadge = '<span class="status-badge error">Failed</span>';
            } else if (file.status === 'processing') {
                statusBadge = '<span class="status-badge processing">Processing</span>';
            } else if (file.status === 'canceled') {
                statusBadge = '<span class="status-badge canceled">Canceled</span>';
            } else if (file.status === 'skipped') {
                statusBadge = '<span class="status-badge canceled">Skipped</span>';
            }

            // Create retry button for failed files
            let retryButton = '';
            if (file.status === 'failed') {
                retryButton = `
                    <button class="action-button retry" data-file-index="${index}" onclick="retryFile(${index})">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span class="tooltip">
                            <span class="tooltip-text">Retry this file</span>
                        </span>
                    </button>
                `;
            }

            // Create file item content
            fileItem.innerHTML = `
                <div class="flex justify-between items-center">
                    <div class="flex-1">
                        <div class="flex items-center">
                            <span class="text-gray-200 font-medium">${file.filename}</span>
                            <span class="ml-2">${statusBadge}</span>
                        </div>
                        ${file.error ? `<div class="text-xs text-red-400 mt-1">${file.error}</div>` : ''}
                    </div>
                    <div class="flex items-center">
                        ${retryButton}
                    </div>
                </div>
                <div class="file-progress-bar mt-1">
                    <div class="file-progress-bar-inner ${file.status || 'processing'}" style="width: ${file.progress || 0}%"></div>
                </div>
            `;

            fileProgressList.appendChild(fileItem);
        });
    }

    // Function to fetch task results
    function fetchResults() {
        fetch(`/api/task_result/${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    progressMessage.textContent = 'Error';
                    progressDetail.textContent = data.error;
                    progressMessage.classList.remove('text-green-400');
                    progressMessage.classList.add('text-red-400');
                    return;
                }

                // Show results container
                resultsContainer.classList.remove('hidden');

                // Update stats
                const totalFiles = data.total || 0;
                const successfulCount = data.successful || 0;
                const failedCount = data.failed || 0;

                document.getElementById('total-files-count').textContent = totalFiles;
                document.getElementById('successful-files-count').textContent = successfulCount;
                document.getElementById('failed-files-count').textContent = failedCount;

                // Update summary
                const conversionSummary = document.getElementById('conversion-summary');
                conversionSummary.textContent = `${successfulCount} of ${totalFiles} files converted successfully`;

                // Update successful files
                if (data.results && data.results.length > 0) {
                    successfulFiles.innerHTML = '';
                    data.results.forEach(file => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'file-card success';

                        // Get file extension
                        const fileExtension = file.output_filename.split('.').pop().toUpperCase();

                        // Create file item content
                        fileItem.innerHTML = `
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-gray-200 font-medium">${file.filename}</span>
                                    <div class="text-xs text-gray-400 mt-1">Converted to ${fileExtension}</div>
                                </div>
                                <a href="/download/${file.output_filename}"
                                   class="download-button flex items-center px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
                                   download="${file.output_filename}" data-filename="${file.output_filename}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                    </svg>
                                    Download
                                </a>
                            </div>
                            <div class="file-details">
                                <div class="file-details-row">
                                    <span class="file-details-label">Original</span>
                                    <span class="file-details-value">${file.filename}</span>
                                </div>
                                <div class="file-details-row">
                                    <span class="file-details-label">Converted</span>
                                    <span class="file-details-value">${file.output_filename}</span>
                                </div>
                                <div class="file-details-row">
                                    <span class="file-details-label">Format</span>
                                    <span class="file-details-value">${fileExtension}</span>
                                </div>
                            </div>
                        `;
                        successfulFiles.appendChild(fileItem);
                    });
                } else {
                    successfulFiles.innerHTML = '<div class="p-2 text-gray-400">No successful conversions</div>';
                }

                // Update failed files
                if (data.failed_files && data.failed_files.length > 0) {
                    failedFilesContainer.classList.remove('hidden');
                    failedFiles.innerHTML = '';

                    // Reset failed file indices
                    failedFileIndices = [];

                    data.failed_files.forEach((file, index) => {
                        // Store the index for retry functionality
                        failedFileIndices.push(index);

                        const fileItem = document.createElement('div');
                        fileItem.className = 'file-card error';
                        fileItem.innerHTML = `
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-gray-200 font-medium">${file.filename}</span>
                                    <div class="text-xs text-red-400 mt-1">Conversion failed</div>
                                </div>
                                <button class="action-button retry" onclick="retryFile(${index})">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                </button>
                            </div>
                            <div class="file-details">
                                <div class="file-details-row">
                                    <span class="file-details-label">Error</span>
                                    <span class="file-details-value text-red-400">${file.error}</span>
                                </div>
                            </div>
                        `;
                        failedFiles.appendChild(fileItem);
                    });

                    // Show retry all button if there are failed files
                    if (failedFileIndices.length > 0) {
                        retryAllButton.classList.remove('hidden');
                    }
                }

                // Update progress message
                progressMessage.textContent = 'Conversion Complete';
                progressDetail.textContent = `${successfulCount} of ${totalFiles} files converted successfully`;

                // Update download all buttons (both in next steps and main buttons)
                const downloadAllBtn = document.getElementById('download-all-btn');
                const downloadAllButton = document.getElementById('download-all-button');
                const downloadUrl = `/download_batch/${taskId}`;

                downloadAllBtn.href = downloadUrl;
                downloadAllButton.href = downloadUrl;

                // Initialize the result page enhancer
                new ResultPageEnhancer({
                    containerSelector: '.result-container',
                    toolType: 'batch',
                    onReset: null // We use direct links instead of JS reset
                });
            })
            .catch(error => {
                console.error('Error fetching results:', error);
                progressMessage.textContent = 'Error';
                progressDetail.textContent = 'Failed to fetch conversion results';
                progressMessage.classList.remove('text-green-400');
                progressMessage.classList.add('text-red-400');
            });
    }

    // Function to poll for progress updates
    function pollProgress() {
        fetch(`/api/task_progress/${taskId}`)
            .then(response => response.json())
            .then(data => {
                updateProgress(data);
            })
            .catch(error => {
                console.error('Error fetching progress:', error);
            });
    }

    // Initial poll and then set interval
    pollProgress();
    pollInterval = setInterval(pollProgress, 2000);

    // Clean up interval when page is unloaded
    window.addEventListener('beforeunload', function() {
        clearInterval(pollInterval);
    });

    // Function to cancel the current task
    function cancelTask() {
        fetch(`/api/task_cancel/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update UI to show cancellation in progress
                progressMessage.textContent = 'Canceling...';
                progressMessage.classList.remove('text-green-400');
                progressMessage.classList.add('text-yellow-400');
                progressDetail.textContent = 'Your request to cancel is being processed';

                // Disable cancel button
                cancelButton.disabled = true;
                cancelButton.classList.add('opacity-50', 'cursor-not-allowed');
            } else {
                alert('Failed to cancel task: ' + (data.error?.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error canceling task:', error);
            alert('Failed to cancel task. Please try again.');
        });
    }

    // Function to retry specific files
    function retryFiles(fileIndices) {
        fetch(`/api/task_retry/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                file_indices: fileIndices
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect to the new task progress page
                window.location.href = `/tools/batch_progress/${data.new_task_id}`;
            } else {
                alert('Failed to retry files: ' + (data.error?.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error retrying files:', error);
            alert('Failed to retry files. Please try again.');
        });
    }

    // Function to retry a single file
    function retryFile(fileIndex) {
        retryFiles([fileIndex]);
    }

    // Helper function to get CSRF token
    function getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }
});
</script>
{% endblock %}
