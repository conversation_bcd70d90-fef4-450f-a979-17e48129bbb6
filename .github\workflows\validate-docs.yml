name: Validate Documentation

on:
  push:
    branches: [ main, perfect-ten ]
    paths:
      - 'docs/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'docs/**'
  workflow_dispatch:

jobs:
  validate-docs:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install markdown jsonschema pyyaml
    
    - name: Validate API.md
      run: |
        python .github/scripts/validate_docs.py
