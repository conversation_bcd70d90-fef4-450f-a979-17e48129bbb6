"""
Utility modules for the application.
"""

from app.utils.performance import (
    versioned_url_for,
    get_file_hash,
    should_load_module,
    get_critical_css,
    get_non_critical_css,
    get_page_modules,
    get_current_page_modules
)

from app.utils.error_responses import create_error_response
from app.utils.constants import (
    ALLOWED_IMAGE_EXTENSIONS,
    ALLOWED_FILE_EXTENSIONS,
    MAX_IMAGE_SIZE,
    MAX_FILE_SIZE,
    MAX_BATCH_SIZE,
    MAX_PAGES
)

# Import utility functions from file_helpers.py
from app.utils.file_helpers import (
    allowed_file,
    check_file_size,
    secure_and_validate_filename,
    save_uploaded_file
)

# Import utility functions from utils.py
from app.utils.response_helpers import (
    generate_etag,
    add_cache_headers,
    add_etag_header
)

# Import logging utilities
from app.utils.logging import log_conversion
