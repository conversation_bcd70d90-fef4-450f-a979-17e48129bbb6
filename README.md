# ImgCipherF1

ImgCipherF1 is a comprehensive Flask-based web application for image and file processing, offering a variety of tools including image conversion, PDF manipulation, text extraction, and more.

![ImgCipherF1 Logo](app/static/imgcipher-seo.webp)

## Features

- **Image Conversion**: Convert images between various formats (WebP, JPG, PNG, GIF, ICO)
- **PDF Tools**: Split and merge PDF files
- **File Conversion**: Convert between data formats (CSV, Excel, JSON, XML, Parquet)
- **Text Extraction**: Extract text from images using OCR
- **Batch Processing**: Process multiple files simultaneously
- **User Authentication**: Secure user accounts with role-based access control
- **API Access**: RESTful API endpoints for programmatic access
- **Fun Zone**: Interactive features including polls, vibes, and tech history facts

## Table of Contents

- [Requirements](#requirements)
- [Installation](#installation)
- [Configuration](#configuration)
- [Running the Application](#running-the-application)
- [Testing](#testing)
- [Deployment](#deployment)
- [Project Structure](#project-structure)
- [API Documentation](#api-documentation)
- [Setup Scripts](#setup-scripts)
- [Contributing](#contributing)
- [License](#license)

## Requirements

- Python 3.9+
- Redis (for caching and Celery)
- SQLite (default) or PostgreSQL (production)
- Tesseract OCR (for text extraction)

## Installation

### Quick Setup (Recommended)

The easiest way to set up ImgCipherF1 is using the automated setup scripts:

#### Windows
```cmd
setup.bat
```

#### Unix/Linux/macOS
```bash
./setup.sh
```

#### Cross-platform (Python)
```bash
python setup.py --dev
```

### Manual Installation

If you prefer manual setup or the automated script fails:

#### Clone the Repository

```bash
git clone https://github.com/yourusername/ImgCipherF1.git
cd ImgCipherF1
```

#### Create a Virtual Environment

```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python -m venv venv
source venv/bin/activate
```

#### Install Dependencies

```bash
pip install -r requirements.txt
```

#### Install Tesseract OCR

For text extraction functionality, you need to install Tesseract OCR:

- **Windows**: Download and install from [Tesseract at UB Mannheim](https://github.com/UB-Mannheim/tesseract/wiki)
- **macOS**: `brew install tesseract`
- **Linux**: `sudo apt install tesseract-ocr`

For detailed setup instructions, see [SETUP.md](SETUP.md).

## Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///app.db
UPLOAD_FOLDER=data/uploads
OUTPUT_FOLDER=data/output
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CACHE_REDIS_URL=redis://localhost:6379/1
```

### Database Initialization

Initialize the database and apply migrations:

```bash
flask db upgrade
```

### Create Admin User

Create an admin user for the application:

```bash
flask create-admin --username admin --email <EMAIL> --password secure-password
```

## Running the Application

### Start Redis Server

```bash
# Windows (if installed via WSL or Docker)
# Start Docker Desktop or WSL Redis service

# macOS/Linux
redis-server
```

### Start Celery Worker

```bash
# In a separate terminal
celery -A app.celery worker --loglevel=info
```

### Run the Flask Application

```bash
# Development mode
flask run

# Or using Python
python run.py
```

The application will be available at http://localhost:5000

## Testing

### Run Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_routes.py

# Run with coverage report
pytest --cov=app tests/
```

### Test Environment

Tests use an in-memory SQLite database and mock Redis/Celery services.

## Deployment

### Docker Deployment

A Dockerfile and docker-compose.yml are provided for containerized deployment:

```bash
# Build and start containers
docker-compose up -d

# View logs
docker-compose logs -f

# Stop containers
docker-compose down
```

### Traditional Deployment

For deployment on a traditional server:

1. Set up a production WSGI server (Gunicorn, uWSGI)
2. Configure a reverse proxy (Nginx, Apache)
3. Set up Redis for caching and Celery
4. Configure a production database (PostgreSQL recommended)

Example Gunicorn configuration:

```bash
gunicorn -w 4 -b 127.0.0.1:8000 "app:create_app()"
```

Example Nginx configuration:

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /static {
        alias /path/to/ImgCipherF1/app/static;
    }
}
```

## Project Structure

```
ImgCipherF1/
├── app/                    # Application package
│   ├── __init__.py         # Application factory
│   ├── cache.py            # Caching configuration
│   ├── config.py           # Configuration
│   ├── decorators.py       # Custom decorators
│   ├── extensions.py       # Flask extensions
│   ├── tasks.py            # Celery tasks
│   ├── utils.py            # Utility functions
│   ├── forms/              # Form definitions
│   ├── models/             # Database models
│   ├── routes/             # Route blueprints
│   ├── static/             # Static files
│   ├── templates/          # Jinja2 templates
│   └── tools/              # Tool implementations
├── data/                   # Data storage
│   ├── uploads/            # Uploaded files
│   └── output/             # Processed files
├── docs/                   # Documentation
├── migrations/             # Database migrations
├── tests/                  # Test suite
├── .env                    # Environment variables
├── requirements.txt        # Dependencies
└── run.py                  # Application entry point
```

## API Documentation

The application provides several API endpoints for programmatic access:

- `/tools/api/confession` - Manage confessions
- `/tools/api/poll` - Access poll data
- `/tools/api/vibe` - Get and update vibes
- `/tools/api/history` - Get tech history facts
- `/tools/api/task_progress/<task_id>` - Check task progress
- `/tools/api/task_result/<task_id>` - Get task results

For detailed API documentation, see the [API.md](docs/API.md) file.

## Setup Scripts

The project includes comprehensive setup scripts for easy installation:

- **`setup.py`** - Main Python setup script (cross-platform)
- **`setup.bat`** - Windows batch file wrapper
- **`setup.sh`** - Unix/Linux/macOS shell script wrapper
- **`Makefile`** - Development workflow automation

### Setup Options

```bash
# Development setup (includes dev dependencies and testing)
python setup.py --dev

# Production setup (optimized for production)
python setup.py --prod

# Docker setup (configured for containers)
python setup.py --docker

# Skip specific steps
python setup.py --dev --skip-venv --skip-db

# Force overwrite existing files
python setup.py --dev --force
```

### Development Workflow

Use the Makefile for common development tasks:

```bash
make setup-dev     # Setup development environment
make run           # Run the application
make test          # Run tests
make lint          # Check code quality
make format        # Format code
make clean         # Clean temporary files
```

For detailed setup instructions, see [SETUP.md](SETUP.md) and [docs/setup_scripts.md](docs/setup_scripts.md).

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
