"""
Feedback routes for ImgCipherF1 application.
Handles user feedback and usability testing.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

from flask import (
    Blueprint, render_template, request, jsonify, session, 
    current_app, redirect, url_for, flash
)
from flask_login import current_user, login_required
from werkzeug.exceptions import BadRequest

from app.extensions import db
from app.models.feedback import Feedback, UsabilityTest
from app.decorators import role_required

# Create blueprint
feedback_bp = Blueprint("feedback", __name__, url_prefix="/feedback")


@feedback_bp.before_request
def ensure_session_id() -> None:
    """Ensure that a session ID exists for tracking feedback."""
    if "session_id" not in session:
        session["session_id"] = str(uuid.uuid4())


@feedback_bp.route("/submit", methods=["POST"])
def submit_feedback() -> Dict[str, Any]:
    """Submit feedback from any page."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Required fields
        page_url = data.get("page_url")
        feedback_type = data.get("feedback_type")
        
        if not page_url or not feedback_type:
            return jsonify({"error": "Missing required fields"}), 400
        
        # Optional fields
        rating = data.get("rating")
        comment = data.get("comment")
        metadata = data.get("metadata", {})
        browser_info = data.get("browser_info")
        device_type = data.get("device_type")
        screen_size = data.get("screen_size")
        
        # Create feedback
        feedback = Feedback.create(
            session_id=session["session_id"],
            page_url=page_url,
            feedback_type=feedback_type,
            rating=rating,
            comment=comment,
            metadata=metadata,
            browser_info=browser_info,
            device_type=device_type,
            screen_size=screen_size,
        )
        
        return jsonify({
            "success": True,
            "message": "Feedback submitted successfully",
            "feedback_id": feedback.id
        })
    
    except Exception as e:
        current_app.logger.error(f"Error submitting feedback: {str(e)}")
        return jsonify({"error": "An error occurred while submitting feedback"}), 500


@feedback_bp.route("/usability-test", methods=["GET"])
def usability_test_page() -> str:
    """Render the usability test page."""
    # Generate a test ID if not already in session
    if "test_id" not in session:
        session["test_id"] = str(uuid.uuid4())
    
    # Get test scenarios
    test_scenarios = get_test_scenarios()
    
    return render_template(
        "usability_test.html",
        test_scenarios=test_scenarios,
        test_id=session["test_id"]
    )


@feedback_bp.route("/start-test", methods=["POST"])
def start_usability_test() -> Dict[str, Any]:
    """Start a new usability test session."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        test_name = data.get("test_name", "General Usability Test")
        browser_info = data.get("browser_info")
        device_type = data.get("device_type")
        screen_size = data.get("screen_size")
        tasks_total = data.get("tasks_total", 0)
        
        # Create test session
        test = UsabilityTest(
            user_id=current_user.id if not current_user.is_anonymous else None,
            session_id=session["session_id"],
            test_name=test_name,
            browser_info=browser_info,
            device_type=device_type,
            screen_size=screen_size,
            tasks_total=tasks_total
        )
        
        db.session.add(test)
        db.session.commit()
        
        session["usability_test_id"] = test.id
        
        return jsonify({
            "success": True,
            "message": "Usability test started",
            "test_id": test.id
        })
    
    except Exception as e:
        current_app.logger.error(f"Error starting usability test: {str(e)}")
        return jsonify({"error": "An error occurred while starting the test"}), 500


@feedback_bp.route("/update-test", methods=["POST"])
def update_usability_test() -> Dict[str, Any]:
    """Update an existing usability test session."""
    try:
        if "usability_test_id" not in session:
            return jsonify({"error": "No active test session"}), 400
        
        test_id = session["usability_test_id"]
        test = UsabilityTest.query.get(test_id)
        
        if not test:
            return jsonify({"error": "Test session not found"}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        status = data.get("status")
        tasks_completed = data.get("tasks_completed")
        tasks_total = data.get("tasks_total")
        satisfaction_score = data.get("satisfaction_score")
        
        if status == "completed":
            test.complete(satisfaction_score)
        elif status == "abandoned":
            test.abandon()
        
        if tasks_completed is not None and tasks_total is not None:
            test.update_progress(tasks_completed, tasks_total)
        
        return jsonify({
            "success": True,
            "message": "Test updated successfully"
        })
    
    except Exception as e:
        current_app.logger.error(f"Error updating usability test: {str(e)}")
        return jsonify({"error": "An error occurred while updating the test"}), 500


@feedback_bp.route("/dashboard", methods=["GET"])
@login_required
@role_required("admin")
def feedback_dashboard() -> str:
    """Admin dashboard for viewing feedback and usability test results."""
    feedback_items = Feedback.get_recent_feedback(100)
    feedback_stats = Feedback.get_feedback_stats()
    
    # Get test statistics
    test_stats = {
        "total_tests": UsabilityTest.query.count(),
        "completed_tests": UsabilityTest.query.filter_by(status="completed").count(),
        "abandoned_tests": UsabilityTest.query.filter_by(status="abandoned").count(),
        "avg_completion_time": db.session.query(db.func.avg(UsabilityTest.completion_time)).filter(
            UsabilityTest.status == "completed"
        ).scalar() or 0,
        "avg_satisfaction": db.session.query(db.func.avg(UsabilityTest.satisfaction_score)).filter(
            UsabilityTest.satisfaction_score.isnot(None)
        ).scalar() or 0,
    }
    
    return render_template(
        "feedback_dashboard.html",
        feedback_items=feedback_items,
        feedback_stats=feedback_stats,
        test_stats=test_stats
    )


def get_test_scenarios() -> List[Dict[str, Any]]:
    """Get test scenarios for usability testing."""
    return [
        {
            "id": "pdf-split",
            "name": "PDF Splitting Task",
            "description": "Try to split a PDF file into individual pages.",
            "steps": [
                "Navigate to the PDF tools section",
                "Select the Split PDF option",
                "Upload a PDF file",
                "Specify pages to extract (e.g., 1-3)",
                "Submit the form and download the result"
            ],
            "success_criteria": "Successfully downloaded a split PDF file"
        },
        {
            "id": "image-convert",
            "name": "Image Conversion Task",
            "description": "Convert an image from one format to another.",
            "steps": [
                "Navigate to the Images tools section",
                "Select the Convert Image option",
                "Upload an image file",
                "Select a target format (e.g., PNG to JPG)",
                "Submit the form and download the converted image"
            ],
            "success_criteria": "Successfully downloaded a converted image"
        },

        {
            "id": "batch-convert",
            "name": "Batch Conversion Task",
            "description": "Convert multiple files at once (requires login).",
            "steps": [
                "Log in to your account (or register if needed)",
                "Navigate to the Data tools section",
                "Select the Batch Convert option",
                "Upload multiple files",
                "Select conversion options",
                "Submit and monitor progress",
                "Download the converted files"
            ],
            "success_criteria": "Successfully converted and downloaded multiple files"
        }
    ]
