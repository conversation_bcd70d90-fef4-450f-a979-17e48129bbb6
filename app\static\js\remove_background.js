document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const form = document.getElementById('remove-bg-form');
    const fileInput = document.getElementById('file-input');
    const dropArea = document.getElementById('drop-area');
    const fileList = document.getElementById('file-list');
    const fileListUl = fileList.querySelector('ul');
    const submitButton = document.getElementById('submit-button');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');

    // Constants
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    const MAX_FILES = 5;

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });

    // Handle dropped files
    dropArea.addEventListener('drop', handleDrop, false);

    // Handle selected files from file input
    fileInput.addEventListener('change', handleFiles, false);

    // Handle form submission
    if (form) {
        form.addEventListener('submit', handleSubmit, false);
    }

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight() {
        dropArea.classList.add('border-purple-500');
        dropArea.classList.add('bg-gray-700');
    }

    function unhighlight() {
        dropArea.classList.remove('border-purple-500');
        dropArea.classList.remove('bg-gray-700');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles({target: {files: files}});
    }

    function handleFiles(e) {
        const files = e.target.files;
        
        // Check if too many files are selected
        if (files.length > MAX_FILES) {
            showToast(`Too many files selected. Maximum ${MAX_FILES} allowed.`, true);
            fileInput.value = '';
            return;
        }
        
        updateFileList(files);
        
        // Enable submit button if files are selected
        submitButton.disabled = files.length === 0;
    }

    function updateFileList(files) {
        if (files.length > 0) {
            fileList.classList.remove('hidden');
            fileListUl.innerHTML = '';
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                
                // Check file type
                if (!file.type.startsWith('image/')) {
                    showToast(`File "${file.name}" is not an image.`, true);
                    continue;
                }
                
                // Check file size
                if (file.size > MAX_FILE_SIZE) {
                    showToast(`File "${file.name}" exceeds the maximum size of 10MB.`, true);
                    continue;
                }
                
                const li = document.createElement('li');
                li.className = 'text-sm';
                li.textContent = `${file.name} (${formatFileSize(file.size)})`;
                fileListUl.appendChild(li);
            }
            
            // If no valid files were added to the list
            if (fileListUl.children.length === 0) {
                fileList.classList.add('hidden');
                submitButton.disabled = true;
                fileInput.value = '';
            }
        } else {
            fileList.classList.add('hidden');
            submitButton.disabled = true;
        }
    }

    function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' bytes';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
        else return (bytes / 1048576).toFixed(1) + ' MB';
    }

    function handleSubmit(e) {
        if (fileInput.files.length === 0) {
            e.preventDefault();
            showToast('Please select at least one image file.', true);
            return;
        }

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = `
            <div class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
            </div>
        `;

        // Show progress bar
        progressContainer.classList.remove('hidden');
        simulateProgress();

        // Let the form submit normally
    }

    function simulateProgress() {
        let progress = 0;
        const interval = setInterval(() => {
            progress += 5;
            
            // Cap at 90% - the last 10% will happen when the server responds
            if (progress >= 90) {
                progress = 90;
                clearInterval(interval);
            }
            
            updateProgress(progress);
        }, 500);
    }

    function updateProgress(percent) {
        progressBar.style.width = `${percent}%`;
        progressText.textContent = `${percent}%`;
    }

    function showToast(message, isError = false) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'fixed bottom-4 right-4 z-50 space-y-2';
            document.body.appendChild(toastContainer);
        }

        // Create toast
        const toast = document.createElement('div');
        toast.className = `${isError ? 'bg-red-800' : 'bg-purple-800'} text-white px-4 py-2 rounded-lg shadow-lg mb-2 flex items-center`;
        toast.innerHTML = `
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${isError ? 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' : 'M5 13l4 4L19 7'}"></path>
            </svg>
            <span>${message}</span>
        `;

        // Add to container
        toastContainer.appendChild(toast);

        // Remove after 3 seconds
        setTimeout(() => {
            toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(() => {
                toast.remove();
            }, 500);
        }, 3000);
    }
});
