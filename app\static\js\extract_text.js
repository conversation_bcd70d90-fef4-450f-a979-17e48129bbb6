
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const form = document.getElementById('extract-text-form');
    const fileInput = document.getElementById('file-input');
    const dropArea = document.getElementById('drop-area');
    const fileDetails = document.getElementById('file-details');
    const noFileSelected = document.getElementById('no-file-selected');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const sizeWarning = document.getElementById('size-warning');
    const submitButton = document.getElementById('submit-button');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const removeFileBtn = document.getElementById('remove-file');
    const imagePreviewContainer = document.getElementById('image-preview-container');
    const imagePreview = document.getElementById('image-preview');
    const errorContainer = document.getElementById('error-container');
    const filePreviewDisplay = document.getElementById('file-preview-display');

    // File preview handler
    let filePreviewHandler;

    // Constants
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    const LARGE_FILE_SIZE = 2 * 1024 * 1024; // 2MB

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => highlightDropArea(true), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => highlightDropArea(false), false);
    });

    // Handle dropped files
    dropArea.addEventListener('drop', handleDrop, false);

    // Handle file selection via input
    fileInput.addEventListener('change', handleFiles, false);

    // Handle remove file button
    removeFileBtn.addEventListener('click', resetFileInput, false);

    // Handle form submission
    form.addEventListener('submit', handleSubmit, false);

    // Make drop area clickable
    dropArea.addEventListener('click', () => fileInput.click());

    // Add keyboard support for drop area
    dropArea.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            fileInput.click();
        }
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlightDropArea(highlight) {
        dropArea.classList.toggle('drag-active', highlight);

        // Update screen reader status
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = highlight ?
                "Image is being dragged over the drop area" :
                fileInput.files.length > 0 ? `File selected: ${fileInput.files[0].name}` : "No image selected";
        }
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            handleFiles();
        }
    }

    function handleFiles() {
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];

            // Validate file type
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/tiff', 'image/bmp'];
            if (!validTypes.includes(file.type)) {
                showError('Please select a valid image file (JPG, PNG, GIF, WebP, TIFF, BMP)');
                resetFileInput();
                return;
            }

            // Validate file size
            if (file.size > MAX_FILE_SIZE) {
                showError(`File size exceeds the maximum limit of ${formatFileSize(MAX_FILE_SIZE)}`);
                resetFileInput();
                return;
            }

            // Show file details
            fileDetails.classList.remove('hidden');
            noFileSelected.classList.add('hidden');
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);

            // Show warning for large files
            sizeWarning.classList.toggle('hidden', file.size <= LARGE_FILE_SIZE);

            // Enable submit button
            submitButton.disabled = false;

            // Show image preview
            previewImage(file);

            // Update screen reader status
            const statusElement = document.getElementById('file-upload-status');
            if (statusElement) {
                statusElement.textContent = `File selected: ${file.name}, size: ${formatFileSize(file.size)}`;
            }

            // Clear any previous errors
            clearError();
        } else {
            resetFileInput();
        }
    }

    function previewImage(file) {
        // Show the legacy image preview for backward compatibility
        const reader = new FileReader();
        reader.onload = function(e) {
            imagePreview.src = e.target.result;
            imagePreviewContainer.classList.remove('hidden');
        };
        reader.onerror = function() {
            imagePreviewContainer.classList.add('hidden');
        };
        reader.readAsDataURL(file);

        // Initialize the enhanced file preview if available
        if (filePreviewDisplay) {
            // Initialize file preview handler if not already done
            if (!filePreviewHandler) {
                filePreviewHandler = new FilePreview({
                    containerId: 'file-preview-display',
                    onPreviewReady: (file, type) => {
                        // Show the preview container
                        filePreviewDisplay.classList.remove('hidden');

                        // Add close button if not already present
                        if (!document.getElementById('preview-close-button')) {
                            const closeButton = document.createElement('button');
                            closeButton.id = 'preview-close-button';
                            closeButton.className = 'absolute top-2 right-2 bg-gray-800 bg-opacity-75 rounded-full p-1 text-gray-300 hover:text-white transition-colors';
                            closeButton.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            `;
                            closeButton.addEventListener('click', () => {
                                filePreviewDisplay.classList.add('hidden');
                            });
                            filePreviewDisplay.appendChild(closeButton);
                        }
                    },
                    onError: (message) => {
                        showError(message);
                    }
                });
            }

            // Preview the file
            filePreviewHandler.previewFile(file);
        }
    }

    function resetFileInput() {
        fileInput.value = '';
        fileDetails.classList.add('hidden');
        noFileSelected.classList.remove('hidden');
        imagePreviewContainer.classList.add('hidden');
        submitButton.disabled = true;
        clearError();

        // Clear enhanced file preview if available
        if (filePreviewDisplay) {
            filePreviewDisplay.classList.add('hidden');
            if (filePreviewHandler) {
                filePreviewHandler.clearPreview();
            }
        }

        // Update screen reader status
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = "No image selected";
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showError(message) {
        // Update the file upload status for screen readers
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = `Error: ${message}`;
        }

        errorContainer.innerHTML = `
            <div class="error-message">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="error-message-content">
                    <div class="error-message-title">Error</div>
                    <div class="error-message-text">${message}</div>
                </div>
            </div>
        `;
        errorContainer.classList.remove('hidden');
    }

    function clearError() {
        errorContainer.innerHTML = '';
        errorContainer.classList.add('hidden');
    }

    function handleSubmit(e) {
        e.preventDefault();

        if (!fileInput.files.length) {
            showError('Please select an image file');
            return;
        }

        // Create a FormData object to submit the form data
        const formData = new FormData(form);
        const file = fileInput.files[0];

        // Show button loading state (spinner in button)
        setLoadingState(true);

        // Start progress bar animation
        simulateProgress();

        // Hide any existing loading overlay
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.classList.add('hidden');

        // Use fetch API to submit the form asynchronously
        fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData,
            // Don't follow redirects - we'll handle the response ourselves
            redirect: 'manual'
        })
        .then(response => {
            // If we get a redirect, follow it (this is how Flask typically handles form submissions)
            if (response.type === 'opaqueredirect') {
                window.location.href = window.location.href;
                return null;
            }
            return response.text();
        })
        .then(html => {
            if (html) {
                // Hide the loading overlay immediately
                loadingOverlay.classList.add('hidden');

                // Reload the page to show results
                window.location.href = window.location.href;
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);

            // Hide loading overlay immediately
            loadingOverlay.classList.add('hidden');

            // Show error message
            showError('An error occurred while processing your request. Please try again.');

            // Reset loading state
            setLoadingState(false);
        });

        // For large files, show the overlay after a delay
        if (file.size > LARGE_FILE_SIZE) {
            setTimeout(() => {
                // Only show if we're still processing
                if (submitButton.disabled) {
                    loadingOverlay.classList.remove('hidden');
                }
            }, 1500);
        }
    }

    function setLoadingState(isLoading) {
        if (isLoading) {
            // Disable the submit button and show spinner
            submitButton.disabled = true;
            submitButton.innerHTML = `
                <div class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                </div>
            `;

            // Show progress bar
            progressContainer.classList.remove('hidden');

            // For large files, prepare a more detailed message
            if (fileInput.files.length > 0 && fileInput.files[0].size > LARGE_FILE_SIZE) {
                const loadingText = document.querySelector('.loading-text');
                if (loadingText) {
                    loadingText.textContent = 'Processing large image...';
                }
            }

            // Disable form inputs during processing
            fileInput.disabled = true;
            const languageSelect = document.getElementById('language');
            if (languageSelect) {
                languageSelect.disabled = true;
            }

            // Disable the remove file button
            if (removeFileBtn) {
                removeFileBtn.disabled = true;
                removeFileBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        } else {
            // Re-enable the submit button
            submitButton.disabled = false;
            submitButton.textContent = 'Extract Text';

            // Hide progress indicators
            progressContainer.classList.add('hidden');
            document.getElementById('loading-overlay').classList.add('hidden');

            // Re-enable form inputs
            fileInput.disabled = false;
            const languageSelect = document.getElementById('language');
            if (languageSelect) {
                languageSelect.disabled = false;
            }

            // Re-enable the remove file button
            if (removeFileBtn) {
                removeFileBtn.disabled = false;
                removeFileBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }
    }

    function simulateProgress() {
        let progress = 0;
        const interval = setInterval(() => {
            // For large files, progress more slowly
            const increment = fileInput.files[0].size > LARGE_FILE_SIZE ? 5 : 10;
            progress += increment;

            // Cap at 90% - the last 10% will happen when the server responds
            if (progress >= 90) {
                progress = 90;
                clearInterval(interval);
            }

            updateProgress(progress);
        }, 500);
    }

    function updateProgress(percent) {
        progressBar.style.width = `${percent}%`;
        progressText.textContent = `${percent}%`;
    }
});

// Functions for handling extracted text
function copyToClipboard() {
    const text = document.getElementById('extracted-text').innerText;
    navigator.clipboard.writeText(text).then(() => {
        // Show toast notification
        showToast('Text copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy text: ', err);
        showToast('Failed to copy text', true);
    });
}

function downloadText() {
    const text = document.getElementById('extracted-text').innerText;
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted_text.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show toast notification
    showToast('Text downloaded successfully!');
}

function showToast(message, isError = false) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed bottom-4 right-4 z-50';
        document.body.appendChild(toastContainer);
    }

    // Create toast
    const toast = document.createElement('div');
    toast.className = `${isError ? 'bg-red-800' : 'bg-purple-800'} text-white px-4 py-2 rounded-lg shadow-lg mb-2 flex items-center`;
    toast.innerHTML = `
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${isError ? 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' : 'M5 13l4 4L19 7'}"></path>
        </svg>
        <span>${message}</span>
    `;

    // Add to container
    toastContainer.appendChild(toast);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
        setTimeout(() => {
            toast.remove();
        }, 500);
    }, 3000);
}