"""
Form classes for image operations.
"""

from flask_wtf import <PERSON>laskForm
from flask_wtf.file import <PERSON>Field, FileRequired
from wtforms import SelectField, IntegerField, SubmitField
from wtforms.validators import InputRequired, NumberRange, DataRequired


class ImageUploadForm(FlaskForm):
    """Form for image conversion."""
    
    files = FileField("Upload Image", validators=[InputRequired()])
    format = SelectField(
        "Output Format",
        choices=[("webp", "WebP"), ("jpg", "JPG"), ("png", "PNG"), ("gif", "GIF"), ("ico", "ICO")],
        validators=[InputRequired()],
    )
    quality = IntegerField("Quality (0-100)", default=80, validators=[NumberRange(min=0, max=100)])
    submit = SubmitField("Convert Image")


class RemoveBackgroundForm(FlaskForm):
    """Form for background removal."""
    
    files = FileField("Upload Image", validators=[InputRequired()])
    format = SelectField(
        "Output Format",
        choices=[("png", "PNG"), ("webp", "WebP"), ("jpg", "JPG")],
        default="png",
        validators=[InputRequired()],
    )
    submit = SubmitField("Remove Background")


class TextExtractionForm(FlaskForm):
    """Form for text extraction from images."""
    
    image_file = FileField("Upload Image", validators=[FileRequired(message="Please select an image file")])
    language = SelectField(
        "OCR Language",
        choices=[
            ("eng", "English"),
            ("fra", "French"),
            ("deu", "German"),
            ("spa", "Spanish"),
            ("ita", "Italian"),
            ("por", "Portuguese"),
            ("chi_sim", "Chinese (Simplified)"),
            ("jpn", "Japanese"),
            ("kor", "Korean"),
        ],
        default="eng",
        validators=[DataRequired(message="Please select a language")],
    )
    submit = SubmitField("Extract Text")
