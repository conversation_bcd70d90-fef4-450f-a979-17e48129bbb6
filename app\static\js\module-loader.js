/**
 * Module Loader
 * Provides functionality for loading JavaScript modules on demand
 */

(function() {
  'use strict';
  
  // Module registry to keep track of loaded modules
  const moduleRegistry = {};
  
  // Function to load a module
  function loadModule(moduleName, options = {}) {
    // Default options
    const defaults = {
      async: true,
      defer: true,
      onLoad: null,
      onError: null
    };
    
    // Merge options with defaults
    const settings = Object.assign({}, defaults, options);
    
    // Check if module is already loaded
    if (moduleRegistry[moduleName]) {
      if (typeof settings.onLoad === 'function') {
        settings.onLoad(moduleRegistry[moduleName]);
      }
      return Promise.resolve(moduleRegistry[moduleName]);
    }
    
    // Create a promise to load the module
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = `/static/js/${moduleName}.js`;
      script.async = settings.async;
      script.defer = settings.defer;
      
      // Set up load event
      script.onload = function() {
        console.log(`Module ${moduleName} loaded successfully`);
        
        // Store the module in the registry
        moduleRegistry[moduleName] = window[moduleName] || true;
        
        // Call the onLoad callback if provided
        if (typeof settings.onLoad === 'function') {
          settings.onLoad(moduleRegistry[moduleName]);
        }
        
        resolve(moduleRegistry[moduleName]);
      };
      
      // Set up error event
      script.onerror = function(error) {
        console.error(`Failed to load module ${moduleName}`, error);
        
        // Call the onError callback if provided
        if (typeof settings.onError === 'function') {
          settings.onError(error);
        }
        
        reject(error);
      };
      
      // Add the script to the document
      document.head.appendChild(script);
    });
  }
  
  // Function to load multiple modules
  function loadModules(moduleNames, options = {}) {
    const promises = moduleNames.map(moduleName => loadModule(moduleName, options));
    return Promise.all(promises);
  }
  
  // Function to check if a module is loaded
  function isModuleLoaded(moduleName) {
    return !!moduleRegistry[moduleName];
  }
  
  // Function to get a loaded module
  function getModule(moduleName) {
    return moduleRegistry[moduleName] || null;
  }
  
  // Expose the API to the global scope
  window.ModuleLoader = {
    load: loadModule,
    loadMany: loadModules,
    isLoaded: isModuleLoaded,
    get: getModule
  };
  
  // Auto-load modules based on data attributes
  document.addEventListener('DOMContentLoaded', function() {
    // Find elements with data-module attribute
    const moduleElements = document.querySelectorAll('[data-module]');
    
    moduleElements.forEach(function(element) {
      const moduleName = element.dataset.module;
      
      // Check if the module should be loaded immediately
      const loadImmediately = element.dataset.moduleLoad === 'immediate';
      
      if (loadImmediately) {
        // Load the module immediately
        loadModule(moduleName, {
          onLoad: function(module) {
            // Initialize the module if it has an init function
            if (module && typeof module.init === 'function') {
              module.init(element);
            }
          }
        });
      } else {
        // Set up intersection observer for lazy loading
        const observer = new IntersectionObserver(function(entries, observer) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              loadModule(moduleName, {
                onLoad: function(module) {
                  // Initialize the module if it has an init function
                  if (module && typeof module.init === 'function') {
                    module.init(element);
                  }
                }
              });
              
              // Stop observing once the module is loaded
              observer.unobserve(element);
            }
          });
        }, {
          rootMargin: '200px 0px', // Start loading when element is 200px from viewport
          threshold: 0.01 // Trigger when at least 1% of the element is visible
        });
        
        // Start observing the element
        observer.observe(element);
      }
    });
  });
})();
