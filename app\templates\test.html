<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{% block title %}Code Scrapyard{% endblock %}</title>
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='filecipher-01.png') }}">
  <!-- Tailwind CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
  <!-- FontAwesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/fun-zone.css') }}">
</head>

<body class="flex flex-col min-h-screen bg-gray-900 text-white">
    <nav class="fixed top-0 left-0 w-full z-50 flex items-center justify-between py-3 px-6 bg-gray-800 shadow-md">
      <div class="flex items-center">
        <a href="{{ url_for('main.index') }}">
          <img src="{{ url_for('static', filename='filecipher-01.png') }}" alt="Logo" class="h-10 mr-4">
        </a>
      </div>
      <div class="hidden md:flex space-x-4">
        <a href="{{ url_for('tools.split_pdf_page') }}" 
           class="menu-item text-gray-300 font-semibold px-4 py-2 rounded hover:bg-blue-700 hover:text-white {% if request.path == url_for('tools.split_pdf_page') %}active-page bg-blue-700{% endif %}">
            <i class="fas fa-cut mr-2"></i>Split PDF
        </a>
        <a href="{{ url_for('tools.merge_pdf_page') }}" 
           class="menu-item text-gray-300 font-semibold px-4 py-2 rounded hover:bg-red-700 hover:text-white {% if request.path == url_for('tools.merge_pdf_page') %}active-page bg-red-700{% endif %}">
            <i class="fas fa-object-group mr-2"></i>Merge PDF
        </a>
        <a href="{{ url_for('tools.image_converter_page') }}" 
           class="menu-item text-gray-300 font-semibold px-4 py-2 rounded hover:bg-yellow-600 hover:text-white {% if request.path.startswith(url_for('tools.image_converter_page')) %}active-page bg-yellow-600{% endif %}">
            <i class="fas fa-exchange-alt mr-2"></i>Convert Image
        </a>
        <a href="{{ url_for('tools.convert_file_page') }}" 
           class="menu-item text-gray-300 font-semibold px-4 py-2 rounded hover:bg-green-700 hover:text-white {% if request.path == url_for('tools.convert_file_page') %}active-page bg-green-700{% endif %}">
            <i class="fas fa-file-export mr-2"></i>Convert Files
        </a>
      </div>
      <button id="mobile-menu-button" class="md:hidden text-gray-300 px-3 py-2 focus:outline-none">☰</button>
    </nav>
  
    <!-- Mobile Menu -->
    <div id="mobile-menu" class="fixed top-0 right-0 h-full w-64 bg-gray-800 shadow-lg transform translate-x-full mobile-menu p-6 flex flex-col space-y-4 z-60">
      <button id="close-mobile-menu" class="text-gray-300 self-end mb-4 focus:outline-none">✕</button>
      <a href="{{ url_for('tools.image_converter_page') }}" 
         class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-yellow-600 hover:text-white transition-colors {% if request.path == url_for('tools.image_converter_page') %}active-page bg-yellow-600{% endif %}">
         Convert Image
      </a>
      <a href="{{ url_for('tools.split_pdf_page') }}" 
         class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-blue-600 hover:text-white transition-colors {% if request.path == url_for('tools.split_pdf_page') %}active-page bg-blue-600{% endif %}">
        Split PDF
      </a>
      <a href="{{ url_for('tools.merge_pdf_page') }}" 
         class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-red-600 hover:text-white transition-colors {% if request.path == url_for('tools.merge_pdf_page') %}active-page bg-red-600{% endif %}">
        Merge PDF
      </a>
      <a href="{{ url_for('tools.convert_file_page') }}" 
         class="text-gray-300 font-semibold px-4 py-2 rounded hover:bg-green-600 hover:text-white transition-colors {% if request.path == url_for('tools.convert_file_page') %}active-page bg-green-600{% endif %}">
        Convert Files
      </a>
    </div>
  
    <div class="container mx-auto px-4 pt-20 pb-8">
      <main class="flex-grow">
        {% block content %}{% endblock %}
      </main>
    </div>
  
    <footer class="mt-auto py-6 px-6 bg-gray-800 flex flex-col md:flex-row justify-between items-center text-sm shadow-md">
      <p class="text-gray-400 mb-4 md:mb-0">
          Powered by: <a href="https://www.tomscyberlab.net" target="_blank" class="text-blue-500 hover:text-blue-400 transition-colors">tomscyberlab</a>
      </p>
      <div class="flex space-x-6">
          <a href="/terms" class="text-blue-500 hover:text-blue-400 transition-colors">Terms & Conditions</a>
          <a href="/privacy" class="text-blue-500 hover:text-blue-400 transition-colors">Privacy Policy</a>
      </div>
    </footer>
  
    <!-- Fun Zone Sidebar -->
    <div id="fun-zone-sidebar" class="fun-zone-visible productive">
        <!-- Sidebar Content -->
        <div class="sidebar-content">
        <!-- Tab Headers -->
        <div class="tab-headers">
            <button class="tab-btn active-tab" data-tab="confessions">
            <svg class="tab-icon" width="24" height="24" viewBox="0 0 24 24" fill="white" stroke="none">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08s5.97 1.09 6 3.08c-1.29 1.94-3.5 3.22-6 3.22z"/>
            </svg>
            </button>
            <button class="tab-btn" data-tab="poll">
            <svg class="tab-icon" width="24" height="24" viewBox="0 0 24 24" fill="white" stroke="none">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            </button>
            <button class="tab-btn" data-tab="vibe">
            <svg class="tab-icon" width="24" height="24" viewBox="0 0 24 24" fill="white" stroke="none">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 5c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm2 0c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1zm-5 9c0-2.2 1.8-4 4-4s4 1.8 4 4H8z"/>
            </svg>
            </button>
            <button class="tab-btn" data-tab="history">
            <svg class="tab-icon" width="24" height="24" viewBox="0 0 24 24" fill="white" stroke="none">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
            </svg>
            </button>
        </div>
        
        <!-- Tab Contents -->
        <div class="tab-contents">
            <!-- Confessions Tab -->
            <div id="confessions-tab" class="tab-content">
            <h3>Anonymous Confessions</h3>
            <div id="confession-display" class="confession-display">
                "I use StackOverflow more than my IDE"
            </div>
            <textarea id="confession-input" placeholder="Your confession... (keep it clean)"></textarea>
            <button id="submit-confession">
                Submit Confession
            </button>
            </div>
            
            <!-- Poll Tab -->
            <div id="poll-tab" class="tab-content hidden">
            <h3>Quick Poll</h3>
            <p id="poll-question">Tabs vs. Spaces?</p>
            <div class="poll-options">
                <button class="poll-option">
                <span>👉</span> Tabs
                </button>
                <button class="poll-option">
                <span>👉</span> Spaces
                </button>
            </div>
            <div id="poll-results" class="hidden">
                <div class="poll-result">
                <span>Tabs</span>
                <span id="tabs-percent">0%</span>
                </div>
                <div class="poll-bar">
                <div id="tabs-bar" style="width: 0%"></div>
                </div>
                <div class="poll-result">
                <span>Spaces</span>
                <span id="spaces-percent">0%</span>
                </div>
                <div class="poll-bar">
                <div id="spaces-bar" style="width: 0%"></div>
                </div>
            </div>
            </div>
            
            <!-- Vibe Tab -->
            <div id="vibe-tab" class="tab-content hidden">
            <h3>Dev Vibe Today</h3>
            <div class="vibe-grid">
                <button data-vibe="productive" class="vibe-btn active-vibe">
                <span>🚀</span>
                <span>Productive</span>
                </button>
                <button data-vibe="tired" class="vibe-btn">
                <span>😴</span>
                <span>Tired</span>
                </button>
                <button data-vibe="debugging" class="vibe-btn">
                <span>🐛</span>
                <span>Debugging</span>
                </button>
                <button data-vibe="procrastinating" class="vibe-btn">
                <span>🎮</span>
                <span>Procrastinating</span>
                </button>
            </div>
            <div id="vibe-result" class="hidden">
                Thanks for sharing your vibe!
            </div>
            </div>
            
            <!-- History Tab -->
            <div id="history-tab" class="tab-content hidden">
            <h3>Tech History</h3>
            <div id="history-loading">
                <div class="loading-spinner"></div>
            </div>
            <p id="tech-history-text" class="hidden"></p>
            <a id="history-link" href="#" target="_blank" class="hidden">Read more</a>
            </div>
        </div>
        
        <!-- Toggle Button at Bottom -->
        <div>
            <button id="fun-zone-toggle" class="bottom-toggle focus:outline-none">
            <svg id="toggle-icon" class="toggle-icon-arrow" viewBox="0 0 24 24">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
            <svg id="close-icon" class="toggle-icon-x" viewBox="0 0 24 24">
                <path d="M18 6L6 18M6 6l12 12"/>
            </svg>
            </button>
        </div>
    </div>
  <!-- JavaScript -->
  <script src="{{ url_for('static', filename='js/mobile-menu.js') }}"></script>
  <script src="{{ url_for('static', filename='js/fun-zone.js') }}"></script>
</body>
</html>