/**
 * File Preview CSS
 * Provides styling for file preview components
 */

/* Base preview container */
.file-preview-container {
    width: 100%;
    margin: 1rem 0;
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: var(--color-gray-800);
}

/* Image preview */
.image-preview {
    max-width: 100%;
    max-height: 16rem;
    margin: 0 auto;
    display: block;
    border-radius: 0.5rem;
    object-fit: contain;
}

/* PDF preview */
.pdf-preview-container {
    width: 100%;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.pdf-preview-container embed {
    width: 100%;
    height: 16rem;
    border: none;
}

/* Text preview */
.text-preview {
    background-color: var(--color-gray-800);
    padding: 0.75rem;
    border-radius: 0.5rem;
    color: var(--color-gray-300);
    font-size: 0.875rem;
    font-family: monospace;
    white-space: pre-wrap;
    overflow-x: auto;
    max-height: 16rem;
    overflow-y: auto;
}

/* Preview placeholder */
.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--color-gray-800);
    border-radius: 0.5rem;
    padding: 1rem;
    height: 8rem;
}

.preview-placeholder svg {
    height: 2.5rem;
    width: 2.5rem;
    color: var(--color-gray-500);
    margin-bottom: 0.5rem;
}

.preview-placeholder-text {
    color: var(--color-gray-400);
    font-size: 0.875rem;
    text-align: center;
}

.preview-placeholder-filename {
    color: var(--color-gray-500);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Loading state */
.preview-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-gray-800);
    border-radius: 0.5rem;
    padding: 1rem;
    height: 8rem;
}

.preview-loading svg {
    height: 1.5rem;
    width: 1.5rem;
    color: var(--color-gray-400);
}

.preview-loading-text {
    margin-left: 0.5rem;
    color: var(--color-gray-400);
}

/* Error state */
.preview-error {
    background-color: rgba(var(--color-error-rgb), 0.2);
    color: var(--color-error-300);
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

/* Preview toolbar */
.preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-gray-800);
    padding: 0.5rem;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.preview-toolbar-filename {
    color: var(--color-gray-300);
    font-size: 0.875rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 80%;
}

.preview-toolbar-actions {
    display: flex;
    gap: 0.5rem;
}

.preview-toolbar-button {
    color: var(--color-primary-400);
    transition: color 0.2s;
}

.preview-toolbar-button:hover {
    color: var(--color-primary-300);
}

/* File type icons */
.file-icon {
    height: 2.5rem;
    width: 2.5rem;
    margin-bottom: 0.5rem;
}

.file-icon-pdf {
    color: var(--color-error-500);
}

.file-icon-word {
    color: var(--color-blue-500);
}

.file-icon-excel {
    color: var(--color-green-500);
}

.file-icon-text {
    color: var(--color-gray-500);
}

.file-icon-image {
    color: var(--color-purple-500);
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .image-preview,
    .pdf-preview-container embed,
    .text-preview {
        max-height: 12rem;
    }
    
    .preview-placeholder,
    .preview-loading {
        height: 6rem;
    }
}
