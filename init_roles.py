from app import create_app
from app.extensions import db
from app.models.auth import Role

app, _ = create_app()

with app.app_context():
    # Create database tables
    db.create_all()

    # Create default roles
    roles = [
        {"name": "guest", "description": "Limited access for non-registered users"},
        {"name": "registered", "description": "Standard access for registered users"},
        {"name": "admin", "description": "Full access to all features"},
    ]

    for role_data in roles:
        if not Role.query.filter_by(name=role_data["name"]).first():
            db.session.add(Role(**role_data))

    db.session.commit()
    print("Default roles created successfully")
