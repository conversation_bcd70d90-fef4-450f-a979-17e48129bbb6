"""
Smoke tests for main routes.
"""

# flake8: noqa
import pytest
from flask import url_for


def test_index_page(client):
    """Test that the index page loads successfully."""
    response = client.get("/")
    assert response.status_code == 200
    assert b"<!DOCTYPE html>" in response.data


def test_merge_pdf_form(client, monkeypatch):
    """Test that the merge PDF form page loads successfully."""
    # Monkeypatch the render_template function to avoid form issues
    from flask import render_template as original_render

    def mock_render_template(*args, **kwargs):
        if "merge_pdf.html" in args:
            kwargs["form"] = type("obj", (object,), {"hidden_tag": lambda: ""})
        return original_render(*args, **kwargs)

    monkeypatch.setattr("flask.render_template", mock_render_template)

    response = client.get("/merge_pdf")
    assert response.status_code == 200
    assert b"<!DOCTYPE html>" in response.data


def test_split_pdf_form(client):
    """Test that the split PDF form page loads successfully."""
    response = client.get("/split_pdf")
    assert response.status_code == 200
    assert b"<!DOCTYPE html>" in response.data
