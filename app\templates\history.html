{% extends "base.html" %}

{% block title %}Conversion History - File Processing Hub{% endblock %}

{% block content %}
<style>
    /* Empty state styles */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
    }

    /* History item styles */
    .history-item {
        transition: all 0.2s ease-in-out;
    }

    .history-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Status indicators */
    .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }

    .status-success {
        background-color: #10B981; /* green-500 */
    }

    .status-failed {
        background-color: #EF4444; /* red-500 */
    }

    .status-processing {
        background-color: #F59E0B; /* amber-500 */
        animation: pulse 2s infinite;
    }

    .status-canceled {
        background-color: #6B7280; /* gray-500 */
    }

    @keyframes pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
    }

    /* Fade out animation for deleted items */
    .fade-out {
        animation: fadeOut 0.5s forwards;
    }

    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; height: 0; margin: 0; padding: 0; overflow: hidden; }
    }

    /* Loading animation */
    .loading-spinner {
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top: 3px solid #3B82F6;
        width: 24px;
        height: 24px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<div class="min-h-screen py-8 px-4">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">Conversion History</h1>
        <p class="text-gray-400 max-w-2xl mx-auto">
            View and manage your recent file conversions. History is stored temporarily and will be cleared after some time.
        </p>
    </div>

    <!-- History Container -->
    <div class="max-w-4xl mx-auto bg-gray-800 rounded-lg shadow-lg p-6">
        <!-- Loading State -->
        <div id="loading-state" class="py-8 flex justify-center">
            <div class="loading-spinner"></div>
            <span class="ml-3 text-gray-300">Loading history...</span>
        </div>

        <!-- Empty State -->
        <div id="empty-state" class="empty-state hidden">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="text-xl font-semibold text-gray-300 mb-2">No Conversion History</h3>
            <p class="text-gray-400 mb-4">You haven't converted any files yet. Try converting a file first.</p>
            <a href="{{ url_for('main.index') }}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition">
                Go to Converters
            </a>
        </div>

        <!-- History List -->
        <div id="history-list" class="hidden">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-white">Recent Conversions</h2>
                <button id="clear-history-btn" class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition">
                    Clear All
                </button>
            </div>

            <div id="history-items" class="space-y-4">
                <!-- History items will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const historyManager = new ConversionHistoryManager();
        historyManager.init();
    });

    class ConversionHistoryManager {
        constructor() {
            this.elements = {
                loadingState: document.getElementById('loading-state'),
                emptyState: document.getElementById('empty-state'),
                historyList: document.getElementById('history-list'),
                historyItems: document.getElementById('history-items'),
                clearHistoryBtn: document.getElementById('clear-history-btn')
            };
            this.history = [];
        }

        init() {
            this.loadHistory();
            this.setupEventListeners();
        }

        setupEventListeners() {
            // Clear history button
            this.elements.clearHistoryBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear all conversion history?')) {
                    this.clearHistory();
                }
            });

            // Delegate event listener for delete buttons
            this.elements.historyItems.addEventListener('click', (e) => {
                const deleteBtn = e.target.closest('.delete-history-btn');
                if (deleteBtn) {
                    const historyId = deleteBtn.dataset.id;
                    this.deleteHistoryItem(historyId);
                }
            });
        }

        async loadHistory() {
            try {
                const response = await fetch('/api/history');
                const data = await response.json();

                if (data.success) {
                    this.history = data.history;
                    this.renderHistory();
                } else {
                    this.showError('Failed to load history');
                }
            } catch (error) {
                console.error('Error loading history:', error);
                this.showError('Failed to load history');
            } finally {
                this.elements.loadingState.classList.add('hidden');
            }
        }

        renderHistory() {
            if (this.history.length === 0) {
                this.elements.emptyState.classList.remove('hidden');
                this.elements.historyList.classList.add('hidden');
                return;
            }

            this.elements.historyList.classList.remove('hidden');
            this.elements.emptyState.classList.add('hidden');
            this.elements.historyItems.innerHTML = '';

            this.history.forEach(item => {
                const historyItem = this.createHistoryItem(item);
                this.elements.historyItems.appendChild(historyItem);
            });
        }

        createHistoryItem(item) {
            const div = document.createElement('div');
            div.className = 'history-item bg-gray-700 rounded-lg p-4';
            div.dataset.id = item.id;

            // Format date
            const date = new Date(item.created_at);
            const formattedDate = date.toLocaleString();

            // Get status class
            const statusClass = this.getStatusClass(item.status);
            
            div.innerHTML = `
                <div class="flex justify-between">
                    <div class="flex-1">
                        <div class="flex items-center">
                            <span class="status-indicator ${statusClass}"></span>
                            <h3 class="text-lg font-medium text-white">${item.original_filename}</h3>
                        </div>
                        <div class="text-sm text-gray-400 mt-1">
                            ${item.conversion_type.toUpperCase()} • ${item.input_format} → ${item.output_format}
                        </div>
                        <div class="text-xs text-gray-500 mt-1">
                            ${formattedDate}
                        </div>
                    </div>
                    <div class="flex items-start space-x-2">
                        ${item.status === 'success' ? `
                            <a href="/download/${item.output_filename}" class="text-blue-400 hover:text-blue-300" title="Download">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </a>
                        ` : ''}
                        <button class="delete-history-btn text-red-400 hover:text-red-300" title="Delete" data-id="${item.id}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                </div>
                ${item.status === 'failed' && item.error_message ? `
                    <div class="mt-2 p-2 bg-red-900 bg-opacity-50 rounded text-sm text-red-200">
                        ${item.error_message}
                    </div>
                ` : ''}
            `;

            return div;
        }

        getStatusClass(status) {
            switch (status) {
                case 'success': return 'status-success';
                case 'failed': return 'status-failed';
                case 'processing': return 'status-processing';
                case 'canceled': return 'status-canceled';
                default: return '';
            }
        }

        async deleteHistoryItem(id) {
            try {
                const response = await fetch(`/api/history/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Find the item in the DOM and remove it with animation
                    const item = this.elements.historyItems.querySelector(`[data-id="${id}"]`);
                    if (item) {
                        item.classList.add('fade-out');
                        setTimeout(() => {
                            item.remove();
                            
                            // Update the history array
                            this.history = this.history.filter(item => item.id != id);
                            
                            // Check if we need to show empty state
                            if (this.history.length === 0) {
                                this.elements.historyList.classList.add('hidden');
                                this.elements.emptyState.classList.remove('hidden');
                            }
                        }, 500);
                    }
                } else {
                    this.showError(data.error?.message || 'Failed to delete item');
                }
            } catch (error) {
                console.error('Error deleting history item:', error);
                this.showError('Failed to delete item');
            }
        }

        async clearHistory() {
            try {
                const response = await fetch('/api/history/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Clear the history array
                    this.history = [];
                    
                    // Show empty state
                    this.elements.historyList.classList.add('hidden');
                    this.elements.emptyState.classList.remove('hidden');
                } else {
                    this.showError(data.error?.message || 'Failed to clear history');
                }
            } catch (error) {
                console.error('Error clearing history:', error);
                this.showError('Failed to clear history');
            }
        }

        showError(message) {
            // Create toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-4 right-4 bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // Remove after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    }
</script>
{% endblock %}
