/**
 * Progress Indicators JavaScript Utilities
 * Provides functions for managing animated progress indicators
 */

class ProgressIndicator {
    /**
     * Initialize a progress indicator
     * @param {Object} options - Configuration options
     * @param {string} options.type - Type of progress indicator ('linear', 'circular', 'dots', 'steps')
     * @param {string} options.containerId - ID of the container element
     * @param {boolean} options.indeterminate - Whether the progress is indeterminate
     * @param {number} options.initialValue - Initial progress value (0-100)
     * @param {number} options.steps - Number of steps (for step indicator)
     * @param {Function} options.onComplete - Callback function when progress reaches 100%
     */
    constructor(options) {
        this.options = Object.assign({
            type: 'linear',
            containerId: null,
            indeterminate: false,
            initialValue: 0,
            steps: 4,
            onComplete: null
        }, options);

        this.container = document.getElementById(this.options.containerId);
        if (!this.container) {
            console.error(`Container with ID "${this.options.containerId}" not found.`);
            return;
        }

        this.value = this.options.initialValue;
        this.currentStep = 1;
        this.elements = {};

        this.render();
        this.update(this.value);
    }

    /**
     * Render the progress indicator
     */
    render() {
        // Clear container
        this.container.innerHTML = '';
        
        switch (this.options.type) {
            case 'linear':
                this.renderLinearProgress();
                break;
            case 'circular':
                this.renderCircularProgress();
                break;
            case 'dots':
                this.renderDotsProgress();
                break;
            case 'steps':
                this.renderStepsProgress();
                break;
            default:
                console.error(`Unknown progress type: ${this.options.type}`);
        }
    }

    /**
     * Render a linear progress bar
     */
    renderLinearProgress() {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        
        // Create progress bar container
        const barContainer = document.createElement('div');
        barContainer.className = this.options.indeterminate 
            ? 'progress-indeterminate' 
            : 'progress-bar-container';
        
        if (!this.options.indeterminate) {
            // Create progress bar fill
            const barFill = document.createElement('div');
            barFill.className = 'progress-bar-fill progress-gradient';
            barContainer.appendChild(barFill);
            this.elements.barFill = barFill;
        }
        
        // Create text display
        const textDisplay = document.createElement('div');
        textDisplay.className = 'progress-text';
        textDisplay.innerHTML = '<span class="progress-percentage">0%</span>';
        
        progressContainer.appendChild(barContainer);
        progressContainer.appendChild(textDisplay);
        this.container.appendChild(progressContainer);
        
        this.elements.textDisplay = textDisplay;
    }

    /**
     * Render a circular progress indicator
     */
    renderCircularProgress() {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        
        // Create SVG element
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('class', 'progress-circular');
        svg.setAttribute('viewBox', '0 0 36 36');
        
        // Create track circle
        const track = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        track.setAttribute('class', 'progress-circular-track');
        track.setAttribute('cx', '18');
        track.setAttribute('cy', '18');
        track.setAttribute('r', '16');
        
        // Create progress circle
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('class', 'progress-circular-fill');
        circle.setAttribute('cx', '18');
        circle.setAttribute('cy', '18');
        circle.setAttribute('r', '16');
        
        // Calculate the circumference
        const circumference = 2 * Math.PI * 16;
        circle.style.strokeDasharray = `${circumference} ${circumference}`;
        circle.style.strokeDashoffset = `${circumference}`;
        
        svg.appendChild(track);
        svg.appendChild(circle);
        
        // Create text display
        const textDisplay = document.createElement('div');
        textDisplay.className = 'progress-text';
        textDisplay.innerHTML = '<span class="progress-percentage">0%</span>';
        
        progressContainer.appendChild(svg);
        progressContainer.appendChild(textDisplay);
        this.container.appendChild(progressContainer);
        
        this.elements.circle = circle;
        this.elements.textDisplay = textDisplay;
        this.elements.circumference = circumference;
    }

    /**
     * Render a dots loading indicator
     */
    renderDotsProgress() {
        const dotsContainer = document.createElement('div');
        dotsContainer.className = 'progress-dots';
        
        // Create three dots
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('div');
            dot.className = 'progress-dot';
            dotsContainer.appendChild(dot);
        }
        
        this.container.appendChild(dotsContainer);
    }

    /**
     * Render a step progress indicator
     */
    renderStepsProgress() {
        const stepsContainer = document.createElement('div');
        stepsContainer.className = 'progress-steps';
        
        // Create step indicators
        const steps = [];
        for (let i = 1; i <= this.options.steps; i++) {
            const step = document.createElement('div');
            step.className = 'progress-step';
            step.textContent = i;
            stepsContainer.appendChild(step);
            steps.push(step);
        }
        
        this.container.appendChild(stepsContainer);
        this.elements.steps = steps;
    }

    /**
     * Update the progress indicator
     * @param {number} value - Progress value (0-100)
     * @param {string} message - Optional message to display
     */
    update(value, message = null) {
        this.value = Math.min(100, Math.max(0, value));
        
        switch (this.options.type) {
            case 'linear':
                this.updateLinearProgress();
                break;
            case 'circular':
                this.updateCircularProgress();
                break;
            case 'steps':
                this.updateStepsProgress();
                break;
        }
        
        // Update message if provided
        if (message && this.elements.textDisplay) {
            this.elements.textDisplay.innerHTML = `
                <span class="progress-percentage">${this.value}%</span>
                <span class="progress-message">${message}</span>
            `;
        } else if (this.elements.textDisplay) {
            this.elements.textDisplay.innerHTML = `
                <span class="progress-percentage">${this.value}%</span>
            `;
        }
        
        // Call onComplete callback if progress is 100%
        if (this.value === 100 && typeof this.options.onComplete === 'function') {
            this.options.onComplete();
        }
    }

    /**
     * Update linear progress bar
     */
    updateLinearProgress() {
        if (this.elements.barFill) {
            this.elements.barFill.style.width = `${this.value}%`;
        }
    }

    /**
     * Update circular progress indicator
     */
    updateCircularProgress() {
        if (this.elements.circle && this.elements.circumference) {
            const offset = this.elements.circumference - (this.value / 100 * this.elements.circumference);
            this.elements.circle.style.strokeDashoffset = offset;
        }
    }

    /**
     * Update step progress indicator
     */
    updateStepsProgress() {
        if (!this.elements.steps) return;
        
        // Calculate which step should be active based on progress value
        const activeStep = Math.ceil((this.value / 100) * this.options.steps);
        
        // Update step classes
        this.elements.steps.forEach((step, index) => {
            step.classList.remove('progress-step-active', 'progress-step-complete');
            
            if (index + 1 === activeStep) {
                step.classList.add('progress-step-active');
            } else if (index + 1 < activeStep) {
                step.classList.add('progress-step-complete');
            }
        });
    }

    /**
     * Set the progress indicator to indeterminate state
     * @param {boolean} indeterminate - Whether the progress is indeterminate
     */
    setIndeterminate(indeterminate) {
        if (this.options.indeterminate === indeterminate) return;
        
        this.options.indeterminate = indeterminate;
        this.render();
    }
}

// Export to global scope
window.ProgressIndicator = ProgressIndicator;
