name: Smoke Tests

on:
  deployment:
    environments: [staging]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run tests against'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  smoke-tests:
    name: Run Smoke Tests
    runs-on: ubuntu-latest
    environment: ${{ github.event.deployment.environment || github.event.inputs.environment }}
    
    env:
      STAGING_URL: ${{ secrets.STAGING_URL }}
      STAGING_API_KEY: ${{ secrets.STAGING_API_KEY }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest requests
      
      - name: Run smoke tests
        run: |
          python -m pytest tests/smoke_tests.py -v
