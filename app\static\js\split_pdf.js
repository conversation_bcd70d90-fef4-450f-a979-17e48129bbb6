src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"

class PDFSplitterUI {
    constructor() {
        // Set pdf.js worker source
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js';

        this.elements = {
            dropArea: document.getElementById("drop-area"),
            fileInput: document.getElementById("file-input"),
            fileDetails: document.getElementById("file-details"),
            noFileSelected: document.getElementById("no-file-selected"),
            fileName: document.getElementById("file-name"),
            fileSize: document.getElementById("file-size"),
            pageCount: document.getElementById("page-count"),
            sizeWarning: document.getElementById("size-warning"),
            submitButton: document.getElementById("submit-button"),
            form: document.getElementById("split-pdf-form"),
            progressContainer: document.getElementById("progress-container"),
            progressBar: document.getElementById("progress-bar"),
            progressText: document.getElementById("progress-text"),
            pagesInput: document.getElementById("pages"),
            resultsContainer: document.getElementById("results-container"),
            errorContainer: document.getElementById("error-container"),
            formContainer: document.querySelector('.form-container') // Add form container
        };

        this.MAX_FILE_SIZE_MB = 10;
        this.selectedFile = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Prevent default drag behaviors
        ["dragenter", "dragover", "dragleave", "drop"].forEach(eventName => {
            this.elements.dropArea.addEventListener(eventName, this.preventDefaults.bind(this));
            document.body.addEventListener(eventName, this.preventDefaults.bind(this));
        });

        // Highlight drop area when item is dragged over it
        ["dragenter", "dragover"].forEach(eventName => {
            this.elements.dropArea.addEventListener(eventName, () => this.highlightDropArea(true));
        });

        // Remove highlight when item leaves or is dropped
        ["dragleave", "drop"].forEach(eventName => {
            this.elements.dropArea.addEventListener(eventName, () => this.highlightDropArea(false));
        });

        // Handle file drop
        this.elements.dropArea.addEventListener("drop", this.handleDrop.bind(this));

        // Handle file selection via input
        this.elements.fileInput.addEventListener("change", this.handleFileSelect.bind(this));

        // Handle form submission
        this.elements.form.addEventListener("submit", this.handleSubmit.bind(this));

        // Make drop area clickable
        this.elements.dropArea.addEventListener("click", () => this.elements.fileInput.click());

        // Add keyboard support for drop area
        this.elements.dropArea.addEventListener("keydown", (e) => {
            if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                this.elements.fileInput.click();
            }
        });
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlightDropArea(highlight) {
        this.elements.dropArea.classList.toggle("drag-active", highlight);

        // Update screen reader status
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = highlight ?
                "File is being dragged over the drop area" :
                this.selectedFile ? `File selected: ${this.selectedFile.name}` : "No file selected";
        }
    }

    handleDrop(e) {
        const dt = e.dataTransfer;
        if (dt.files.length > 0) {
            this.elements.fileInput.files = dt.files;
            this.handleFileSelect();
        }
    }

    async handleFileSelect() {
        if (this.elements.fileInput.files.length > 0) {
            const file = this.elements.fileInput.files[0];
            this.selectedFile = file;

            if (!file.name.toLowerCase().endsWith('.pdf')) {
                this.showError("Please select a PDF file");
                this.resetFileInput();
                return;
            }

            if (file.size > this.MAX_FILE_SIZE_MB * 1024 * 1024) {
                this.showError(`File size exceeds ${this.MAX_FILE_SIZE_MB}MB limit`);
                this.resetFileInput();
                return;
            }

            this.elements.fileDetails.classList.remove("hidden");
            this.elements.noFileSelected.classList.add("hidden");
            this.elements.fileName.textContent = file.name;
            this.elements.fileSize.textContent = this.formatFileSize(file.size);
            this.elements.sizeWarning.classList.toggle("hidden", file.size <= 5 * 1024 * 1024);
            this.elements.submitButton.disabled = false;

            // Update screen reader status
            const statusElement = document.getElementById('file-upload-status');
            if (statusElement) {
                statusElement.textContent = `File selected: ${file.name}, size: ${this.formatFileSize(file.size)}`;
            }

            this.addDeleteButton();

            this.elements.pageCount.textContent = "Detecting pages...";
            const pageCount = await this.getPageCount(file);
            this.elements.pageCount.textContent = `Total Pages: ${pageCount}`;
        } else {
            this.resetFileInput();
        }
    }

    async getPageCount(file) {
        try {
            const arrayBuffer = await file.arrayBuffer();
            const pdfData = new Uint8Array(arrayBuffer);
            const pdf = await pdfjsLib.getDocument({ data: pdfData }).promise;
            return pdf.numPages;
        } catch (error) {
            console.error('Error getting page count:', error);
            this.showError('Error detecting page count');
            return 0;
        }
    }

    addDeleteButton() {
        const existingDeleteButton = this.elements.fileDetails.querySelector('.delete-button');
        if (existingDeleteButton) return;

        const deleteButton = document.createElement('button');
        deleteButton.textContent = 'Delete File';
        deleteButton.className = 'delete-button ml-4 px-4 py-1 bg-red-600 text-white rounded hover:bg-red-700';
        deleteButton.addEventListener('click', () => this.resetFileInput());

        const fileInfoContainer = this.elements.fileDetails.querySelector('div');
        fileInfoContainer.appendChild(deleteButton);
    }

    resetFileInput() {
        this.elements.fileInput.value = '';
        this.selectedFile = null;
        this.elements.fileDetails.classList.add("hidden");
        this.elements.noFileSelected.classList.remove("hidden");
        this.elements.pageCount.textContent = "";
        this.elements.sizeWarning.classList.add("hidden");
        this.elements.submitButton.disabled = true;
        this.elements.pagesInput.value = '';

        // Update screen reader status
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = "No file selected";
        }

        const deleteButton = this.elements.fileDetails.querySelector('.delete-button');
        if (deleteButton) deleteButton.remove();
    }

    resetForm() {
        this.resetFileInput();
        this.elements.formContainer.classList.remove('hidden');
        if (this.elements.resultsContainer) {
            this.elements.resultsContainer.remove();
            this.elements.resultsContainer = null;
        }
        this.clearErrors();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateProgress(percent) {
        this.elements.progressBar.style.width = `${percent}%`;
        this.elements.progressText.textContent = `${percent}%`;
    }

    showError(message) {
        // Update the file upload status for screen readers
        const statusElement = document.getElementById('file-upload-status');
        if (statusElement) {
            statusElement.textContent = `Error: ${message}`;
        }

        // Get or create error container
        let errorContainer = document.getElementById('error-container');
        if (!errorContainer) {
            errorContainer = document.createElement('div');
            errorContainer.id = 'error-container';
            errorContainer.setAttribute('aria-live', 'assertive');
            errorContainer.className = 'mt-6';
            this.elements.form.parentNode.insertBefore(errorContainer, this.elements.form.nextSibling);
        }

        // Show enhanced error message
        errorContainer.innerHTML = `
            <div class="error-message">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="error-message-content">
                    <div class="error-message-title">Error</div>
                    <div class="error-message-text">${message}</div>
                </div>
            </div>
        `;
        errorContainer.classList.remove('hidden');
    }

    clearErrors() {
        if (this.elements.errorContainer) {
            this.elements.errorContainer.remove();
            this.elements.errorContainer = null;
        }
    }

    async handleSubmit(e) {
        e.preventDefault();
        this.clearErrors();

        if (!this.selectedFile) {
            this.showError("Please select a PDF file");
            return;
        }

        const pages = this.elements.pagesInput.value.trim();
        if (!pages) {
            this.showError("Please specify pages to extract");
            return;
        }

        if (!/^[\d\s,-]+$/.test(pages)) {
            this.showError("Invalid page format. Use numbers, commas, and hyphens only");
            return;
        }

        const formData = new FormData(this.elements.form);
        this.setLoadingState(true);

        try {
            this.updateProgress(20);

            const response = await fetch(this.elements.form.action, {
                method: "POST",
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            });

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const result = await response.json();
                if (!response.ok) {
                    throw new Error(result.error || "Server error occurred");
                }
                this.handleSuccessResponse(result);
            } else {
                const html = await response.text();
                if (!response.ok) {
                    throw new Error("Server returned an error");
                }
                document.open();
                document.write(html);
                document.close();
            }
        } catch (error) {
            console.error("Error:", error);
            this.showError(error.message);
            this.setLoadingState(false);
        }
    }

    setLoadingState(isLoading) {
        if (isLoading) {
            this.elements.submitButton.disabled = true;
            this.elements.submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
            `;
            this.elements.progressContainer.classList.remove("hidden");
        } else {
            this.elements.submitButton.disabled = false;
            this.elements.submitButton.textContent = "Split PDF";
            this.elements.progressContainer.classList.add("hidden");
            this.updateProgress(0);
        }
    }

    handleSuccessResponse(result) {
        this.updateProgress(100);

        if (result.download_url) {
            this.elements.formContainer.classList.add('hidden');
            this.showResultsContainer(result.download_url, result.output_filename);
        }

        setTimeout(() => {
            this.setLoadingState(false);
        }, 1000);
    }

    showResultsContainer(url, filename) {
        // Remove existing results container if present
        if (this.elements.resultsContainer) {
            this.elements.resultsContainer.remove();
        }

        // Create new results container
        this.elements.resultsContainer = document.createElement('div');
        this.elements.resultsContainer.id = 'results-container';
        this.elements.resultsContainer.className = 'w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6';

        // Insert after form container
        this.elements.formContainer.insertAdjacentElement('afterend', this.elements.resultsContainer);

        // Set the content
        this.elements.resultsContainer.innerHTML = `
            <div class="mb-6 text-center">
                <svg class="mx-auto h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h3 class="mt-4 text-2xl font-semibold text-white">Split Successful!</h3>
                <p class="mt-2 text-gray-400">Your PDF has been successfully processed.</p>
            </div>
            <div class="p-4 bg-gray-700 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="text-gray-300">${filename || 'split.pdf'}</span>
                    <a href="${url}" class="download-link text-blue-500 hover:text-blue-400 hover:underline"
                       download="${filename || 'split.pdf'}">
                        Download PDF
                    </a>
                </div>
            </div>
            <button id="new-split-btn" class="mt-6 w-full px-4 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition">
                Split Another PDF
            </button>
        `;

        // Add event listeners
        const downloadLink = this.elements.resultsContainer.querySelector('.download-link');
        downloadLink.addEventListener('click', () => {
            setTimeout(() => this.resetForm(), 500);
        });

        const newSplitBtn = this.elements.resultsContainer.querySelector('#new-split-btn');
        newSplitBtn.addEventListener('click', () => this.resetForm());
    }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
    new PDFSplitterUI();
});