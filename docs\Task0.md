# AC 10-sprint Perfect-Ten Flask Refinement

> **Instructions for Augment Code (AC)**
> 1. Execute tasks in order.
> 2. After each task:
>    - Mark `[ ]` → `[x] ✅`
>    - Commit with `feat(ac): …` or `refactor(ac): …`
>    - Run `flake8 .` & `black --check .` & `pytest --maxfail=1 --disable-warnings -q` (0 errors)
>    - Output:
>      ```
>      >>>NEXT-BEGIN<<<
>      RemainingTasks: <count>
>      Focus: <next-task>
>      >>>NEXT-END<<<
>      ```
> 3. Keep strict typing; use `mypy` with no `# type: ignore`.
> 4. If nearing 30 messages in this thread, start a new one.

---

## Sprint 1: Setup & Smoke Tests
- [x] ✅ Create feature branch `perfect-ten`.
- [x] ✅ Configure CI (GitHub Actions) for flake8, black, pytest.
- [x] ✅ Setup PostgreSQL test database in CI.
- [x] ✅ Add smoke tests for existing endpoints.

## Sprint 2: Theme Toggle & Layout
- [x] ✅ Define CSS variables for light mode alongside dark mode.
- [x] ✅ Implement theme toggle in base template.
- [x] ✅ Ensure all components respect theme variables.
- [x] ✅ Write tests for theme switch.

## Sprint 3: User & Role Models
- [x] ✅ Add `User` and `Role` models (SQLAlchemy).
- [x] ✅ Create migrations via Flask-Migrate.
- [x] ✅ Seed guest and registered roles.
- [x] ✅ Write model integrity tests.

## Sprint 4: Authentication & ACL
- [x] ✅ Integrate Flask-Login or Flask-JWT-Extended.
- [x] ✅ Implement login, logout, registration endpoints.
- [x] ✅ Add role-based access decorator.
- [x] ✅ Write auth flow tests.

## Sprint 5: Guest Single File Conversion
- [x] ✅ Enable single-file conversion for guests.
- [x] ✅ Add rate limiting (Flask-Limiter).
- [x] ✅ Validate inputs and handle errors.
- [x] ✅ Add integration tests.

## Sprint 6: Registered User Batch Conversion
- [x] ✅ Create batch conversion endpoint.
- [x] ✅ Integrate Celery with Redis.
- [x] ✅ Add progress-tracking endpoint.
- [x] ✅ Write batch workflow tests.

## Sprint 7: Code Refactor & Quality
- [x] ✅ Run black & flake8; fix all issues.
- [x] ✅ Remove debug logs and redundant routes.
- [x] ✅ Standardize JSON error schema.
- [x] ✅ Ensure CI passes.

## Sprint 8: Caching & Performance
- [x] ✅ Configure Redis caching for DB queries.
- [x] ✅ Add ETag and caching headers for results.
- [x] ✅ Optimize key SQL queries.
- [x] ✅ Add performance tests.

## Sprint 9: Documentation & API Spec
- [x] ✅ Update README with setup/test/deploy.
- [x] ✅ Document endpoints via API.md documentation.
- [x] ✅ Include example requests/responses.
- [x] ✅ Validate docs in CI.

## Sprint 10: Final QA & Deploy
- [x] ✅ Manual QA across features/roles.
- [x] ✅ Fix any edge-case bugs.
- [x] ✅ Merge `perfect-ten` into `main`.
- [x] ✅ Deploy to staging; run CI smoke tests.

---
*Last updated: 2025-05-14*
