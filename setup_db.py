from app import create_app
from app.extensions import db
from app.models.auth import Role, User, UserStatus
import os
import traceback

app, _ = create_app()

with app.app_context():
    try:
        # Create database tables
        db.create_all()
        print("Database tables created successfully")

        # Create default roles
        roles = [
            {"name": "guest", "description": "Limited access for non-registered users"},
            {"name": "registered", "description": "Standard access for registered users"},
            {"name": "admin", "description": "Full access to all features"},
        ]

        for role_data in roles:
            if not Role.query.filter_by(name=role_data["name"]).first():
                db.session.add(Role(**role_data))

        db.session.commit()
        print("Default roles created successfully")

        # Create admin user if it doesn't exist
        if not User.query.filter_by(username="admin").first():
            admin = User(
                username="admin",
                email="<EMAIL>",
                status=UserStatus.ACTIVE,
            )
            admin.password = "adminpassword"
            admin.generate_api_key()
            admin.add_role("admin")
            db.session.add(admin)
            db.session.commit()
            print("Admin user created successfully")

        print("Database setup completed successfully")

    except Exception as e:
        print(f"ERROR: {str(e)}")
        traceback.print_exc()
