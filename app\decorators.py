"""
Decorators for the application.
"""

from functools import wraps
from typing import Callable, List, Any
from flask import flash, redirect, url_for, request, jsonify
from flask_login import current_user


def role_required(role_name: str) -> Callable:
    """
    Decorator to restrict access to users with a specific role.

    Args:
        role_name: The name of the required role

    Returns:
        Decorated function that checks if the user has the required role
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> Any:
            if not current_user.is_authenticated or not current_user.has_role(role_name):
                if request.is_json:
                    return jsonify({"error": "Unauthorized access"}), 403
                flash("You don't have permission to access this page.", "error")
                return redirect(url_for("auth.login"))
            return f(*args, **kwargs)

        return decorated_function

    return decorator


def roles_required(role_names: List[str]) -> Callable:
    """
    Decorator to restrict access to users with any of the specified roles.

    Args:
        role_names: List of role names, any of which grants access

    Returns:
        Decorated function that checks if the user has any of the required roles
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> Any:
            if not current_user.is_authenticated or not any(current_user.has_role(role) for role in role_names):
                if request.is_json:
                    return jsonify({"error": "Unauthorized access"}), 403
                flash("You don't have permission to access this page.", "error")
                return redirect(url_for("auth.login"))
            return f(*args, **kwargs)

        return decorated_function

    return decorator


def roles_accepted(role_names: List[str]) -> Callable:
    """
    Decorator to restrict access to users with any of the specified roles.
    Unlike roles_required, this doesn't redirect to login for authenticated users,
    it just returns 403 Forbidden.

    Args:
        role_names: List of role names, any of which grants access

    Returns:
        Decorated function that checks if the user has any of the required roles
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> Any:
            if not current_user.is_authenticated:
                if request.is_json:
                    return jsonify({"error": "Authentication required"}), 401
                flash("Please log in to access this page.", "error")
                return redirect(url_for("auth.login"))

            if not any(current_user.has_role(role) for role in role_names):
                if request.is_json:
                    return jsonify({"error": "Insufficient permissions"}), 403
                flash("You don't have permission to access this page.", "error")
                return redirect(url_for("main.index"))

            return f(*args, **kwargs)

        return decorated_function

    return decorator


def guest_or_role_required(role_names: List[str]) -> Callable:
    """
    Decorator to allow access to guests or users with specific roles.
    Guests have limited functionality compared to authenticated users.

    Args:
        role_names: List of role names that grant full access

    Returns:
        Decorated function that sets a context variable indicating if the user is a guest
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> Any:
            # Set a flag to indicate if the user is a guest
            kwargs["is_guest"] = not current_user.is_authenticated

            # If user is authenticated but doesn't have required roles, deny access
            if current_user.is_authenticated and not any(current_user.has_role(role) for role in role_names):
                if request.is_json:
                    return jsonify({"error": "Insufficient permissions"}), 403
                flash("You don't have permission to access this page.", "error")
                return redirect(url_for("main.index"))

            return f(*args, **kwargs)

        return decorated_function

    return decorator
