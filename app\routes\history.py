"""
Routes for conversion history management.
"""

import uuid
from flask import Blueprint, render_template, jsonify, session, current_app
from flask_login import current_user
from app.models.conversion import ConversionHistory
from app.utils import create_error_response, add_cache_headers, add_etag_header
from app.decorators import guest_or_role_required

# Blueprint initialization
history_bp = Blueprint("history", __name__)


@history_bp.route("/history", methods=["GET"])
@guest_or_role_required("registered")
def history_page():
    """Render the conversion history page."""
    # Ensure session ID exists for guest users
    if not current_user.is_authenticated and "session_id" not in session:
        session["session_id"] = str(uuid.uuid4())
    return render_template("history.html")


@history_bp.route("/api/history", methods=["GET"])
@guest_or_role_required("registered")
def get_history():
    """API endpoint to get conversion history."""
    try:
        # Get history based on user authentication status
        if current_user.is_authenticated:
            history = ConversionHistory.get_user_history(limit=20)
        else:
            # For guest users, use session ID
            session_id = session.get("session_id", str(uuid.uuid4()))
            if "session_id" not in session:
                session["session_id"] = session_id
            history = ConversionHistory.get_session_history(session_id, limit=10)
        # Convert to dictionary for JSON response
        history_data = [item.to_dict() for item in history]
        response_data = {"success": True, "history": history_data}
        # Return history data with caching and ETag
        response = jsonify(response_data)
        response = add_cache_headers(response, max_age=60)  # Cache for 1 minute
        response = add_etag_header(response, response_data)
        return response
    except Exception as e:
        current_app.logger.error(f"Error retrieving conversion history: {str(e)}")
        return create_error_response("Failed to retrieve conversion history", 500)


@history_bp.route("/api/history/<int:history_id>", methods=["DELETE"])
@guest_or_role_required("registered")
def delete_history_item(history_id):
    """Delete a specific history item."""
    try:
        # Find the history item
        history_item = ConversionHistory.query.get_or_404(history_id)
        # Check if the user has permission to delete this item
        if current_user.is_authenticated:
            if history_item.user_id != current_user.id:
                return create_error_response("You don't have permission to delete this item", 403)
        else:
            # For guest users, check session ID
            session_id = session.get("session_id")
            if not session_id or history_item.session_id != session_id:
                return create_error_response("You don't have permission to delete this item", 403)
        # Delete the item
        from app.extensions import db

        db.session.delete(history_item)
        db.session.commit()
        return jsonify({"success": True, "message": "History item deleted successfully"})
    except Exception as e:
        current_app.logger.error(f"Error deleting history item: {str(e)}")
        return create_error_response("Failed to delete history item", 500)


@history_bp.route("/api/history/clear", methods=["POST"])
@guest_or_role_required("registered")
def clear_history():
    """Clear all conversion history for the current user or session."""
    try:
        from app.extensions import db

        # Delete history based on user authentication status
        if current_user.is_authenticated:
            ConversionHistory.query.filter_by(user_id=current_user.id).delete()
        else:
            # For guest users, use session ID
            session_id = session.get("session_id")
            if session_id:
                ConversionHistory.query.filter_by(session_id=session_id).delete()
        db.session.commit()
        return jsonify({"success": True, "message": "History cleared successfully"})
    except Exception as e:
        current_app.logger.error(f"Error clearing history: {str(e)}")
        return create_error_response("Failed to clear history", 500)
