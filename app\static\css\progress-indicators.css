/**
 * Progress Indicators CSS
 * Provides animated SVG and CSS-based progress indicators
 */

/* Base progress container */
.progress-container {
    width: 100%;
    margin: 1rem 0;
    position: relative;
}

/* Linear progress bar */
.progress-bar-container {
    width: 100%;
    height: 0.5rem;
    background-color: rgba(55, 65, 81, 0.5);
    border-radius: 9999px;
    overflow: hidden;
    position: relative;
}

/* Linear progress bar fill */
.progress-bar-fill {
    height: 100%;
    width: 0%;
    border-radius: 9999px;
    transition: width 0.3s ease;
}

/* Animated gradient background for progress bars */
.progress-gradient {
    background: linear-gradient(90deg, 
        var(--color-primary-600), 
        var(--color-primary-400), 
        var(--color-primary-600));
    background-size: 200% 100%;
    animation: progress-gradient 2s linear infinite;
}

@keyframes progress-gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Indeterminate progress bar */
.progress-indeterminate {
    width: 100%;
    height: 0.5rem;
    background-color: rgba(55, 65, 81, 0.5);
    border-radius: 9999px;
    overflow: hidden;
    position: relative;
}

.progress-indeterminate::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%;
    background-color: var(--color-primary-500);
    border-radius: 9999px;
    animation: indeterminate 1.5s ease-in-out infinite;
}

@keyframes indeterminate {
    0% { left: -30%; }
    100% { left: 100%; }
}

/* Circular progress indicator */
.progress-circular {
    width: 60px;
    height: 60px;
    position: relative;
    margin: 0 auto;
}

.progress-circular-track {
    fill: transparent;
    stroke: rgba(55, 65, 81, 0.3);
    stroke-width: 4;
}

.progress-circular-fill {
    fill: transparent;
    stroke: var(--color-primary-500);
    stroke-width: 4;
    stroke-linecap: round;
    transform-origin: center;
    transform: rotate(-90deg);
    transition: stroke-dasharray 0.3s ease;
}

/* Pulsing dots for loading states */
.progress-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.progress-dot {
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--color-primary-500);
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
}

.progress-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.progress-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.5; }
}

/* Step indicators for multi-stage processes */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin: 1.5rem 0;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: rgba(55, 65, 81, 0.3);
    transform: translateY(-50%);
    z-index: 1;
}

.progress-step {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: var(--color-gray-700);
    border: 2px solid var(--color-gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.progress-step-active {
    background-color: var(--color-primary-500);
    border-color: var(--color-primary-400);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.3);
}

.progress-step-complete {
    background-color: var(--color-success-500);
    border-color: var(--color-success-400);
}

/* Progress text */
.progress-text {
    text-align: center;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--color-gray-300);
}

.progress-percentage {
    font-weight: 600;
    color: var(--color-primary-400);
}

/* Progress with label */
.progress-with-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.progress-label {
    font-size: 0.875rem;
    color: var(--color-gray-300);
}

/* Spinner animation */
.spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid rgba(var(--color-primary-rgb), 0.3);
    border-radius: 50%;
    border-top-color: var(--color-primary-500);
    animation: spin 1s linear infinite;
    display: inline-block;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .progress-circular {
        width: 50px;
        height: 50px;
    }
    
    .progress-step {
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .progress-bar-container,
    .progress-indeterminate {
        height: 0.375rem;
    }
}
