#fun-zone-sidebar {
    position: fixed;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 380px;
    width: 300px;
    background-color: var(--bg-secondary);
    border-left: 2px solid var(--vibe-productive); /* Default productive color */
    box-shadow: var(--shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 40;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    overflow: hidden;
  }

  /* Border colors for different vibes */
  #fun-zone-sidebar.productive { border-left-color: var(--vibe-productive); }
  #fun-zone-sidebar.tired { border-left-color: var(--vibe-tired); }
  #fun-zone-sidebar.debugging { border-left-color: var(--vibe-debugging); }
  #fun-zone-sidebar.procrastinating { border-left-color: var(--vibe-procrastinating); }

  /* Collapsed state */
  #fun-zone-sidebar:not(.fun-zone-visible) {
    transform: translateX(calc(100% - 40px)) translateY(-50%);
  }

  /* Bottom Toggle Button */
  .bottom-toggle {
    position: absolute;
    left: 0px;
    bottom: 360px;
    color: white;
    padding: 2px;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 41;
  }

  .bottom-toggle:hover {
    background-color: #374151;
  }

  /* Toggle Icons */
  .toggle-icon-arrow,
  .toggle-icon-x {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: all 0.3s ease;
    transform-origin: center;
  }

  .toggle-icon-x {
    position: absolute;
    opacity: 0;
    transform: scale(0.8);
  }

  .fun-zone-visible .toggle-icon-arrow {
    opacity: 0;
    transform: scale(0.8);
  }

  .fun-zone-visible .toggle-icon-x {
    opacity: 1;
    transform: scale(1);
  }

  .bottom-toggle:hover .toggle-icon-arrow,
  .bottom-toggle:hover .toggle-icon-x {
    transform: scale(1.2);
  }

  /* Sidebar Content */
  .sidebar-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* Tab Headers */
  .tab-headers {
    display: flex;
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-tertiary);
  }

  .tab-btn {
    flex: 1;
    padding: 12px;
    color: var(--text-tertiary);
    position: relative;
    transition: all 0.2s ease;
    display: flex;
    justify-content: center;
  }

  .tab-btn:hover {
    color: var(--text-primary);
  }

  .tab-btn.active-tab {
    color: var(--text-primary);
  }

  .tab-btn.active-tab::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    height: 2px;
    width: 32px;
    background-color: var(--border-accent);
    opacity: 1;
  }

  .tab-icon {
    font-size: 1.25rem;
    transition: transform 0.3s ease;
  }

  .tab-btn:hover .tab-icon {
    transform: scale(1.2);
  }

  /* Collapsed state tabs */
  #fun-zone-sidebar:not(.fun-zone-visible) .tab-headers {
    flex-direction: column;
    border-bottom: none;
    border-right: 1px solid #4b5563;
  }

  #fun-zone-sidebar:not(.fun-zone-visible) .tab-btn {
    padding: 16px 12px;
  }

  #fun-zone-sidebar:not(.fun-zone-visible) .tab-btn.active-tab::after {
    left: 0;
    bottom: 50%;
    transform: translateY(50%);
    height: 24px;
    width: 2px;
  }

  /* Tab Contents */
  .tab-contents {
    flex-grow: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: var(--bg-secondary);
  }

  .tab-content {
    display: none;
    flex-direction: column;
    gap: 12px;
    height: 100%;
  }

  .tab-content:not(.hidden) {
    display: flex;
  }

  h3 {
    color: var(--text-primary);
    font-weight: 500;
    text-align: center;
    margin-bottom: 12px;
  }

  /* Confessions Tab */
  .confession-display {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-style: italic;
    padding: 12px;
    background-color: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
  }

  #confession-input {
    width: 100%;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 0.875rem;
    padding: 8px;
    border-radius: 6px;
    border: 1px solid var(--border-primary);
    resize: none;
    min-height: 80px;
    transition: all 0.2s ease;
  }
  /* Error and success messages */
  .error-message {
    color: var(--vibe-debugging);
    padding: 0.5rem;
    margin: 0.5rem 0;
    border-radius: 0.25rem;
    background-color: rgba(239, 68, 68, 0.1);
  }
  #confession-input:focus {
    outline: none;
    border-color: var(--border-accent);
    box-shadow: var(--shadow-sm);
  }

  #submit-confession {
    width: 100%;
    background-color: var(--btn-primary-bg);
    color: var(--btn-primary-text);
    border: 1px solid var(--border-accent);
    padding: 8px;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  #submit-confession:hover {
    filter: brightness(1.1);
  }

  #submit-confession:active {
    transform: scale(0.98);
  }

  /* Poll Tab */
  #poll-question {
    font-size: 0.875rem;
    color: #d1d5db;
    margin-bottom: 12px;
    text-align: center;
  }

  .poll-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .poll-option {
    text-align: left;
    padding: 8px 16px;
    background-color: rgba(55, 65, 81, 0.7);
    border-radius: 8px;
    border: 1px solid #4b5563;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
  }

  .poll-option:hover {
    background-color: rgba(75, 85, 99, 0.7);
    border-color: #6b7280;
  }

  .poll-option span:first-child {
    margin-right: 8px;
    transition: transform 0.2s ease;
  }

  /* Poll bar improvements */
  .poll-bar-fill {
    transition: width 0.5s ease-in-out;
    height: 100%;
    background-color: #3b82f6;
  }

  .poll-option:hover span:first-child {
    transform: scale(1.2);
  }

  #poll-results {
    margin-top: 16px;
  }

  .poll-result {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #9ca3af;
    margin-bottom: 4px;
  }

  .poll-bar {
    height: 4px;
    background-color: #374151;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 12px;
  }

  .poll-bar div {
    height: 100%;
    transition: width 0.5s ease;
  }

  #tabs-bar {
    background-color: #3b82f6;
  }

  #spaces-bar {
    background-color: #8b5cf6;
  }

  /* Vibe Tab */
  .vibe-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .vibe-btn {
    padding: 12px;
    background-color: transparent;
    border-radius: 9999px; /* Pill shape */
    border: 1px solid #6b7280;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .vibe-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .vibe-btn span:first-child {
    font-size: 1.5rem;
    margin-bottom: 4px;
    transition: transform 0.3s ease;
  }

  .vibe-btn:hover span:first-child {
    transform: scale(1.2) rotate(5deg);
  }

  .vibe-btn span:last-child {
    font-size: 0.75rem;
    color: #d1d5db;
  }

  /* Active vibe button */
  .vibe-btn.active-vibe {
    border-color: white;
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* Different colors for each vibe */
  [data-vibe="productive"] {
    border-color: #3b82f6;
  }
  [data-vibe="tired"] {
    border-color: #60a5fa;
  }
  [data-vibe="debugging"] {
    border-color: #ef4444;
  }
  [data-vibe="procrastinating"] {
    border-color: #f59e0b;
  }

  #vibe-result {
    padding: 8px;
    background-color: rgba(55, 65, 81, 0.5);
    border-radius: 6px;
    text-align: center;
    font-size: 0.875rem;
    color: #d1d5db;
  }

  /* History Tab */
  #history-loading {
    text-align: center;
    padding: 16px 0;
  }

  /* Loading states */
  .loading-spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 3px solid white;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  }


  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  #tech-history-text {
    font-size: 0.875rem;
    color: #d1d5db;
  }

  #history-link {
    font-size: 0.75rem;
    color: #60a5fa;
    text-align: center;
    display: block;
    margin-top: 8px;
    transition: color 0.2s ease;
  }

  #history-link:hover {
    color: #93c5fd;
  }

  /* Game (Tic-Tac-Toe) */
  .cell {
    width: 100%;
    height: 100%;
    transition: all 0.2s ease;
  }

  .cell:hover {
    background-color: rgba(75, 85, 99, 0.3);
  }

  .cell:focus {
    outline: none;
    box-shadow: var(--focus-ring);
    background-color: rgba(75, 85, 99, 0.3);
  }

  .winning-cell {
    background-color: rgba(59, 130, 246, 0.3) !important;
    border-color: rgba(59, 130, 246, 0.8) !important;
    animation: pulse 1.5s infinite;
  }

  /* Animations */
  @keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .animate-pulse {
    animation: pulse 1s ease-in-out;
  }

  .animate-bounce {
    animation: bounce 0.5s ease-in-out;
  }

  /* Responsive adjustments */
  @media only screen and (max-width: 767px) {
    #fun-zone-sidebar {
        display: none !important;
    }
  }