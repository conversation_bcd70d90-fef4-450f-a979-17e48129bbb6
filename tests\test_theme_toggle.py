"""
Tests for dark mode theme (theme toggle functionality removed).
"""

# flake8: noqa
import pytest
from flask import url_for


def test_theme_toggle_button_removed(client):
    """Test that the theme toggle button has been removed from the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check that theme toggle button is not present
    assert b'id="theme-toggle"' not in response.data
    assert b'id="mobile-theme-toggle"' not in response.data

    # Check that theme toggle icons are not present
    assert b"sun-icon" not in response.data
    assert b"moon-icon" not in response.data


def test_theme_css_included(client):
    """Test that the theme CSS file is included in the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for theme CSS link
    assert b'href="/static/css/theme.css"' in response.data
    assert b'rel="stylesheet"' in response.data


def test_theme_toggle_js_removed(client):
    """Test that the theme toggle JavaScript file has been removed from the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check that theme toggle JS script is not present
    assert b'src="/static/js/theme-toggle.js"' not in response.data


def test_html_has_dark_theme_only(client):
    """Test that the HTML element has only dark theme set."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for data-theme attribute on html element - should only be dark
    html_data = response.data.decode("utf-8")
    assert 'data-theme="dark"' in html_data
    # Ensure light theme is not present
    assert 'data-theme="light"' not in html_data
