"""
Smoke tests for API endpoints.
"""

# pytest is used as a fixture by the test runner
# flake8: noqa
import pytest
import json
from flask import url_for


def test_vibes_endpoint(client):
    """Test that the vibes endpoint returns valid JSON."""
    response = client.get("/tools/api/vibe")
    assert response.status_code == 200
    assert response.content_type == "application/json"
    data = json.loads(response.data)
    assert "vibes" in data


def test_confession_endpoint(client):
    """Test that the confession endpoint returns valid JSON."""
    response = client.get("/tools/api/confession")
    assert response.status_code == 200
    assert response.content_type == "application/json"
    data = json.loads(response.data)
    assert "confession" in data


def test_poll_votes_endpoint(client):
    """Test that the poll votes endpoint returns valid JSON."""
    response = client.get("/tools/api/poll")
    assert response.status_code == 200
    assert response.content_type == "application/json"
    data = json.loads(response.data)
    assert "results" in data
