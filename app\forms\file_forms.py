"""
Form classes for file conversion operations.
"""

from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileRequired
from wtforms import SelectField, SubmitField
from wtforms.validators import DataRequired


class ConvertFileForm(FlaskForm):
    """Form for single file conversion."""
    
    input_file = FileField("Input File", validators=[FileRequired(message="Please select a file")])
    output_format = SelectField(
        "Output Format",
        choices=[
            ("csv", "CSV"),
            ("xlsx", "Excel"),
            ("json", "JSON"),
            ("xml", "XML"),
            ("parquet", "Parquet"),
            ("pdf", "PDF"),
            ("docx", "Word Document"),
        ],
        validators=[DataRequired(message="Please select an output format")],
    )


class BatchConvertForm(FlaskForm):
    """Form for batch file conversion."""

    files = FileField(
        "Upload Files", 
        validators=[FileRequired(message="Please select at least one file")], 
        render_kw={"multiple": True}
    )
    output_format = SelectField(
        "Output Format",
        choices=[
            ("csv", "CSV"),
            ("xlsx", "Excel"),
            ("json", "JSON"),
            ("xml", "XML"),
            ("parquet", "Parquet"),
            ("pdf", "PDF"),
            ("docx", "Word Document"),
        ],
        validators=[DataRequired(message="Please select an output format")],
    )
    submit = SubmitField("Convert Files")
