@echo off
REM ImgCipherF1 Windows Setup Script
REM This batch file provides a simple way to run the Python setup script on Windows

echo ============================================================
echo                ImgCipherF1 Windows Setup
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.9+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found. Starting setup...
echo.

REM Default to development setup
set SETUP_ARGS=--dev

REM Check command line arguments
if "%1"=="--prod" set SETUP_ARGS=--prod
if "%1"=="--docker" set SETUP_ARGS=--docker
if "%1"=="--help" (
    echo Usage: setup.bat [--dev^|--prod^|--docker^|--help]
    echo.
    echo Options:
    echo   --dev      Setup for development ^(default^)
    echo   --prod     Setup for production
    echo   --docker   Setup for Docker environment
    echo   --help     Show this help message
    echo.
    echo Examples:
    echo   setup.bat           # Development setup
    echo   setup.bat --prod    # Production setup
    echo   setup.bat --docker  # Docker setup
    pause
    exit /b 0
)

echo Running Python setup script with arguments: %SETUP_ARGS%
echo.

REM Run the Python setup script
python setup.py %SETUP_ARGS%

if errorlevel 1 (
    echo.
    echo Setup failed! Check the error messages above.
    pause
    exit /b 1
)

echo.
echo ============================================================
echo                    Setup Complete!
echo ============================================================
echo.
echo To activate the virtual environment, run:
echo   venv\Scripts\activate
echo.
echo Then start the application with:
echo   python run.py
echo.
pause
