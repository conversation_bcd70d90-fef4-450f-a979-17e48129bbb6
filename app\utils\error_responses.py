"""
Error response utilities for the application.
"""

from typing import Dict, Any, Optional, <PERSON><PERSON>
from flask import jsonify


def create_error_response(
    message: str, status_code: int = 400, details: Optional[Dict[str, Any]] = None
) -> Tuple[Dict[str, Any], int]:
    """
    Create a standardized error response for API endpoints.

    Args:
        message: Main error message
        status_code: HTTP status code
        details: Optional dictionary with additional error details

    Returns:
        <PERSON>ple containing the error response dict and status code
    """
    response = {"success": False, "error": {"message": message, "code": status_code}}

    if details:
        response["error"]["details"] = details

    return jsonify(response), status_code
