import pandas as pd
import os
import tempfile
import subprocess
from flask import current_app
from werkzeug.datastructures import FileStorage
from werkzeug.utils import secure_filename
from PyPDF2 import PdfReader
import tabula
import docx
from docx2pdf import convert as docx_to_pdf_convert
from pdf2docx import Converter as PdfToDocxConverter


class FileConverter:
    SUPPORTED_FORMATS = {"csv", "xlsx", "json", "parquet", "xml", "pdf", "docx"}
    MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB

    @classmethod
    def convert(cls, input_file: FileStorage, output_path: str, output_format: str):
        """Handle the complete file conversion process."""
        cls._validate_input(input_file, output_format)

        # Get input file extension
        input_ext = input_file.filename.rsplit(".", 1)[1].lower()

        # Handle direct conversions between PDF and DOCX
        if (input_ext == "pdf" and output_format == "docx") or (input_ext == "docx" and output_format == "pdf"):
            # Save input file to temporary location
            temp_dir = os.path.join(current_app.config["UPLOAD_FOLDER"], "temp")
            os.makedirs(temp_dir, exist_ok=True)
            temp_input_path = os.path.join(temp_dir, secure_filename(input_file.filename))
            input_file.save(temp_input_path)

            try:
                # Perform direct conversion
                if input_ext == "pdf" and output_format == "docx":
                    cls.convert_pdf_to_docx(temp_input_path, output_path)
                elif input_ext == "docx" and output_format == "pdf":
                    cls.convert_docx_to_pdf(temp_input_path, output_path)
            finally:
                # Clean up temporary file
                if os.path.exists(temp_input_path):
                    os.remove(temp_input_path)
        else:
            # For other conversions, use the DataFrame approach
            df = cls._read_file(input_file, input_ext)
            cls._write_file(df, output_path, output_format)

    @classmethod
    def _validate_input(cls, input_file: FileStorage, output_format: str):
        """Validate input file and requested conversion."""
        if input_file.content_length > cls.MAX_FILE_SIZE:
            raise ValueError("File size exceeds 10MB limit")

        input_ext = input_file.filename.rsplit(".", 1)[1].lower()
        if input_ext not in cls.SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported input format: {input_ext}")
        if output_format not in cls.SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported output format: {output_format}")

    @classmethod
    def _read_file(cls, input_file: FileStorage, input_ext: str) -> pd.DataFrame:
        """Read the input file based on its extension."""
        temp_path = os.path.join(current_app.config["UPLOAD_FOLDER"], "temp", secure_filename(input_file.filename))
        os.makedirs(os.path.dirname(temp_path), exist_ok=True)
        input_file.save(temp_path)

        try:
            if input_ext == "csv":
                return cls._read_csv(temp_path)
            elif input_ext == "xlsx":
                return pd.read_excel(temp_path)
            elif input_ext == "json":
                return pd.read_json(temp_path)
            elif input_ext == "parquet":
                return pd.read_parquet(temp_path)
            elif input_ext == "xml":
                return pd.read_xml(temp_path)
            elif input_ext == "pdf":
                # Read PDF as DataFrame using tabula-py
                return cls._read_pdf(temp_path)
            elif input_ext == "docx":
                # Read DOCX as DataFrame
                return cls._read_docx(temp_path)
            else:
                raise ValueError(f"Unsupported input format: {input_ext}")
        finally:
            # Clean up temp file
            if os.path.exists(temp_path):
                os.remove(temp_path)

    @staticmethod
    def _read_pdf(file_path: str) -> pd.DataFrame:
        """Read PDF file and extract tables."""
        # Try to extract tables from PDF using tabula
        tables = tabula.read_pdf(file_path, pages="all")

        # If tables were found, concatenate them
        if tables and len(tables) > 0:
            return pd.concat(tables, ignore_index=True)

        # If no tables were found, create a DataFrame with text content
        pdf = PdfReader(file_path)
        data = []

        for i, page in enumerate(pdf.pages):
            text = page.extract_text()
            if text:
                data.append({"page": i + 1, "content": text})

        return pd.DataFrame(data)

    @staticmethod
    def _read_docx(file_path: str) -> pd.DataFrame:
        """Read DOCX file and convert to DataFrame."""
        doc = docx.Document(file_path)
        data = []

        for i, para in enumerate(doc.paragraphs):
            if para.text:
                data.append({"paragraph": i + 1, "content": para.text})

        return pd.DataFrame(data)

    @staticmethod
    def _read_csv(file_path: str) -> pd.DataFrame:
        """Read large CSV files in chunks."""
        chunks = []
        for chunk in pd.read_csv(file_path, chunksize=10000):
            chunks.append(chunk)
        return pd.concat(chunks)

    @classmethod
    def _write_file(cls, df: pd.DataFrame, output_path: str, output_ext: str):
        """Write dataframe to the specified output format."""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Handle standard data formats
        if output_ext == "csv":
            df.to_csv(output_path, index=False)
        elif output_ext == "xlsx":
            df.to_excel(output_path, index=False)
        elif output_ext == "json":
            df.to_json(output_path)
        elif output_ext == "parquet":
            df.to_parquet(output_path)
        elif output_ext == "xml":
            df.to_xml(output_path)
        # Handle PDF output
        elif output_ext == "pdf":
            cls._convert_to_pdf(df, output_path)
        # Handle DOCX output
        elif output_ext == "docx":
            cls._convert_to_docx(df, output_path)
        else:
            raise ValueError(f"Unsupported output format: {output_ext}")

    @staticmethod
    def _convert_to_pdf(df: pd.DataFrame, output_path: str):
        """Convert DataFrame to PDF."""
        # Create a temporary HTML file
        with tempfile.NamedTemporaryFile(suffix=".html", delete=False) as temp_html:
            temp_html_path = temp_html.name
            # Convert DataFrame to HTML
            html_content = df.to_html(index=False)
            temp_html.write(html_content.encode("utf-8"))

        try:
            # Use wkhtmltopdf to convert HTML to PDF
            subprocess.run(["wkhtmltopdf", "--quiet", temp_html_path, output_path], check=True)
        except subprocess.CalledProcessError as e:
            raise ValueError(f"PDF conversion failed: {str(e)}")
        finally:
            # Clean up temporary file
            if os.path.exists(temp_html_path):
                os.remove(temp_html_path)

    @staticmethod
    def _convert_to_docx(df: pd.DataFrame, output_path: str):
        """Convert DataFrame to DOCX."""
        # Create a new Word document
        doc = docx.Document()

        # Add a title
        doc.add_heading("Data Export", level=1)

        # Add a table
        table = doc.add_table(rows=1, cols=len(df.columns))
        table.style = "Table Grid"

        # Add headers
        header_cells = table.rows[0].cells
        for i, column in enumerate(df.columns):
            header_cells[i].text = str(column)

        # Add data rows
        for _, row in df.iterrows():
            row_cells = table.add_row().cells
            for i, value in enumerate(row):
                row_cells[i].text = str(value)

        # Save the document
        doc.save(output_path)

    @classmethod
    def convert_pdf_to_docx(cls, input_path: str, output_path: str):
        """Convert PDF to DOCX using pdf2docx."""
        converter = PdfToDocxConverter(input_path)
        converter.convert(output_path)
        converter.close()

    @classmethod
    def convert_docx_to_pdf(cls, input_path: str, output_path: str):
        """Convert DOCX to PDF using docx2pdf."""
        docx_to_pdf_convert(input_path, output_path)
