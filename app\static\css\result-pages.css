/**
 * Result Pages CSS
 * Provides consistent styling for conversion result pages
 */

/* Base result container */
.result-container {
    width: 100%;
    max-width: 2xl;
    background-color: var(--color-gray-800);
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    margin-top: 1.5rem;
}

/* Success icon animation */
@keyframes success-checkmark {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-icon {
    animation: success-checkmark 0.5s ease-in-out forwards;
}

/* File card styling */
.file-card {
    background-color: var(--color-gray-700);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.file-card.success {
    border-left: 3px solid var(--color-success-500);
}

.file-card.error {
    border-left: 3px solid var(--color-error-500);
}

/* File details section */
.file-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--color-gray-600);
    font-size: 0.875rem;
    color: var(--color-gray-400);
}

.file-details-row {
    display: flex;
    justify-content: space-between;
}

.file-details-label {
    color: var(--color-gray-500);
}

.file-details-value {
    color: var(--color-gray-300);
    font-family: monospace;
}

/* Download button styling */
.download-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: var(--color-primary-600);
    color: white;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.2s;
}

.download-button:hover {
    background-color: var(--color-primary-700);
}

.download-button svg {
    margin-right: 0.5rem;
    height: 1rem;
    width: 1rem;
}

/* Next steps section */
.next-steps {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--color-gray-700);
}

.next-steps-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-gray-200);
    margin-bottom: 1rem;
}

.next-steps-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.next-step-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.next-step-icon {
    flex-shrink: 0;
    height: 1.5rem;
    width: 1.5rem;
    color: var(--color-primary-500);
}

.next-step-content {
    flex: 1;
}

.next-step-title {
    font-weight: 500;
    color: var(--color-gray-200);
    margin-bottom: 0.25rem;
}

.next-step-description {
    font-size: 0.875rem;
    color: var(--color-gray-400);
}

/* Related tools section */
.related-tools {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--color-gray-700);
}

.related-tools-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-gray-200);
    margin-bottom: 1rem;
}

.related-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
}

.related-tool-card {
    background-color: var(--color-gray-700);
    border-radius: 0.375rem;
    padding: 1rem;
    transition: background-color 0.2s;
    text-align: center;
}

.related-tool-card:hover {
    background-color: var(--color-gray-600);
}

.related-tool-icon {
    height: 2rem;
    width: 2rem;
    margin: 0 auto 0.5rem auto;
    color: var(--color-primary-500);
}

.related-tool-name {
    font-weight: 500;
    color: var(--color-gray-200);
    margin-bottom: 0.25rem;
}

.related-tool-description {
    font-size: 0.75rem;
    color: var(--color-gray-400);
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .related-tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .file-details-row {
        flex-direction: column;
        gap: 0.25rem;
    }
}
