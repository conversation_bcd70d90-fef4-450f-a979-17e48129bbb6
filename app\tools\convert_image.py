# File: ImgCipher/tools/convert_image.py

import os
import shutil
from PIL import Image
from flask import current_app as app


def convert_image(input_path: str, orig_filename: str, output_format: str, quality: int, output_folder: str) -> str:
    """
    Convert an image to a specified format.

    Args:
        input_path (str): Path to the input image file.
        orig_filename (str): Original filename (used for naming the output file).
        output_format (str): Desired output format ('webp', 'jpg', 'jpeg', 'png', 'ico', or 'gif').
        quality (int): Quality setting for lossy formats (e.g., JPEG, WebP). Should be between 0 and 100.
        output_folder (str): Folder where the converted file will be saved.

    Returns:
        str: The output filename if conversion is successful.

    Raises:
        Exception: If any error occurs during conversion.
    """
    name_without_ext = os.path.splitext(orig_filename)[0]
    output_folder = app.config["OUTPUT_FOLDER"]
    output_filename = f"{name_without_ext}.{output_format}"
    output_path = os.path.join(output_folder, output_filename)

    input_ext = os.path.splitext(orig_filename)[1].lower().lstrip(".")
    try:
        if input_ext == output_format:
            # If the file is already in the desired format, just copy it.
            shutil.copy(input_path, output_path)
        else:
            with Image.open(input_path) as img:
                # Convert image to RGB to ensure compatibility (especially for JPEG).
                img = img.convert("RGB")
                if output_format == "webp":
                    img.save(output_path, "WEBP", quality=quality)
                elif output_format in ["jpg", "jpeg"]:
                    img.save(output_path, "JPEG", quality=quality)
                elif output_format == "png":
                    # For PNG, use optimize=True and a compression level based on quality
                    # Higher quality = less compression
                    compression_level = max(1, min(9, 10 - (quality // 10)))
                    img.save(output_path, "PNG", optimize=True, compress_level=compression_level)
                elif output_format == "ico":
                    img.save(output_path, "ICO")
                elif output_format == "gif":
                    img.save(output_path, "GIF")
                else:
                    img.save(output_path)
    except Exception as e:
        raise Exception(f"Error converting {orig_filename}: {e}") from e

    return output_filename
