2025-03-29 20:04:07,076 - root - INFO - Application startup completed [config.py:182]
2025-03-29 20:05:31,521 - root - INFO - Application startup completed [config.py:182]
2025-03-29 20:08:03,388 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:08:04,286 - app - INFO - Registered blueprint: main at / [__init__.py:66]
2025-03-29 20:08:04,288 - app - INFO - Registered blueprint: tools at /tools [__init__.py:66]
2025-03-29 20:08:04,290 - app - INFO - Registered blueprint: privacy at /privacy [__init__.py:66]
2025-03-29 20:08:04,291 - app - INFO - Registered blueprint: terms at /terms [__init__.py:66]
2025-03-29 20:08:04,292 - app - INFO - Celery initialized [__init__.py:70]
2025-03-29 20:08:04,293 - flask_migrate - ERROR - Error: Path doesn't exist: 'C:\\Users\\<USER>\\Projects\\ImgCipherF1\\migrations'.  Please use the 'init' command to create a new scripts folder. [__init__.py:113]
2025-03-29 20:08:56,086 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:08:57,210 - app - INFO - Registered blueprint: main at / [__init__.py:66]
2025-03-29 20:08:57,213 - app - INFO - Registered blueprint: tools at /tools [__init__.py:66]
2025-03-29 20:08:57,214 - app - INFO - Registered blueprint: privacy at /privacy [__init__.py:66]
2025-03-29 20:08:57,215 - app - INFO - Registered blueprint: terms at /terms [__init__.py:66]
2025-03-29 20:08:57,217 - app - INFO - Celery initialized [__init__.py:70]
2025-03-29 20:08:57,218 - flask_migrate - ERROR - Error: Path doesn't exist: 'C:\\Users\\<USER>\\Projects\\ImgCipherF1\\migrations'.  Please use the 'init' command to create a new scripts folder. [__init__.py:113]
2025-03-29 20:09:48,476 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:09:49,591 - app - INFO - Registered blueprint: main at / [__init__.py:66]
2025-03-29 20:09:49,595 - app - INFO - Registered blueprint: tools at /tools [__init__.py:66]
2025-03-29 20:09:49,597 - app - INFO - Registered blueprint: privacy at /privacy [__init__.py:66]
2025-03-29 20:09:49,598 - app - INFO - Registered blueprint: terms at /terms [__init__.py:66]
2025-03-29 20:09:49,600 - app - INFO - Celery initialized [__init__.py:70]
2025-03-29 20:09:49,601 - flask_migrate - ERROR - Error: Path doesn't exist: 'C:\\Users\\<USER>\\Projects\\ImgCipherF1\\migrations'.  Please use the 'init' command to create a new scripts folder. [__init__.py:113]
2025-03-29 20:11:59,780 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:12:00,650 - flask_migrate - ERROR - Error: Path doesn't exist: 'C:\\Users\\<USER>\\Projects\\ImgCipherF1\\migrations'.  Please use the 'init' command to create a new scripts folder. [__init__.py:113]
2025-03-29 20:12:21,252 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:12:22,094 - flask_migrate - ERROR - Error: Path doesn't exist: 'C:\\Users\\<USER>\\Projects\\ImgCipherF1\\migrations'.  Please use the 'init' command to create a new scripts folder. [__init__.py:113]
2025-03-29 20:13:39,008 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:13:39,924 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:13:39,925 - flask_migrate - ERROR - Error: Path doesn't exist: 'C:\\Users\\<USER>\\Projects\\ImgCipherF1\\migrations'.  Please use the 'init' command to create a new scripts folder. [__init__.py:113]
2025-03-29 20:14:12,221 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:14:13,093 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:14:13,094 - app - WARNING - Migration failed: Can't find Python file migrations\env.py [__init__.py:63]
2025-03-29 20:14:13,099 - app - INFO - Registered blueprint: main at / [__init__.py:87]
2025-03-29 20:14:13,100 - app - INFO - Registered blueprint: tools at /tools [__init__.py:87]
2025-03-29 20:14:13,101 - app - INFO - Registered blueprint: privacy at /privacy [__init__.py:87]
2025-03-29 20:14:13,102 - app - INFO - Registered blueprint: terms at /terms [__init__.py:87]
2025-03-29 20:14:13,104 - app - INFO - Celery initialized [__init__.py:91]
2025-03-29 20:14:13,104 - app - INFO - Application initialized in development mode [__init__.py:102]
2025-03-29 20:15:59,088 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:15:59,981 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:16:10,140 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:16:10,958 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:16:26,295 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:16:27,166 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:16:38,053 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:16:38,953 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:16:40,068 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:16:40,985 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:24:24,069 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:32:10,972 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:32:43,737 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:32:44,718 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:32:45,847 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:32:46,845 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 20:45:57,761 - root - INFO - Application startup completed [config.py:185]
2025-03-29 20:45:59,226 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 21:17:12,715 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\1d8de9259c4a47b593004b6fe47571d4_Slide_3.png [config.py:91]
2025-03-29 21:17:12,715 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\663fb828c2ee4b8faa3061ae84d512de_Slide_1.png [config.py:91]
2025-03-29 21:17:12,718 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\9ad83ba9fff246e88df55ed75c6f3d73_Slide_1.png [config.py:91]
2025-03-29 21:17:12,720 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\9fde2609feb144bbb737d4463f1eb6d8_Slide_2.png [config.py:91]
2025-03-29 21:17:12,720 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\c3262b13f1f44e26926da5c545f302bf_Slide_1.png [config.py:91]
2025-03-29 21:17:12,723 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\merged_1743268685.pdf [config.py:91]
2025-03-29 21:17:12,723 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\split.pdf [config.py:91]
2025-03-29 21:17:12,723 - root - INFO - Application startup completed [config.py:185]
2025-03-29 21:17:15,475 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 21:21:19,430 - root - INFO - Application startup completed [config.py:185]
2025-03-29 21:21:22,363 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 21:47:49,681 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\split.pdf [config.py:91]
2025-03-29 21:47:49,684 - root - INFO - Application startup completed [config.py:185]
2025-03-29 21:47:53,507 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 21:50:28,497 - root - INFO - Application startup completed [config.py:185]
2025-03-29 21:50:32,780 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 21:51:27,761 - root - INFO - Application startup completed [config.py:185]
2025-03-29 21:51:30,163 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 21:52:49,524 - root - INFO - Application startup completed [config.py:185]
2025-03-29 21:52:52,742 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 22:14:31,606 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\merged_1743274601.pdf [config.py:91]
2025-03-29 22:14:31,607 - root - INFO - Application startup completed [config.py:185]
2025-03-29 22:14:32,790 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 22:19:58,717 - root - INFO - Application startup completed [config.py:185]
2025-03-29 22:20:23,078 - root - INFO - Application startup completed [config.py:185]
2025-03-29 22:20:24,207 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 22:20:25,595 - root - INFO - Application startup completed [config.py:185]
2025-03-29 22:20:26,946 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-29 22:24:06,066 - root - INFO - Application startup completed [config.py:185]
2025-03-29 22:24:07,452 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 00:38:36,955 - root - INFO - Application startup completed [config.py:185]
2025-03-30 00:38:46,546 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 00:40:44,412 - root - INFO - Application startup completed [config.py:185]
2025-03-30 00:40:45,461 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 00:43:03,230 - root - INFO - Application startup completed [config.py:185]
2025-03-30 00:43:04,140 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 00:43:18,787 - root - INFO - Application startup completed [config.py:185]
2025-03-30 00:43:19,823 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 00:45:54,949 - root - INFO - Application startup completed [config.py:185]
2025-03-30 00:45:56,117 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 00:47:01,256 - root - INFO - Application startup completed [config.py:185]
2025-03-30 00:47:02,227 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 01:15:17,665 - root - INFO - Application startup completed [config.py:185]
2025-03-30 01:15:19,664 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 01:20:23,642 - root - INFO - Application startup completed [config.py:185]
2025-03-30 01:20:24,991 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 01:20:50,255 - root - INFO - Application startup completed [config.py:185]
2025-03-30 01:20:51,472 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 01:53:02,987 - root - INFO - Application startup completed [config.py:185]
2025-03-30 01:53:03,949 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 01:53:05,241 - root - INFO - Application startup completed [config.py:185]
2025-03-30 01:53:06,243 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 13:23:11,173 - root - INFO - Application startup completed [config.py:185]
2025-03-30 13:23:20,778 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-30 13:23:22,315 - root - INFO - Application startup completed [config.py:185]
2025-03-30 13:23:23,186 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:28:02,797 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:28:13,919 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:28:17,671 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:28:18,448 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:32:07,819 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:32:09,053 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:32:28,351 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:32:29,141 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:32:30,179 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:32:31,039 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:34:27,711 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:34:28,634 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:36:24,078 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:36:26,352 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:37:14,562 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:37:15,487 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:37:24,165 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:37:25,181 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 09:37:26,348 - root - INFO - Application startup completed [config.py:185]
2025-03-31 09:37:27,464 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:05:13,957 - root - INFO - Application startup completed [config.py:185]
2025-03-31 10:05:20,543 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:05:40,644 - root - INFO - Application startup completed [config.py:185]
2025-03-31 10:05:41,627 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:05:58,993 - root - INFO - Application startup completed [config.py:185]
2025-03-31 10:05:59,967 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:06:00,995 - root - INFO - Application startup completed [config.py:185]
2025-03-31 10:06:02,117 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:34:38,248 - root - INFO - Application startup completed [config.py:185]
2025-03-31 10:34:39,529 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:38:57,029 - root - INFO - Application startup completed [config.py:185]
2025-03-31 10:48:53,789 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:48:56,009 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:48:57,393 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:48:58,345 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:49:12,311 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:49:13,172 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:49:19,657 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:49:20,639 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:49:21,739 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:49:22,970 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:50:12,575 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:50:13,653 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:51:35,347 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:51:36,219 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:51:43,778 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:51:44,631 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:51:56,989 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:51:57,912 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 10:51:59,396 - root - INFO - Application startup completed [config.py:186]
2025-03-31 10:52:00,489 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:17:01,581 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:17:02,601 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:17:22,823 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:17:23,951 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:17:53,821 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:17:54,819 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:22:02,577 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:22:03,573 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:22:04,769 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:22:05,801 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:25:43,906 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:25:45,016 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:26:26,441 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:26:27,652 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:44:47,270 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:44:48,326 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:46:05,215 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:46:06,463 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 11:55:37,709 - root - INFO - Application startup completed [config.py:186]
2025-03-31 11:55:38,912 - app - INFO - Database migrations initialized [__init__.py:54]
2025-03-31 12:18:05,633 - root - INFO - Application startup completed [config.py:186]
2025-03-31 12:18:06,941 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 01:48:20,894 - root - INFO - Application startup completed [config.py:186]
2025-04-01 01:48:33,160 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 01:48:35,087 - root - INFO - Application startup completed [config.py:186]
2025-04-01 01:48:36,429 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 02:05:45,615 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\converted_test_file.csv [config.py:92]
2025-04-01 02:05:45,615 - root - INFO - Application startup completed [config.py:186]
2025-04-01 02:05:47,198 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 02:06:36,152 - root - INFO - Application startup completed [config.py:186]
2025-04-01 02:06:37,954 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 02:07:39,846 - root - INFO - Application startup completed [config.py:186]
2025-04-01 02:07:41,742 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 17:37:57,127 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\test_file.csv [config.py:92]
2025-04-01 17:37:57,130 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\test_file.json [config.py:92]
2025-04-01 17:37:57,130 - root - INFO - Application startup completed [config.py:186]
2025-04-01 17:38:08,888 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 17:38:10,455 - root - INFO - Application startup completed [config.py:186]
2025-04-01 17:38:11,657 - app - INFO - Database migrations initialized [__init__.py:54]
2025-04-01 18:07:23,387 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\test_file.csv [config.py:92]
2025-04-01 18:07:23,388 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\test_file.json [config.py:92]
2025-04-01 18:07:23,388 - root - INFO - Application startup completed [config.py:186]
2025-04-01 18:07:24,944 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-07 00:48:35,173 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\test_file.json [config.py:92]
2025-05-07 00:48:35,175 - root - INFO - Application startup completed [config.py:186]
2025-05-07 00:48:47,325 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-07 00:48:49,424 - root - INFO - Application startup completed [config.py:186]
2025-05-07 00:48:50,722 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-07 00:56:37,511 - root - INFO - Application startup completed [config.py:186]
2025-05-07 00:56:38,676 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-07 00:57:13,170 - root - INFO - Application startup completed [config.py:186]
2025-05-07 00:57:14,412 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-07 01:00:24,008 - root - INFO - Application startup completed [config.py:186]
2025-05-07 01:00:25,621 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-07 01:00:33,434 - root - INFO - Application startup completed [config.py:186]
2025-05-07 01:00:34,773 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 10:07:34,490 - root - INFO - Application startup completed [config.py:186]
2025-05-14 10:07:51,887 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:36:36,064 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:36:57,320 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:37:56,622 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:38:02,246 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:38:43,059 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:38:50,545 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:39:07,302 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:39:14,651 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:39:29,537 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:39:37,752 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:42:41,915 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:42:47,345 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:42:57,358 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:43:04,991 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:43:17,329 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:43:22,330 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:47:59,549 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:48:06,146 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:53:33,517 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:54:52,790 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:56:44,260 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:56:51,513 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 14:57:02,475 - root - INFO - Application startup completed [config.py:186]
2025-05-14 14:57:09,279 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 17:46:54,940 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:46:59,346 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 17:47:09,636 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:47:11,175 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 17:47:33,976 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:47:36,883 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 17:47:49,219 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:47:51,688 - app - INFO - Database migrations initialized [__init__.py:54]
2025-05-14 17:56:19,103 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:56:33,962 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:57:20,974 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:57:53,675 - root - INFO - Application startup completed [config.py:186]
2025-05-14 17:57:55,719 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:00:37,192 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:00:38,809 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:01:37,997 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:01:39,730 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:01:49,448 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:01:52,275 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:02:49,847 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:02:51,340 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:03:02,751 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:03:05,224 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:03:18,763 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:03:20,684 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:14:57,886 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:14:59,499 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:16:31,953 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:16:33,568 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:17:41,380 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:17:43,363 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:18:50,578 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:18:58,907 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:20:05,564 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:20:07,109 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:20:22,187 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:20:26,121 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:21:20,805 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:21:22,431 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:21:51,043 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:21:55,256 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:23:19,375 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:23:21,483 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:24:04,430 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:24:05,924 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:24:18,154 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:24:19,930 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:40:42,246 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\080adea8368646638fae20bce58fef9e_Screenshot_2025-02-12_093920.webp [config.py:92]
2025-05-14 18:40:42,247 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\0a559f8ae47c4bbd94f4a70ebbc9c4c7_Screenshot_2025-02-04_180927.webp [config.py:92]
2025-05-14 18:40:42,247 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\13169d4ef5c54539999ee8945c59f135_Screenshot_2025-02-09_150919.webp [config.py:92]
2025-05-14 18:40:42,248 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\304ea979ac7d4377a6082ba34d4f6f93_Screenshot_2025-02-12_204708.webp [config.py:92]
2025-05-14 18:40:42,249 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\38f1e1577c3a4b718b9748231dd8f756_Screenshot_2025-02-11_123507.webp [config.py:92]
2025-05-14 18:40:42,249 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\4c0d8c74b7c441f8beb3366cfb0f83f3_Screenshot_2025-02-07_133220.webp [config.py:92]
2025-05-14 18:40:42,250 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\bfec26a7a4204533b2a80fca88238af9_Screenshot_2025-02-01_200902.webp [config.py:92]
2025-05-14 18:40:42,250 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\f578216d3ce5488a97c476d2548ccc8a_Screenshot_2025-02-01_184720.webp [config.py:92]
2025-05-14 18:40:42,250 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\fbc57f96e7d5402ca37697b689a1bd3a_Screenshot_2025-02-12_204639.webp [config.py:92]
2025-05-14 18:40:42,251 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:40:44,807 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:42:34,785 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:42:36,642 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:44:21,259 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:44:23,434 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:47:16,700 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:47:18,911 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:47:29,527 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:47:31,729 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 18:49:11,275 - root - INFO - Application startup completed [config.py:186]
2025-05-14 18:49:13,839 - app - INFO - Database migrations initialized [__init__.py:64]
2025-05-14 19:09:29,541 - root - INFO - Application startup completed [config.py:188]
2025-05-14 19:09:31,707 - app - INFO - Database migrations initialized [__init__.py:67]
2025-05-14 19:17:27,248 - root - INFO - Application startup completed [config.py:188]
2025-05-14 19:17:28,956 - app - INFO - Database migrations initialized [__init__.py:67]
2025-05-14 19:18:00,972 - root - INFO - Application startup completed [config.py:188]
2025-05-14 19:18:02,624 - app - INFO - Database migrations initialized [__init__.py:67]
2025-05-14 19:19:32,548 - root - INFO - Application startup completed [config.py:188]
2025-05-14 19:19:34,264 - app - INFO - Database migrations initialized [__init__.py:67]
2025-05-14 19:28:26,251 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:28:27,979 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:30:00,026 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:30:01,795 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:31:11,383 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:31:13,065 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:32:29,712 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:32:42,827 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:32:44,503 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:40:18,332 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:40:20,276 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:53:32,686 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\split.pdf [config.py:99]
2025-05-14 19:53:32,688 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:53:34,259 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:53:35,613 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:53:37,098 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:56:00,258 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:56:01,793 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 19:56:03,018 - root - INFO - Application startup completed [config.py:194]
2025-05-14 19:56:04,426 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 20:31:47,952 - root - INFO - Application startup completed [config.py:194]
2025-05-14 20:31:49,740 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 20:31:51,357 - root - INFO - Application startup completed [config.py:194]
2025-05-14 20:31:52,873 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 20:38:47,463 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\077ca152965046da865ff2d8dd351527_Nanam_services_2025.webp [config.py:99]
2025-05-14 20:38:47,465 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\22f33096_split_2.docx [config.py:99]
2025-05-14 20:38:47,466 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\97a115316c1a400c89957cee73effab7_Nanam_services_2025.webp [config.py:99]
2025-05-14 20:38:47,468 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\merged_1747244026.pdf [config.py:99]
2025-05-14 20:38:47,469 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\merged_1747244049.pdf [config.py:99]
2025-05-14 20:38:47,470 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\split.pdf [config.py:99]
2025-05-14 20:38:47,470 - root - INFO - Application startup completed [config.py:194]
2025-05-14 20:38:49,337 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 20:57:57,114 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\split.pdf [config.py:99]
2025-05-14 20:57:57,114 - root - INFO - Application startup completed [config.py:194]
2025-05-14 20:57:59,030 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 20:59:19,355 - root - INFO - Application startup completed [config.py:194]
2025-05-14 20:59:20,912 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 20:59:32,989 - root - INFO - Application startup completed [config.py:194]
2025-05-14 20:59:34,353 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 20:59:46,674 - root - INFO - Application startup completed [config.py:194]
2025-05-14 20:59:48,004 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:08:30,505 - root - INFO - Cleaned up old file: C:\Users\<USER>\Projects\ImgCipherF1\data\outputs\5a451046_merged_1747244049.docx [config.py:99]
2025-05-14 21:08:30,506 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:08:32,337 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:12:04,222 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:12:05,679 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:17:29,270 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:17:30,970 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:17:39,965 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:17:42,071 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:17:50,318 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:17:51,863 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:18:17,526 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:18:19,215 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:18:41,536 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:19:49,171 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:19:51,220 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:20:18,720 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:20:20,671 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:20:42,325 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:20:44,058 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:21:58,571 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:22:00,644 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:22:15,485 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:22:17,201 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:22:57,984 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:22:59,512 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:23:01,066 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:23:02,756 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:23:25,062 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:23:26,563 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:23:57,635 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:23:59,461 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:24:32,868 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:24:34,428 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:24:44,643 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:24:46,123 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:08,199 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:09,740 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:11,096 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:12,678 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:28,960 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:29,345 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:30,797 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:31,250 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:39,070 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:40,508 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:41,316 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:42,953 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:54,634 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:56,128 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:25:57,595 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:25:59,555 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:26:34,901 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:26:36,482 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:26:55,150 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:26:55,400 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:26:56,784 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:26:57,032 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:27:08,127 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:27:09,880 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:29:23,830 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:29:23,894 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:29:54,103 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:29:54,103 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:30:07,856 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:30:07,856 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:30:11,254 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:30:13,064 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:30:15,726 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:30:16,476 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:30:20,010 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:30:20,872 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:39:37,448 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:39:40,670 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:39:41,915 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:39:45,268 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:41:03,775 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:41:07,536 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 21:43:45,740 - root - INFO - Application startup completed [config.py:194]
2025-05-14 21:43:49,470 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:11:04,124 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:11:24,084 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:11:53,088 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:11:57,341 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:12:21,348 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:12:25,481 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:12:34,539 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:12:38,516 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:14:32,549 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:14:36,280 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:14:57,588 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:15:03,411 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:15:10,236 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:15:15,843 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:17:52,811 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:18:16,675 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:26:19,786 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:26:23,792 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:26:33,796 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:26:37,745 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:26:38,334 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:26:42,481 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:29:19,885 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:29:24,140 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-14 22:44:54,088 - root - INFO - Application startup completed [config.py:194]
2025-05-14 22:44:57,669 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-15 10:50:52,540 - root - INFO - Application startup completed [config.py:194]
2025-05-15 10:51:25,646 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-15 11:01:09,337 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:01:13,718 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-15 11:16:42,824 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:16:47,105 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-15 11:20:29,722 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:21:06,954 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:21:10,572 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-15 11:21:17,930 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:21:21,563 - app - INFO - Database migrations initialized [__init__.py:68]
2025-05-15 11:25:23,058 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:25:27,157 - app - INFO - Database migrations initialized [__init__.py:69]
2025-05-15 11:26:01,708 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:26:06,414 - app - INFO - Database migrations initialized [__init__.py:69]
2025-05-15 11:45:25,387 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:45:30,042 - app - INFO - Database migrations initialized [__init__.py:69]
2025-05-15 11:53:33,649 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:53:38,183 - app - INFO - Database migrations initialized [__init__.py:69]
2025-05-15 11:53:50,219 - root - INFO - Application startup completed [config.py:194]
2025-05-15 11:53:54,068 - app - INFO - Database migrations initialized [__init__.py:69]
2025-05-15 12:01:31,269 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:01:35,102 - app - INFO - Database migrations initialized [__init__.py:70]
2025-05-15 12:19:06,040 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:25:16,517 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:32:14,322 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:34:31,934 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:35:31,523 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:36:50,298 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:37:41,870 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:38:51,626 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:40:40,765 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:41:44,808 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:43:50,421 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:50:38,306 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:53:01,258 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:54:59,038 - root - INFO - Application startup completed [config.py:194]
2025-05-15 12:57:57,568 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:00:09,592 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:00:56,596 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:09:40,355 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:09:45,186 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:09:46,767 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:09:50,985 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:18:36,781 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:18:40,184 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:18:41,529 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:18:45,223 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:20:29,919 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:20:33,819 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:20:35,249 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:20:40,020 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:21:19,109 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:21:23,391 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:21:31,345 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:21:35,797 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:21:55,108 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:21:59,349 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:22:00,780 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:22:05,389 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:23:51,101 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:23:54,806 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:23:56,165 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:24:00,443 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:26:43,007 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:26:47,218 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:26:54,010 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:26:58,275 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:27:04,191 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:27:08,678 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:27:14,393 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:27:19,330 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:27:23,301 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:27:27,134 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:27:28,492 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:27:32,633 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:28:52,064 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:28:55,431 - app - INFO - Database migrations initialized [__init__.py:71]
2025-05-15 13:28:56,937 - root - INFO - Application startup completed [config.py:194]
2025-05-15 13:29:01,477 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:52:30,443 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:52:51,329 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:54:16,600 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:55:21,504 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:55:24,213 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:55:27,992 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:55:31,417 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:55:35,232 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:55:54,784 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:55:57,259 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:56:06,885 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:56:09,234 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:56:10,061 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:56:12,352 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:56:50,067 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:56:52,382 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 00:56:57,502 - root - INFO - Application startup completed [config.py:194]
2025-07-26 00:57:00,148 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:06:41,891 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:08:16,718 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:08:19,090 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:08:20,122 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:08:22,618 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:08:52,852 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:09:04,666 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:09:24,731 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:09:46,608 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:10:28,661 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:10:31,253 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:10:32,093 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:10:34,647 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:13:30,654 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:13:31,735 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:13:36,628 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:13:37,384 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:13:46,181 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:13:47,733 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:13:49,552 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:13:50,994 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:14:26,018 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:14:27,616 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:14:29,878 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:14:32,350 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:24:00,463 - root - INFO - Cleaned up old file: D:\TCL1\Projects\ImgCipherF1\data\outputs\split.pdf [config.py:99]
2025-07-26 01:24:00,464 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:24:24,932 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:24:24,932 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:24:58,146 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:26:15,144 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:26:51,181 - root - INFO - Cleaned up old file: D:\TCL1\Projects\ImgCipherF1\data\uploads\ce902679c0de4f359bf65b7cb3301c06_Molina_Properties_Logo_sq1.jpg [config.py:99]
2025-07-26 01:26:51,181 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:28:10,318 - root - INFO - Cleaned up old file: D:\TCL1\Projects\ImgCipherF1\data\uploads\5eaab64e489e4ae3935491ffd598b123_Molina_Properties_Logo_sq1.jpg [config.py:99]
2025-07-26 01:28:10,319 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:28:25,561 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:28:27,161 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:28:32,939 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:28:33,056 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:28:38,991 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:29:20,071 - root - INFO - Cleaned up old file: D:\TCL1\Projects\ImgCipherF1\data\outputs\9506e9202ee849138da3e645f474f808_Molina_Properties_Logo_sq1_nobg.png [config.py:99]
2025-07-26 01:29:20,072 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:29:21,606 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:29:23,202 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:29:24,527 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:29:36,139 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:29:37,696 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:29:39,379 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:29:40,542 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:29:54,548 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:29:55,741 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:29:56,684 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:29:57,920 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:30:42,995 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:30:44,332 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:30:45,151 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:30:46,382 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:38:59,380 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:38:59,380 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:39:01,644 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:39:01,663 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:39:02,274 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:39:03,594 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:39:11,950 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:39:12,015 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:39:13,641 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:39:14,401 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:39:14,455 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:39:15,640 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:39:37,217 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:39:39,202 - app - INFO - Database migrations initialized [__init__.py:71]
2025-07-26 01:39:40,112 - root - INFO - Application startup completed [config.py:194]
2025-07-26 01:39:41,391 - app - INFO - Database migrations initialized [__init__.py:71]
