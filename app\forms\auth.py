"""
Authentication forms for the application.
"""

from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, BooleanField, SubmitField, EmailField
from wtforms.validators import DataRequired, Length, Email, EqualTo, ValidationError
from app.models.auth import User


class LoginForm(FlaskForm):
    """Form for user login."""

    username = StringField("Username", validators=[DataRequired()])
    password = PasswordField("Password", validators=[DataRequired()])
    remember_me = <PERSON><PERSON>anField("Remember Me")
    submit = SubmitField("Sign In")


class RegistrationForm(FlaskForm):
    """Form for user registration."""

    username = StringField(
        "Username", validators=[DataRequired(), Length(min=3, max=64, message="Username must be between 3 and 64 characters")]
    )
    email = EmailField(
        "Email",
        validators=[
            DataRequired(),
            Email(message="Please enter a valid email address"),
            Length(max=120, message="Email must be less than 120 characters"),
        ],
    )
    password = PasswordField(
        "Password", validators=[DataRequired(), Length(min=8, message="Password must be at least 8 characters")]
    )
    password2 = PasswordField(
        "Confirm Password", validators=[DataRequired(), EqualTo("password", message="Passwords must match")]
    )
    submit = SubmitField("Register")

    def validate_username(self, username):
        """Validate that the username is not already taken."""
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError("Username already taken. Please choose a different one.")

    def validate_email(self, email):
        """Validate that the email is not already registered."""
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError("Email already registered. Please use a different email or login.")


class RequestPasswordResetForm(FlaskForm):
    """Form for requesting a password reset."""

    email = EmailField("Email", validators=[DataRequired(), Email(message="Please enter a valid email address")])
    submit = SubmitField("Request Password Reset")

    def validate_email(self, email):
        """Validate that the email exists in the database."""
        user = User.query.filter_by(email=email.data).first()
        if user is None:
            raise ValidationError("No account found with that email address.")


class ResetPasswordForm(FlaskForm):
    """Form for resetting a password."""

    password = PasswordField(
        "New Password", validators=[DataRequired(), Length(min=8, message="Password must be at least 8 characters")]
    )
    password2 = PasswordField(
        "Confirm New Password", validators=[DataRequired(), EqualTo("password", message="Passwords must match")]
    )
    submit = SubmitField("Reset Password")
