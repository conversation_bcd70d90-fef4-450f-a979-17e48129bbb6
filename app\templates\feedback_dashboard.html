{% extends "base.html" %}

{% block title %}Feedback Dashboard - ImgCipherF1{% endblock %}

{% block content %}
<div class="feedback-dashboard-container page-transition">
  <h1 class="text-2xl font-bold mb-6">Feedback Dashboard</h1>
  
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <!-- Feedback Stats Cards -->
    <div class="stat-card bg-blue-800 p-4 rounded-lg shadow-md">
      <h3 class="text-lg font-medium mb-2">Total Feedback</h3>
      <p class="text-3xl font-bold">{{ feedback_stats.total_count }}</p>
    </div>
    
    <div class="stat-card bg-green-800 p-4 rounded-lg shadow-md">
      <h3 class="text-lg font-medium mb-2">Average Rating</h3>
      <p class="text-3xl font-bold">{{ "%.1f"|format(feedback_stats.avg_rating) }}/5</p>
    </div>
    
    <div class="stat-card bg-purple-800 p-4 rounded-lg shadow-md">
      <h3 class="text-lg font-medium mb-2">Completed Tests</h3>
      <p class="text-3xl font-bold">{{ test_stats.completed_tests }}</p>
    </div>
    
    <div class="stat-card bg-yellow-800 p-4 rounded-lg shadow-md">
      <h3 class="text-lg font-medium mb-2">Avg. Satisfaction</h3>
      <p class="text-3xl font-bold">{{ "%.1f"|format(test_stats.avg_satisfaction) }}/5</p>
    </div>
  </div>
  
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Feedback by Type Chart -->
    <div class="chart-card bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 class="text-xl font-semibold mb-4">Feedback by Type</h2>
      <div class="feedback-type-chart h-64" id="feedback-type-chart"></div>
    </div>
    
    <!-- Feedback by Page Chart -->
    <div class="chart-card bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 class="text-xl font-semibold mb-4">Feedback by Page</h2>
      <div class="feedback-page-chart h-64" id="feedback-page-chart"></div>
    </div>
  </div>
  
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Test Completion Stats -->
    <div class="chart-card bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 class="text-xl font-semibold mb-4">Test Completion Stats</h2>
      <div class="test-completion-chart h-64" id="test-completion-chart"></div>
      <div class="mt-4 grid grid-cols-2 gap-4">
        <div>
          <h3 class="text-md font-medium mb-1">Avg. Completion Time</h3>
          <p class="text-xl font-bold">{{ (test_stats.avg_completion_time / 60)|round(1) }} min</p>
        </div>
        <div>
          <h3 class="text-md font-medium mb-1">Abandonment Rate</h3>
          <p class="text-xl font-bold">
            {% if test_stats.total_tests > 0 %}
              {{ ((test_stats.abandoned_tests / test_stats.total_tests) * 100)|round(1) }}%
            {% else %}
              0%
            {% endif %}
          </p>
        </div>
      </div>
    </div>
    
    <!-- Task Success Rates -->
    <div class="chart-card bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 class="text-xl font-semibold mb-4">Task Success Rates</h2>
      <div class="task-success-chart h-64" id="task-success-chart"></div>
    </div>
  </div>
  
  <div class="bg-gray-800 p-6 rounded-lg shadow-md mb-8">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">Recent Feedback</h2>
      <div class="flex space-x-2">
        <button id="export-csv-btn" class="px-3 py-1 bg-green-700 hover:bg-green-600 rounded text-sm transition-colors">
          <i class="fas fa-file-csv mr-1"></i>Export CSV
        </button>
        <button id="refresh-data-btn" class="px-3 py-1 bg-blue-700 hover:bg-blue-600 rounded text-sm transition-colors">
          <i class="fas fa-sync-alt mr-1"></i>Refresh
        </button>
      </div>
    </div>
    
    <div class="overflow-x-auto">
      <table class="min-w-full bg-gray-900 rounded-lg">
        <thead>
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">ID</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Type</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Page</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Rating</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Comment</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Date</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Device</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-800">
          {% for item in feedback_items %}
          <tr class="hover:bg-gray-800 transition-colors">
            <td class="px-4 py-3 whitespace-nowrap text-sm">{{ item.id }}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm">
              <span class="px-2 py-1 rounded text-xs 
                {% if item.feedback_type == 'usability_test' %}bg-purple-800
                {% elif item.feedback_type == 'bug_report' %}bg-red-800
                {% elif item.feedback_type == 'feature_request' %}bg-green-800
                {% elif item.feedback_type == 'general' %}bg-blue-800
                {% else %}bg-gray-700{% endif %}">
                {{ item.feedback_type }}
              </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm">{{ item.page_url|truncate(30) }}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm">
              {% if item.rating %}
                <div class="flex text-yellow-500">
                  {% for i in range(item.rating) %}
                    <span>★</span>
                  {% endfor %}
                  {% for i in range(5 - item.rating) %}
                    <span class="text-gray-600">★</span>
                  {% endfor %}
                </div>
              {% else %}
                <span class="text-gray-500">N/A</span>
              {% endif %}
            </td>
            <td class="px-4 py-3 text-sm">{{ item.comment|truncate(50) if item.comment else 'No comment' }}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm">{{ item.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm">{{ item.device_type or 'Unknown' }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
  
  <div class="bg-gray-800 p-6 rounded-lg shadow-md">
    <h2 class="text-xl font-semibold mb-4">Actions</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <a href="{{ url_for('feedback.usability_test_page') }}" class="btn-primary text-center" target="_blank">
        <i class="fas fa-flask mr-2"></i>View Usability Test
      </a>
      <button id="generate-report-btn" class="btn-secondary">
        <i class="fas fa-file-alt mr-2"></i>Generate Full Report
      </button>
      <button id="clear-test-data-btn" class="bg-red-700 hover:bg-red-600 text-white py-2 px-4 rounded transition-colors">
        <i class="fas fa-trash-alt mr-2"></i>Clear Test Data
      </button>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Feedback Type Chart
    const typeCtx = document.getElementById('feedback-type-chart').getContext('2d');
    const typeChart = new Chart(typeCtx, {
      type: 'doughnut',
      data: {
        labels: [{% for type, count in feedback_stats.type_counts.items() %}'{{ type }}',{% endfor %}],
        datasets: [{
          data: [{% for type, count in feedback_stats.type_counts.items() %}{{ count }},{% endfor %}],
          backgroundColor: [
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 99, 132, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(255, 206, 86, 0.8)',
            'rgba(153, 102, 255, 0.8)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              color: '#e5e7eb'
            }
          }
        }
      }
    });
    
    // Test Completion Chart
    const completionCtx = document.getElementById('test-completion-chart').getContext('2d');
    const completionChart = new Chart(completionCtx, {
      type: 'pie',
      data: {
        labels: ['Completed', 'Abandoned', 'In Progress'],
        datasets: [{
          data: [
            {{ test_stats.completed_tests }},
            {{ test_stats.abandoned_tests }},
            {{ test_stats.total_tests - test_stats.completed_tests - test_stats.abandoned_tests }}
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.8)',
            'rgba(255, 99, 132, 0.8)',
            'rgba(255, 206, 86, 0.8)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              color: '#e5e7eb'
            }
          }
        }
      }
    });
    
    // Add event listeners for buttons
    document.getElementById('export-csv-btn').addEventListener('click', function() {
      alert('Exporting data to CSV...');
      // Implementation would go here
    });
    
    document.getElementById('refresh-data-btn').addEventListener('click', function() {
      location.reload();
    });
    
    document.getElementById('generate-report-btn').addEventListener('click', function() {
      alert('Generating full report...');
      // Implementation would go here
    });
    
    document.getElementById('clear-test-data-btn').addEventListener('click', function() {
      if (confirm('Are you sure you want to clear all test data? This action cannot be undone.')) {
        alert('Test data cleared.');
        // Implementation would go here
      }
    });
  });
</script>
{% endblock %}
