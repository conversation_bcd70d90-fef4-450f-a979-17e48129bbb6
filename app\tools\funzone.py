import requests
from app.extensions import db
from threading import Lock
from datetime import datetime
from flask import current_app as app
from app.cache import cached_vibes, cached_confession, cached_tech_history, invalidate_vibes_cache, invalidate_confession_cache


class Confession(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    text = db.Column(db.String(500), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)


class Vibe(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    count = db.Column(db.Integer, default=0)


poll_votes = {"tabs": 0, "spaces": 0}
poll_lock = Lock()
vibes_lock = Lock()


def get_confession_count():
    try:
        return Confession.query.count()
    except Exception as e:
        app.logger.error(f"Error counting confessions: {str(e)}")
        return 0


@cached_confession
def get_random_confession():
    try:
        confession = Confession.query.order_by(db.func.random()).first()
        return confession.text if confession else "No confessions yet"
    except Exception:
        return "Error retrieving confession"


def add_confession(text):
    if not isinstance(text, str) or not text.strip() or len(text) > 500:
        return False
    try:
        confession = Confession(text=text)
        db.session.add(confession)
        db.session.commit()
        # Invalidate the confession cache when a new confession is added
        invalidate_confession_cache()
        return True
    except Exception:
        db.session.rollback()
        return False


@cached_vibes
def get_vibes():
    """Get current vibe counts from database."""
    try:
        vibes = {vibe.name: vibe.count for vibe in Vibe.query.all()}
        return vibes or {"productive": 0, "tired": 0, "debugging": 0, "procrastinating": 0}
    except Exception:
        return {"productive": 0, "tired": 0, "debugging": 0, "procrastinating": 0}


def update_vibe(vibe):
    """Update vibe count in database."""
    valid_vibes = {"productive", "tired", "debugging", "procrastinating"}
    if vibe not in valid_vibes:
        return False
    try:
        vibe_obj = Vibe.query.filter_by(name=vibe).first()
        if not vibe_obj:
            vibe_obj = Vibe(name=vibe, count=0)
            db.session.add(vibe_obj)
        vibe_obj.count += 1
        db.session.commit()
        # Invalidate the vibes cache when a vibe is updated
        invalidate_vibes_cache()
        return True
    except Exception:
        db.session.rollback()
        return False


def vote_poll(option):
    """Record a poll vote and return percentages."""
    valid_options = poll_votes.keys()
    if option not in valid_options:
        return None
    with poll_lock:
        try:
            poll_votes[option] += 1
            total = sum(poll_votes.values())
            return {k: v / total * 100 for k, v in poll_votes.items()}
        except Exception:
            return None


@cached_tech_history
def get_tech_history():
    """Get random tech history fact from Wikipedia with Redis caching."""
    try:
        url = "https://en.wikipedia.org/w/api.php"
        params = {"action": "query", "format": "json", "list": "random", "rnnamespace": 0, "rnlimit": 1}
        app.logger.info("Fetching random page from Wikipedia")
        random_response = requests.get(url, params=params, timeout=5)
        app.logger.info(f"Random page response status: {random_response.status_code}")
        random_data = random_response.json()
        app.logger.debug(f"Random page data: {random_data}")

        page = random_data["query"]["random"][0]
        title = page["title"]
        link = f"https://en.wikipedia.org/wiki/{title.replace(' ', '_')}"

        summary_params = {
            "action": "query",
            "format": "json",
            "prop": "extracts",
            "exintro": True,
            "explaintext": True,
            "titles": title,
        }
        app.logger.info(f"Fetching summary for title: {title}")
        summary_response = requests.get(url, params=summary_params, timeout=5)
        app.logger.info(f"Summary response status: {summary_response.status_code}")
        summary_data = summary_response.json()
        app.logger.debug(f"Summary data: {summary_data}")

        pages = summary_data["query"]["pages"]
        page_id = next(iter(pages))  # Get first key safely
        if page_id == "-1" or "extract" not in pages[page_id]:
            app.logger.warning(f"No extract found for {title}")
            return {"text": "No summary available for this page.", "link": link}
        summary = pages[page_id]["extract"].split(".")[0] + "."

        return {"text": summary, "link": link}
    except Exception as e:
        app.logger.error(f"Failed to fetch tech history: {str(e)}")
        return {"text": "Failed to fetch history", "link": ""}
