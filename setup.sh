#!/bin/bash
# ImgCipherF1 Unix/Linux/macOS Setup Script
# This shell script provides a simple way to run the Python setup script

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}============================================================${NC}"
echo -e "${BLUE}                ImgCipherF1 Unix Setup${NC}"
echo -e "${BLUE}============================================================${NC}"
echo

# Function to print colored output
print_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

print_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

print_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed or not in PATH"
    echo "Please install Python 3.9+ using your package manager:"
    echo "  Ubuntu/Debian: sudo apt-get install python3 python3-pip python3-venv"
    echo "  CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "  macOS: brew install python3"
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
REQUIRED_VERSION="3.9"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    print_error "Python $REQUIRED_VERSION or higher is required, but $PYTHON_VERSION is installed"
    exit 1
fi

print_success "Python $PYTHON_VERSION found"

# Default to development setup
SETUP_ARGS="--dev"

# Parse command line arguments
case "$1" in
    --prod)
        SETUP_ARGS="--prod"
        ;;
    --docker)
        SETUP_ARGS="--docker"
        ;;
    --help)
        echo "Usage: $0 [--dev|--prod|--docker|--help]"
        echo
        echo "Options:"
        echo "  --dev      Setup for development (default)"
        echo "  --prod     Setup for production"
        echo "  --docker   Setup for Docker environment"
        echo "  --help     Show this help message"
        echo
        echo "Examples:"
        echo "  $0           # Development setup"
        echo "  $0 --prod    # Production setup"
        echo "  $0 --docker  # Docker setup"
        exit 0
        ;;
    "")
        # Default case, keep --dev
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac

print_info "Running Python setup script with arguments: $SETUP_ARGS"
echo

# Make setup.py executable if it isn't already
chmod +x setup.py

# Run the Python setup script
if python3 setup.py $SETUP_ARGS; then
    echo
    echo -e "${GREEN}============================================================${NC}"
    echo -e "${GREEN}                    Setup Complete!${NC}"
    echo -e "${GREEN}============================================================${NC}"
    echo
    echo "To activate the virtual environment, run:"
    echo -e "  ${BLUE}source venv/bin/activate${NC}"
    echo
    echo "Then start the application with:"
    echo -e "  ${BLUE}python run.py${NC}"
    echo
else
    echo
    print_error "Setup failed! Check the error messages above."
    exit 1
fi
