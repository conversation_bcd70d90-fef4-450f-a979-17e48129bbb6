import os
import time
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
from werkzeug.utils import secure_filename
from flask import current_app
from app.utils.constants import MAX_FILE_SIZE

# Convert MAX_FILE_SIZE from bytes to MB for error messages
MAX_PDF_SIZE_MB = MAX_FILE_SIZE // (1024 * 1024)


def validate_files(files):
    """Validate PDF files before processing."""
    if not files:
        raise ValueError("No files provided")

    for file in files:
        if not file.filename.lower().endswith(".pdf"):
            raise ValueError(f"Invalid file type: {file.filename}. Only PDFs are allowed.")

        # Check file size (in bytes)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        if file_size > MAX_PDF_SIZE_MB * 1024 * 1024:
            raise ValueError(f"File {file.filename} exceeds {MAX_PDF_SIZE_MB}MB limit")


def count_total_pages(files):
    """Count total pages across all PDFs."""
    total_pages = 0
    for file in files:
        try:
            reader = PdfReader(file)
            total_pages += len(reader.pages)
            file.seek(0)  # Reset file pointer after reading
        except Exception as e:
            raise ValueError(f"Error reading {file.filename}: {e}")
    if total_pages > 100:  # Example limit
        raise ValueError("Total pages exceed maximum limit of 100")


def merge_pdfs(files, output_dir):
    """Merge multiple PDF files into one using only output directory"""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Generate unique timestamp for this operation
    timestamp = int(time.time())
    temp_files = []

    try:
        # Process files directly without saving uploads separately
        writer = PdfWriter()

        for file in files:
            try:
                # Read directly from file stream
                file.seek(0)
                reader = PdfReader(file)
                for page in reader.pages:
                    writer.add_page(page)
            except Exception as e:
                raise ValueError(f"Error processing {file.filename}: {e}")
            finally:
                file.seek(0)  # Reset file pointer

        # Save final output
        output_filename = os.path.join(output_dir, f"merged_{timestamp}.pdf")
        with open(output_filename, "wb") as output_pdf:
            writer.write(output_pdf)

        return output_filename

    except Exception:
        # Clean up if error occurs
        for temp_path in temp_files:
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception:
                pass
        raise


def validate_pages(pages_str, max_pages):
    """Validate and parse page ranges string into list of page numbers"""
    if not pages_str:
        raise ValueError("Pages input cannot be empty.")

    pages = []
    for part in pages_str.split(","):
        part = part.strip()
        if not part:
            continue

        if "-" in part:
            try:
                start, end = map(int, part.split("-"))
                if start < 1 or end > max_pages:
                    raise ValueError(f"Page range {start}-{end} is out of bounds (1-{max_pages})")
                if start > end:
                    raise ValueError(f"Invalid range {start}-{end} (start > end)")
                pages.extend(range(start, end + 1))
            except ValueError:
                raise ValueError(f"Invalid range format: '{part}'. Use 'start-end' (e.g., '1-3')")
        else:
            try:
                page = int(part)
                if page < 1 or page > max_pages:
                    raise ValueError(f"Page {page} is out of bounds (1-{max_pages})")
                pages.append(page)
            except ValueError:
                raise ValueError(f"Invalid page number: '{part}'")

    if not pages:
        raise ValueError("No valid pages specified")

    return sorted(set(pages))  # Remove duplicates and sort


def split_pdf(file, pages_str, output_filename=None, optimize=False):
    """Split PDF and save specified pages to output file

    Args:
        file: The PDF file to split
        pages_str: String specifying which pages to include (e.g., "1-3,5,7-9")
        output_filename: Optional name for the output file
        optimize: Whether to optimize the output PDF (not yet implemented)
    """
    if not file.filename.lower().endswith(".pdf"):
        raise ValueError(f"Invalid file type: {file.filename}. Only PDFs are allowed.")

    # Read input PDF
    try:
        reader = PdfReader(file)
    except Exception as e:
        raise ValueError(f"Error reading PDF: {str(e)}")

    max_pages = len(reader.pages)
    if max_pages == 0:
        raise ValueError("The PDF file contains no pages")

    # Parse and validate page ranges
    pages = validate_pages(pages_str, max_pages)

    # Create output PDF
    writer = PdfWriter()
    for page_num in pages:
        writer.add_page(reader.pages[page_num - 1])  # Pages are 0-indexed

    # Generate output filename if not provided
    if not output_filename:
        name, ext = os.path.splitext(secure_filename(file.filename))
        output_filename = f"{name}_split{ext}"

    # Save output file
    output_path = os.path.join(current_app.config["UPLOAD_FOLDER"], secure_filename(output_filename))
    with open(output_path, "wb") as output_file:
        writer.write(output_file)

    # TODO: Implement optimization if requested
    # if optimize:
    #     optimize_pdf(output_path)

    return output_path
