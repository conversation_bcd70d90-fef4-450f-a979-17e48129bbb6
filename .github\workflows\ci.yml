name: CI

on:
  push:
    branches: [ main, perfect-ten ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    env:
      FLASK_ENV: testing
      DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      SECRET_KEY: test_secret_key
      CELERY_BROKER_URL: redis://localhost:6379/0
      CELERY_RESULT_BACKEND: redis://localhost:6379/0
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Set up Redis
      uses: supercharge/redis-github-action@1.5.0
      with:
        redis-version: 6
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flake8 black pytest pytest-cov mypy
    
    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Check formatting with black
      run: |
        black --check .
    
    - name: Type check with mypy
      run: |
        mypy app
    
    - name: Initialize database
      run: |
        flask db init
        flask db migrate -m "Initial migration"
        flask db upgrade
    
    - name: Test with pytest
      run: |
        pytest --maxfail=1 --disable-warnings -q
