/**
 * Mobile Menu Functionality
 * Enhanced with accessibility features
 */
document.addEventListener('DOMContentLoaded', () => {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const closeMenuButton = document.getElementById('close-mobile-menu');
  const mobileMenu = document.getElementById('mobile-menu');

  if (!mobileMenuButton || !closeMenuButton || !mobileMenu) return;

  // Function to open mobile menu
  function openMobileMenu() {
    mobileMenu.classList.remove('translate-x-full');
    mobileMenuButton.setAttribute('aria-expanded', 'true');

    // Set focus to the close button
    setTimeout(() => {
      closeMenuButton.focus();
    }, 100);

    // Trap focus inside the menu
    trapFocus(mobileMenu);

    // Close menu when clicking outside
    document.addEventListener('click', closeMenuOnClickOutside);
  }

  // Function to close mobile menu
  function closeMobileMenu() {
    mobileMenu.classList.add('translate-x-full');
    mobileMenuButton.setAttribute('aria-expanded', 'false');

    // Return focus to the menu button
    setTimeout(() => {
      mobileMenuButton.focus();
    }, 100);

    // Remove outside click listener
    document.removeEventListener('click', closeMenuOnClickOutside);
  }

  // Close menu when clicking outside
  function closeMenuOnClickOutside(event) {
    if (!mobileMenu.contains(event.target) && event.target !== mobileMenuButton) {
      closeMobileMenu();
    }
  }

  // Trap focus inside the mobile menu
  function trapFocus(element) {
    const focusableElements = element.querySelectorAll(
      'a[href], button, textarea, input, select, [tabindex]:not([tabindex="-1"])'
    );

    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    element.addEventListener('keydown', function(e) {
      if (e.key === 'Tab') {
        // Shift + Tab on first element should loop to last element
        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
        // Tab on last element should loop to first element
        else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }

      // Close on Escape key
      if (e.key === 'Escape') {
        closeMobileMenu();
      }
    });
  }

  // Event listeners
  mobileMenuButton.addEventListener('click', openMobileMenu);
  closeMenuButton.addEventListener('click', closeMobileMenu);

  // Keyboard support for menu button
  mobileMenuButton.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      openMobileMenu();
    }
  });

  // Keyboard support for close button
  closeMenuButton.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      closeMobileMenu();
    }
  });
});
