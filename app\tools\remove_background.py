import os
import uuid
from PIL import Image
from flask import current_app as app
from werkzeug.utils import secure_filename


def remove_background(input_path: str, orig_filename: str, output_format: str, output_folder: str) -> str:
    """
    Remove the background from an image.

    Args:
        input_path (str): Path to the input image file.
        orig_filename (str): Original filename (used for naming the output file).
        output_format (str): Desired output format ('png' or 'webp' recommended for transparency).
        output_folder (str): Folder where the processed file will be saved.

    Returns:
        str: The output filename if processing is successful.

    Raises:
        Exception: If any error occurs during processing.
    """
    name_without_ext = os.path.splitext(orig_filename)[0]
    output_folder = app.config["OUTPUT_FOLDER"]
    output_filename = f"{name_without_ext}_nobg.{output_format}"
    output_path = os.path.join(output_folder, output_filename)

    try:
        # Import rembg only when needed to avoid startup issues
        from rembg import remove

        # Open the input image
        with Image.open(input_path) as img:
            # Remove background
            output = remove(img)

            # Save the result
            if output_format == "png":
                output.save(output_path, "PNG")
            elif output_format == "webp":
                output.save(output_path, "WEBP", lossless=True)
            elif output_format in ["jpg", "jpeg"]:
                # Convert to RGB (losing transparency) for JPEG
                rgb_img = Image.new("RGB", output.size, (255, 255, 255))
                rgb_img.paste(output, mask=output.split()[3] if output.mode == "RGBA" else None)
                rgb_img.save(output_path, "JPEG", quality=95)
            else:
                # Default to PNG for other formats
                output.save(output_path, "PNG")
    except Exception as e:
        raise Exception(f"Error removing background from {orig_filename}: {e}") from e

    return output_filename
