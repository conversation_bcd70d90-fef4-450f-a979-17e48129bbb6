"""
Tests for batch file conversion functionality.
"""

# flake8: noqa
import io
import json
import pytest
from unittest.mock import patch, MagicMock
from flask import url_for
from werkzeug.datastructures import FileStorage


def test_batch_convert_page_get_unauthenticated(client):
    """Test that unauthenticated users are redirected from batch conversion page."""
    response = client.get(url_for("tools.batch_convert_page"))
    assert response.status_code == 302  # Redirect to login
    assert "login" in response.location


@pytest.mark.skip(reason="Authentication issues in tests")
def test_batch_convert_page_get_authenticated(client, auth, app):
    """Test that authenticated users can access the batch conversion page."""
    auth.login()

    # Ensure the user has the required role
    with app.app_context():
        from app.models.auth import User, Role
        from app.extensions import db

        user = User.query.filter_by(username="testuser").first()
        registered_role = Role.query.filter_by(name="registered").first()

        if registered_role not in user.roles:
            user.roles.append(registered_role)
            db.session.commit()

    response = client.get(url_for("tools.batch_convert_page"))
    assert response.status_code == 200
    assert b"Batch File Converter" in response.data
    assert b"Convert multiple files at once" in response.data


def test_batch_convert_post_unauthenticated(client):
    """Test that unauthenticated users cannot post to batch conversion endpoint."""
    data = {"output_format": "json"}
    response = client.post(url_for("tools.batch_convert_page"), data=data, content_type="multipart/form-data")
    assert response.status_code == 302  # Redirect to login
    assert "login" in response.location


@pytest.mark.skip(reason="Authentication issues in tests")
@patch("app.tasks.batch_convert_files.apply_async")
def test_batch_convert_post_authenticated(mock_task, client, auth, app):
    """Test batch conversion with authenticated user."""
    auth.login()

    # Ensure the user has the required role
    with app.app_context():
        from app.models.auth import User, Role
        from app.extensions import db

        user = User.query.filter_by(username="testuser").first()
        registered_role = Role.query.filter_by(name="registered").first()

        if registered_role not in user.roles:
            user.roles.append(registered_role)
            db.session.commit()

    # Create test files
    file1 = FileStorage(stream=io.BytesIO(b"test,data\n1,2"), filename="test1.csv", content_type="text/csv")
    file2 = FileStorage(stream=io.BytesIO(b"test,data\n3,4"), filename="test2.csv", content_type="text/csv")

    # Mock the task
    mock_task.return_value = MagicMock(id="test-task-id")

    # Submit the form
    data = {"files": [file1, file2], "output_format": "json"}

    response = client.post(
        url_for("tools.batch_convert_page"), data=data, content_type="multipart/form-data", follow_redirects=False
    )

    # Check redirect to progress page
    assert response.status_code == 302
    assert "batch_progress" in response.location
    assert "test-task-id" in response.location

    # Verify task was called
    mock_task.assert_called_once()
    args, kwargs = mock_task.call_args
    assert kwargs["queue"] == "batch"
    assert len(args[0][0]) == 2  # Two file paths
    assert args[0][1] == "json"  # Output format


def test_batch_progress_page_unauthenticated(client):
    """Test that unauthenticated users are redirected from progress page."""
    response = client.get(url_for("tools.batch_progress", task_id="test-task-id"))
    assert response.status_code == 302  # Redirect to login
    assert "login" in response.location


@pytest.mark.skip(reason="Authentication issues in tests")
def test_batch_progress_page_authenticated(client, auth, app):
    """Test that authenticated users can access the progress page."""
    auth.login()

    # Ensure the user has the required role
    with app.app_context():
        from app.models.auth import User, Role
        from app.extensions import db

        user = User.query.filter_by(username="testuser").first()
        registered_role = Role.query.filter_by(name="registered").first()

        if registered_role not in user.roles:
            user.roles.append(registered_role)
            db.session.commit()

    response = client.get(url_for("tools.batch_progress", task_id="test-task-id"))
    assert response.status_code == 200
    assert b"Batch Conversion Progress" in response.data
    assert b"test-task-id" in response.data


def test_task_progress_api_unauthenticated(client):
    """Test that unauthenticated users cannot access the task progress API."""
    response = client.get(url_for("tools.task_progress", task_id="test-task-id"))
    assert response.status_code == 302  # Redirect to login
    assert "login" in response.location


@pytest.mark.skip(reason="Authentication issues in tests")
@patch("app.tools.progress.ProgressTracker.get_progress")
def test_task_progress_api_authenticated(mock_get_progress, client, auth, app):
    """Test that authenticated users can access the task progress API."""
    auth.login()

    # Ensure the user has the required role
    with app.app_context():
        from app.models.auth import User, Role
        from app.extensions import db

        user = User.query.filter_by(username="testuser").first()
        registered_role = Role.query.filter_by(name="registered").first()

        if registered_role not in user.roles:
            user.roles.append(registered_role)
            db.session.commit()

    # Mock the progress data
    mock_get_progress.return_value = {
        "progress": 50,
        "message": "Processing file 1 of 2",
        "timestamp": 1234567890,
        "estimated_time": "10s remaining",
    }

    response = client.get(url_for("tools.task_progress", task_id="test-task-id"))
    assert response.status_code == 200

    data = json.loads(response.data)
    assert data["progress"] == 50
    assert data["message"] == "Processing file 1 of 2"
    assert data["estimated_time"] == "10s remaining"


def test_task_result_api_unauthenticated(client):
    """Test that unauthenticated users cannot access the task result API."""
    response = client.get(url_for("tools.task_result", task_id="test-task-id"))
    assert response.status_code == 302  # Redirect to login
    assert "login" in response.location


@pytest.mark.skip(reason="Authentication issues in tests")
@patch("celery.result.AsyncResult")
def test_task_result_api_authenticated(mock_async_result, client, auth, app):
    """Test that authenticated users can access the task result API."""
    auth.login()

    # Ensure the user has the required role
    with app.app_context():
        from app.models.auth import User, Role
        from app.extensions import db

        user = User.query.filter_by(username="testuser").first()
        registered_role = Role.query.filter_by(name="registered").first()

        if registered_role not in user.roles:
            user.roles.append(registered_role)
            db.session.commit()

    # Mock the task result
    mock_result = MagicMock()
    mock_result.state = "SUCCESS"
    mock_result.successful.return_value = True
    mock_result.get.return_value = {
        "task_id": "test-task-id",
        "total": 2,
        "successful": 2,
        "failed": 0,
        "results": [
            {"filename": "test1.csv", "output_filename": "output1.json", "success": True},
            {"filename": "test2.csv", "output_filename": "output2.json", "success": True},
        ],
        "failed_files": [],
    }
    mock_async_result.return_value = mock_result

    response = client.get(url_for("tools.task_result", task_id="test-task-id"))
    assert response.status_code == 200

    data = json.loads(response.data)
    assert data["task_id"] == "test-task-id"
    assert data["total"] == 2
    assert data["successful"] == 2
    assert len(data["results"]) == 2


def test_download_batch_unauthenticated(client):
    """Test that unauthenticated users cannot download batch results."""
    response = client.get(url_for("tools.download_batch", task_id="test-task-id"))
    assert response.status_code == 302  # Redirect to login
    assert "login" in response.location


@pytest.mark.skip(reason="Authentication issues in tests")
@patch("celery.result.AsyncResult")
@patch("zipfile.ZipFile")
@patch("os.path.exists")
def test_download_batch_authenticated(mock_exists, mock_zipfile, mock_async_result, client, auth, app):
    """Test that authenticated users can download batch results."""
    auth.login()

    # Ensure the user has the required role
    with app.app_context():
        from app.models.auth import User, Role
        from app.extensions import db

        user = User.query.filter_by(username="testuser").first()
        registered_role = Role.query.filter_by(name="registered").first()

        if registered_role not in user.roles:
            user.roles.append(registered_role)
            db.session.commit()

    # Mock the task result
    mock_result = MagicMock()
    mock_result.successful.return_value = True
    mock_result.get.return_value = {
        "results": [
            {"filename": "test1.csv", "output_filename": "output1.json", "success": True},
            {"filename": "test2.csv", "output_filename": "output2.json", "success": True},
        ]
    }
    mock_async_result.return_value = mock_result

    # Mock file existence
    mock_exists.return_value = True

    # Mock zipfile operations
    mock_zipfile_instance = MagicMock()
    mock_zipfile.return_value.__enter__.return_value = mock_zipfile_instance

    response = client.get(url_for("tools.download_batch", task_id="test-task-id"))
    assert response.status_code == 200
    assert response.mimetype == "application/zip"
    assert "attachment" in response.headers["Content-Disposition"]
    assert "batch_conversion_test-task-id.zip" in response.headers["Content-Disposition"]

    # Verify zipfile was used
    mock_zipfile_instance.write.assert_called()
