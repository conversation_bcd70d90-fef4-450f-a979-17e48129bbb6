{% extends "base.html" %}

{% block title %}Reset Password{% endblock %}

{% block content %}
<div class="max-w-md mx-auto mt-24 p-6 rounded-lg shadow-lg">
  <h1 class="text-2xl font-bold mb-6 text-center">Reset Your Password</h1>
  
  <form method="POST">
    {{ form.hidden_tag() }}
    
    <div class="mb-4">
      {{ form.password.label(class="block text-sm font-medium mb-1") }}
      {{ form.password(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.password.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-6">
      {{ form.password2.label(class="block text-sm font-medium mb-1") }}
      {{ form.password2(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.password2.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-4">
      {{ form.submit(class="w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2") }}
    </div>
  </form>
</div>
{% endblock %}
