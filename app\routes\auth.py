"""
Authentication routes for the application.
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from urllib.parse import urlparse
from app.forms.auth import LoginForm, RegistrationForm, RequestPasswordResetForm, ResetPasswordForm
from app.models.auth import User, UserStatus, Role
from app.extensions import db

# Blueprint initialization
auth_bp = Blueprint("auth", __name__)


@auth_bp.route("/login", methods=["GET", "POST"])
def login():
    """Handle user login."""
    if current_user.is_authenticated:
        return redirect(url_for("main.index"))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()

        if user is None or not user.verify_password(form.password.data):
            flash("Invalid username or password", "error")
            return redirect(url_for("auth.login"))

        if user.status != UserStatus.ACTIVE:
            flash("Your account is not active. Please contact support.", "error")
            return redirect(url_for("auth.login"))

        # Update login statistics
        user.login_count += 1
        user.last_login = db.func.now()
        db.session.commit()

        login_user(user, remember=form.remember_me.data)

        # Redirect to the page the user was trying to access
        next_page = request.args.get("next")
        if not next_page or urlparse(next_page).netloc != "":
            next_page = url_for("main.index")

        return redirect(next_page)

    return render_template("auth/login.html", title="Sign In", form=form)


@auth_bp.route("/logout")
def logout():
    """Handle user logout."""
    logout_user()
    flash("You have been logged out.", "info")
    return redirect(url_for("main.index"))


@auth_bp.route("/register", methods=["GET", "POST"])
def register():
    """Handle user registration."""
    if current_user.is_authenticated:
        return redirect(url_for("main.index"))

    form = RegistrationForm()

    # For debugging
    print(f"Form data: {request.form}")
    print(f"Form errors: {form.errors}")

    if form.validate_on_submit():
        try:
            user = User(
                username=form.username.data,
                email=form.email.data,
                status=UserStatus.ACTIVE,  # Auto-activate for now, could be PENDING for email verification
            )
            user.password = form.password.data
            user.generate_api_key()

            # Add default role
            registered_role = Role.query.filter_by(name="registered").first()
            if registered_role:
                user.roles.append(registered_role)
            else:
                print("ERROR: 'registered' role not found")
                flash("Error during registration: Role not found", "error")
                return render_template("auth/register.html", title="Register", form=form)

            db.session.add(user)
            db.session.commit()

            flash("Registration successful! You can now log in.", "success")
            return redirect(url_for("auth.login"))
        except Exception as e:
            db.session.rollback()
            print(f"ERROR during registration: {str(e)}")
            import traceback

            traceback.print_exc()
            flash(f"Error during registration: {str(e)}", "error")
            return render_template("auth/register.html", title="Register", form=form)

    return render_template("auth/register.html", title="Register", form=form)


@auth_bp.route("/profile")
@login_required
def profile():
    """Display user profile."""
    return render_template("auth/profile.html", title="Profile")


@auth_bp.route("/reset-password-request", methods=["GET", "POST"])
def reset_password_request():
    """Handle password reset request."""
    if current_user.is_authenticated:
        return redirect(url_for("main.index"))

    form = RequestPasswordResetForm()
    if form.validate_on_submit():
        # Find user by email
        User.query.filter_by(email=form.email.data).first()

        # In a real application, send an email with a reset token
        # For now, just show a success message
        flash("Check your email for instructions to reset your password.", "info")
        return redirect(url_for("auth.login"))

    return render_template("auth/reset_password_request.html", title="Reset Password", form=form)


@auth_bp.route("/reset-password/<token>", methods=["GET", "POST"])
def reset_password(token):
    """Handle password reset with token."""
    # Token will be used in a real application to validate the reset request
    if current_user.is_authenticated:
        return redirect(url_for("main.index"))

    # In a real application, validate the token and get the user
    # For now, just show the form

    form = ResetPasswordForm()
    if form.validate_on_submit():
        # In a real application, update the user's password
        flash("Your password has been reset.", "success")
        return redirect(url_for("auth.login"))

    return render_template("auth/reset_password.html", title="Reset Password", form=form)
