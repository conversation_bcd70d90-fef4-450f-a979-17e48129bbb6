from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash
import enum
import os

# Create a simple Flask app for testing
app = Flask(__name__)
app.config["SQLALCHEMY_DATABASE_URI"] = "sqlite:///:memory:"
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
app.config["SECRET_KEY"] = "test-key"

# Create the SQLAlchemy instance
db = SQLAlchemy(app)


# Define the models
class UserStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"


# Association table for many-to-many relationship between users and roles
user_roles = db.Table(
    "user_roles",
    db.Column("user_id", db.Integer, db.Foreign<PERSON>ey("user.id"), primary_key=True),
    db.Column("role_id", db.<PERSON><PERSON><PERSON>, db.<PERSON>("role.id"), primary_key=True),
)


class Role(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(255))
    users = db.relationship("User", secondary=user_roles, back_populates="roles")


class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    status = db.Column(db.Enum(UserStatus), default=UserStatus.ACTIVE)
    roles = db.relationship("Role", secondary=user_roles, back_populates="users")

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)


# Ensure instance directory exists
os.makedirs("instance", exist_ok=True)

# Create the database and tables
with app.app_context():
    db.create_all()

    # Create default roles
    roles = [
        {"name": "guest", "description": "Limited access for non-registered users"},
        {"name": "registered", "description": "Standard access for registered users"},
        {"name": "admin", "description": "Full access to all features"},
    ]

    for role_data in roles:
        if not Role.query.filter_by(name=role_data["name"]).first():
            db.session.add(Role(**role_data))

    # Create admin user for testing
    if not User.query.filter_by(username="admin").first():
        admin = User(username="admin", email="<EMAIL>", status=UserStatus.ACTIVE)
        admin.set_password("adminpassword")
        db.session.add(admin)

    db.session.commit()
    print("Database initialized successfully with default roles and admin user")
