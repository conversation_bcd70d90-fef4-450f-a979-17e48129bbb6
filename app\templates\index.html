{% extends "base.html" %}

{% block title %}Free File Conversion Tools - Split PDF, Merge PDF, Image Converter & More{% endblock %}

{% block head %}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- SEO Metadata -->
    <meta name="description" content="Free, ad-free tools to split PDF, merge PDF, convert images, and convert files. Fast and easy file processing with ImgCipher.">
    <meta name="keywords" content="split PDF, merge PDF, image converter, file converter, free PDF tools, free file conversion, no ads, free image converter, jpg to webp, jpg to png">
    <meta name="robots" content="index, follow">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="UTF-8">

    <!-- Open Graph for Social Sharing -->
    <meta property="og:title" content="Free File Conversion Tools - ImgCipher">
    <meta property="og:description" content="Convert files effortlessly with our free, ad-free tools: split PDF, merge PDF, image converter, and file converter">
    <meta property="og:url" content="{{ url_for('main.index', _external=True) }}">
    <meta property="og:type" content="website">
    <meta property="og:image" content="{{ url_for('static', filename='images/imgcipher-seo.webp', _external=True) }}"> <!-- Add an image in static/images -->

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "ImgCipher - Free File Conversion Tools",
        "description": "A free, ad-free online platform offering tools to split PDF, merge PDF, convert images, and convert files",
        "url": "{{ url_for('main.index', _external=True) }}",
        "applicationCategory": "Utilities",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
        },
        "featureList": [
            "Split PDF files",
            "Merge PDF documents",
            "Convert images to various formats",
            "Convert files between formats"
        ]
    }
    </script>

    <!-- Tailwind and Custom CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center px-8 py-8">
    <!-- Hero Section -->
    <div class="text-center max-w-2xl mb-8">
        <h1 class="text-4xl md:text-5xl font-bold mb-4 text-white">
            Free File Conversion Tools
        </h1>
        <p class="text-lg text-gray-400">
            Effortlessly split PDF files, merge PDFs, convert images, and convert files with our fast, and free online tools.
        </p>
    </div>

    <!-- Animated SVG -->
    <div class="mb-12 flex justify-center items-center">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" style="width: 100%; max-width: 200px; height: auto;">
            <circle cx="100" cy="100" r="50" fill="none" stroke="#3B82F6" stroke-width="10">
                <animate attributeName="r" from="50" to="10" dur="1s" repeatCount="indefinite" />
                <animate attributeName="stroke-width" from="10" to="1" dur="1s" repeatCount="indefinite" />
            </circle>
            <circle cx="100" cy="100" r="50" fill="none" stroke="#3B82F6" stroke-width="10">
                <animate attributeName="r" from="10" to="50" dur="1s" repeatCount="indefinite" />
                <animate attributeName="stroke-width" from="1" to="10" dur="1s" repeatCount="indefinite" />
            </circle>
        </svg>
    </div>

    <!-- Services Cards - Improved for mobile -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <!-- PDF Splitter Card -->
        <a href="{{ url_for('tools.split_pdf_page') }}" class="tool-card flex flex-col items-center bg-gray-800 rounded-lg shadow-lg p-4 sm:p-6 space-y-3 sm:space-y-4 hover:shadow-xl transition-shadow duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 sm:h-12 w-10 sm:w-12 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
            <div class="text-center">
                <h3 class="text-lg sm:text-xl font-semibold text-white">Split PDF</h3>
                <p class="text-gray-400 text-sm sm:text-base">Extract specific pages from your PDF files for free.</p>
            </div>
        </a>

        <!-- Merge PDF Card -->
        <a href="{{ url_for('tools.merge_pdf_page') }}" class="tool-card flex flex-col items-center bg-gray-800 rounded-lg shadow-lg p-4 sm:p-6 space-y-3 sm:space-y-4 hover:shadow-xl transition-shadow duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 sm:h-12 w-10 sm:w-12 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z"/>
            </svg>
            <div class="text-center">
                <h3 class="text-lg sm:text-xl font-semibold text-white">Merge PDF</h3>
                <p class="text-gray-400 text-sm sm:text-base">Combine multiple PDFs into one seamlessly.</p>
            </div>
        </a>

        <!-- File Converter Card -->
        <a href="{{ url_for('tools.convert_file_page') }}" class="tool-card flex flex-col items-center bg-gray-800 rounded-lg shadow-lg p-4 sm:p-6 space-y-3 sm:space-y-4 hover:shadow-xl transition-shadow duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 sm:h-12 w-10 sm:w-12 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3l14 18M19 3L5 21"/>
            </svg>
            <div class="text-center">
                <h3 class="text-lg sm:text-xl font-semibold text-white">File Converter</h3>
                <p class="text-gray-400 text-sm sm:text-base">Break Down Data Silos With Cross-Format Compatibility</p>
            </div>
        </a>

        <!-- Image Converter Card -->
        <a href="{{ url_for('tools.image_converter_page') }}" class="tool-card flex flex-col items-center bg-gray-800 rounded-lg shadow-lg p-4 sm:p-6 space-y-3 sm:space-y-4 hover:shadow-xl transition-shadow duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 sm:h-12 w-10 sm:w-12 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3m3 0h3m3 0h3M4 7h16M4 17h16"/>
            </svg>
            <div class="text-center">
                <h3 class="text-lg sm:text-xl font-semibold text-white">Image Converter</h3>
                <p class="text-gray-400 text-sm sm:text-base">Transform images to any format quickly.</p>
            </div>
        </a>
    </div>

    <!-- Enhanced SEO Content - Improved for mobile -->
    <div class="max-w-3xl mt-8 sm:mt-12 text-left bg-gray-800 p-4 sm:p-6 rounded-lg shadow-lg">
        <h2 class="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4">Why Choose ImgCipher?</h2>
        <p class="text-gray-400 mb-4 leading-relaxed text-sm sm:text-base">
            ImgCipher provides free tools to streamline file processing. Split PDFs, merge documents, convert images, or switch file types (for data scientists).
            <span class="text-gray-400 font-semibold">🔒 Privacy first:</span> Files vanish after download or 30s after a download link is created; no storage, no traces. No sign-ups, just fast, reliable tools.
        </p>
        <ul class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-gray-400 text-sm sm:text-base">
            <li class="flex items-start">
                <span class="text-blue-500 mr-2">•</span>
                <a href="{{ url_for('tools.split_pdf_page') }}" class="text-blue-500 hover:underline">Split PDF</a>: Extract pages instantly.
            </li>
            <li class="flex items-start">
                <span class="text-blue-500 mr-2">•</span>
                <a href="{{ url_for('tools.merge_pdf_page') }}" class="text-blue-500 hover:underline">Merge PDF</a>: Combine PDFs into one.
            </li>
            <li class="flex items-start">
                <span class="text-blue-500 mr-2">•</span>
                <a href="{{ url_for('tools.image_converter_page') }}" class="text-blue-500 hover:underline">Image Converter</a>: JPG, PNG, and more.
            </li>
            <li class="flex items-start">
                <span class="text-blue-500 mr-2">•</span>
                <a href="{{ url_for('tools.convert_file_page') }}" class="text-blue-500 hover:underline">File Converter</a>: Switch document formats.
            </li>
        </ul>
    </div>
</div>
{% endblock %}