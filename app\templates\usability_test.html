{% extends "base.html" %}

{% block title %}Usability Testing - ImgCipherF1{% endblock %}

{% block content %}
<div class="usability-test-container page-transition">
  <h1 class="text-2xl font-bold mb-6">Usability Testing</h1>
  
  <div class="test-intro bg-gray-800 p-6 rounded-lg shadow-md mb-8">
    <h2 class="text-xl font-semibold mb-4">Help Us Improve ImgCipherF1</h2>
    <p class="mb-4">Thank you for participating in our usability testing! Your feedback will help us make ImgCipherF1 better for everyone.</p>
    <p class="mb-4">This session will guide you through a series of tasks to test different features of our application. Please try to complete each task and provide your feedback on the experience.</p>
    
    <div class="mb-4">
      <h3 class="text-lg font-medium mb-2">What to expect:</h3>
      <ul class="list-disc list-inside space-y-1">
        <li>You'll be asked to complete several tasks</li>
        <li>We'll track your progress and collect your feedback</li>
        <li>The entire process should take about 10-15 minutes</li>
        <li>Your responses are anonymous and will only be used to improve our service</li>
      </ul>
    </div>
    
    <button id="start-test-btn" class="btn-primary mt-4">
      <i class="fas fa-play-circle mr-2"></i>Start Testing
    </button>
  </div>
  
  <div id="test-progress" class="hidden">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">Test Progress</h2>
      <span id="progress-indicator" class="px-3 py-1 bg-blue-600 text-white rounded-full text-sm">Task 1 of 4</span>
    </div>
    
    <div class="progress-bar-container w-full h-2 bg-gray-700 rounded-full mb-6">
      <div id="progress-bar" class="h-full bg-blue-600 rounded-full" style="width: 25%"></div>
    </div>
  </div>
  
  <div id="test-scenarios" class="hidden space-y-8">
    {% for scenario in test_scenarios %}
    <div id="scenario-{{ scenario.id }}" class="scenario-card bg-gray-800 p-6 rounded-lg shadow-md {% if not loop.first %}hidden{% endif %}" data-scenario-id="{{ scenario.id }}">
      <h2 class="text-xl font-semibold mb-4">{{ scenario.name }}</h2>
      <p class="mb-4">{{ scenario.description }}</p>
      
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-2">Steps:</h3>
        <ol class="list-decimal list-inside space-y-1">
          {% for step in scenario.steps %}
          <li>{{ step }}</li>
          {% endfor %}
        </ol>
      </div>
      
      <div class="mb-6">
        <h3 class="text-lg font-medium mb-2">Success Criteria:</h3>
        <p>{{ scenario.success_criteria }}</p>
      </div>
      
      <div class="task-feedback mb-6">
        <h3 class="text-lg font-medium mb-4">Did you complete this task successfully?</h3>
        <div class="flex space-x-4">
          <button class="task-result-btn px-4 py-2 rounded-md bg-green-600 hover:bg-green-700 transition-colors" data-result="success">
            <i class="fas fa-check mr-2"></i>Yes, completed
          </button>
          <button class="task-result-btn px-4 py-2 rounded-md bg-yellow-600 hover:bg-yellow-700 transition-colors" data-result="partial">
            <i class="fas fa-exclamation-triangle mr-2"></i>Partially completed
          </button>
          <button class="task-result-btn px-4 py-2 rounded-md bg-red-600 hover:bg-red-700 transition-colors" data-result="failed">
            <i class="fas fa-times mr-2"></i>Could not complete
          </button>
        </div>
      </div>
      
      <div class="difficulty-rating mb-6">
        <h3 class="text-lg font-medium mb-2">How difficult was this task?</h3>
        <div class="flex items-center space-x-2">
          <div class="difficulty-stars flex">
            <button class="difficulty-star text-2xl text-gray-500 hover:text-yellow-500" data-value="1">★</button>
            <button class="difficulty-star text-2xl text-gray-500 hover:text-yellow-500" data-value="2">★</button>
            <button class="difficulty-star text-2xl text-gray-500 hover:text-yellow-500" data-value="3">★</button>
            <button class="difficulty-star text-2xl text-gray-500 hover:text-yellow-500" data-value="4">★</button>
            <button class="difficulty-star text-2xl text-gray-500 hover:text-yellow-500" data-value="5">★</button>
          </div>
          <span class="difficulty-label ml-2">Not selected</span>
        </div>
      </div>
      
      <div class="comments mb-6">
        <h3 class="text-lg font-medium mb-2">Additional Comments</h3>
        <textarea class="task-comment w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white" rows="3" placeholder="Please share any thoughts, issues, or suggestions about this task..."></textarea>
      </div>
      
      <div class="flex justify-between">
        {% if not loop.first %}
        <button class="prev-scenario-btn btn-secondary">
          <i class="fas fa-arrow-left mr-2"></i>Previous Task
        </button>
        {% else %}
        <div></div>
        {% endif %}
        
        {% if not loop.last %}
        <button class="next-scenario-btn btn-primary">
          Next Task<i class="fas fa-arrow-right ml-2"></i>
        </button>
        {% else %}
        <button class="complete-test-btn btn-primary">
          Complete Test<i class="fas fa-check-circle ml-2"></i>
        </button>
        {% endif %}
      </div>
    </div>
    {% endfor %}
  </div>
  
  <div id="test-complete" class="hidden bg-gray-800 p-6 rounded-lg shadow-md">
    <h2 class="text-xl font-semibold mb-4">Thank You!</h2>
    <p class="mb-6">Thank you for completing the usability test. Your feedback is invaluable to us and will help improve ImgCipherF1.</p>
    
    <div class="overall-satisfaction mb-6">
      <h3 class="text-lg font-medium mb-2">Overall, how satisfied are you with ImgCipherF1?</h3>
      <div class="flex items-center space-x-2">
        <div class="satisfaction-stars flex">
          <button class="satisfaction-star text-2xl text-gray-500 hover:text-yellow-500" data-value="1">★</button>
          <button class="satisfaction-star text-2xl text-gray-500 hover:text-yellow-500" data-value="2">★</button>
          <button class="satisfaction-star text-2xl text-gray-500 hover:text-yellow-500" data-value="3">★</button>
          <button class="satisfaction-star text-2xl text-gray-500 hover:text-yellow-500" data-value="4">★</button>
          <button class="satisfaction-star text-2xl text-gray-500 hover:text-yellow-500" data-value="5">★</button>
        </div>
        <span class="satisfaction-label ml-2">Not selected</span>
      </div>
    </div>
    
    <div class="final-comments mb-6">
      <h3 class="text-lg font-medium mb-2">Any final thoughts or suggestions?</h3>
      <textarea id="final-comments" class="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-white" rows="4" placeholder="Please share any additional feedback about your experience..."></textarea>
    </div>
    
    <button id="submit-test-btn" class="btn-primary">
      <i class="fas fa-paper-plane mr-2"></i>Submit Feedback
    </button>
  </div>
  
  <div id="feedback-submitted" class="hidden bg-green-800 p-6 rounded-lg shadow-md text-center">
    <i class="fas fa-check-circle text-5xl text-green-400 mb-4"></i>
    <h2 class="text-xl font-semibold mb-4">Feedback Submitted Successfully!</h2>
    <p class="mb-6">Thank you for your valuable feedback. Your input will help us improve ImgCipherF1.</p>
    <a href="{{ url_for('main.index') }}" class="btn-primary inline-block">
      <i class="fas fa-home mr-2"></i>Return to Home
    </a>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ versioned_url_for('static', filename='js/usability_feedback.js') }}" defer></script>
{% endblock %}
