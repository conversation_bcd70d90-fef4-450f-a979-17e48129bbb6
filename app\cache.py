"""
Cache configuration for the application.
Provides a centralized cache instance using <PERSON><PERSON> as the backend.
"""

from flask_caching import Cache

# Initialize cache extension
cache = Cache()

# Cache key prefixes for different types of data
CACHE_KEY_VIBES = "vibes"
CACHE_KEY_CONFESSION = "confession"
CACHE_KEY_TECH_HISTORY = "tech_history"
CACHE_KEY_USER_ROLES = "user_roles:{}"
CACHE_KEY_STATIC_PAGE = "static_page:{}"

# Cache timeouts (in seconds)
TIMEOUT_SHORT = 60  # 1 minute
TIMEOUT_MEDIUM = 300  # 5 minutes
TIMEOUT_LONG = 3600  # 1 hour
TIMEOUT_VERY_LONG = 86400  # 24 hours

# Cache decorators for common patterns


def cached_vibes(f):
    """Cache decorator for vibes data."""
    return cache.cached(timeout=TIMEOUT_MEDIUM, key_prefix=CACHE_KEY_VIBES)(f)


def cached_confession(f):
    """Cache decorator for random confession."""
    return cache.cached(timeout=TIMEOUT_SHORT, key_prefix=CACHE_KEY_CONFESSION)(f)


def cached_tech_history(f):
    """Cache decorator for tech history facts."""
    return cache.cached(timeout=TIMEOUT_LONG, key_prefix=CACHE_KEY_TECH_HISTORY)(f)


def cached_user_roles(user_id):
    """Cache decorator for user roles."""

    def decorator(f):
        return cache.cached(timeout=TIMEOUT_LONG, key_prefix=CACHE_KEY_USER_ROLES.format(user_id))(f)

    return decorator


def cached_static_page(page_name):
    """Cache decorator for static pages."""

    def decorator(f):
        return cache.cached(timeout=TIMEOUT_VERY_LONG, key_prefix=CACHE_KEY_STATIC_PAGE.format(page_name))(f)

    return decorator


def invalidate_vibes_cache():
    """Invalidate the vibes cache."""
    cache.delete(CACHE_KEY_VIBES)


def invalidate_confession_cache():
    """Invalidate the confession cache."""
    cache.delete(CACHE_KEY_CONFESSION)


def invalidate_user_roles_cache(user_id):
    """Invalidate the user roles cache for a specific user."""
    cache.delete(CACHE_KEY_USER_ROLES.format(user_id))


def invalidate_static_page_cache(page_name):
    """Invalidate the static page cache for a specific page."""
    cache.delete(CACHE_KEY_STATIC_PAGE.format(page_name))
