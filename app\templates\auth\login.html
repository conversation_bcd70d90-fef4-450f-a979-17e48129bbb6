{% extends "base.html" %}

{% block title %}Sign In{% endblock %}

{% block content %}
<div class="max-w-md mx-auto mt-24 p-6 rounded-lg shadow-lg">
  <h1 class="text-2xl font-bold mb-6 text-center">Sign In</h1>
  
  <form method="POST" action="{{ url_for('auth.login') }}">
    {{ form.hidden_tag() }}
    
    <div class="mb-4">
      {{ form.username.label(class="block text-sm font-medium mb-1") }}
      {{ form.username(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.username.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-4">
      {{ form.password.label(class="block text-sm font-medium mb-1") }}
      {{ form.password(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.password.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-6 flex items-center">
      {{ form.remember_me(class="mr-2") }}
      {{ form.remember_me.label(class="text-sm") }}
    </div>
    
    <div class="mb-4">
      {{ form.submit(class="w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2") }}
    </div>
  </form>
  
  <div class="mt-4 text-center text-sm">
    <p>Don't have an account? <a href="{{ url_for('auth.register') }}" class="text-blue-600 hover:underline">Register</a></p>
    <p class="mt-2"><a href="{{ url_for('auth.reset_password_request') }}" class="text-blue-600 hover:underline">Forgot your password?</a></p>
  </div>
</div>
{% endblock %}
