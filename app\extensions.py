from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user
from prometheus_client import Counter
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# SQLAlchemy (Lazy Initialization)
db = SQLAlchemy()

# Login Manager (Lazy Initialization)
login_manager = LoginManager()
login_manager.login_view = "auth.login"
login_manager.login_message = "Please log in to access this page."
login_manager.login_message_category = "info"

# Rate Limiter (Lazy Initialization)
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="memory://",
)


# Custom key function for different limits based on user role
def limiter_key_func():
    """
    Returns different keys for rate limiting based on user authentication status.
    Authenticated users get their user ID as key, guests get their IP address.
    """
    if current_user.is_authenticated:
        return str(current_user.id)
    return get_remote_address()


# Prometheus Metrics
file_conversions = Counter(
    "file_conversions",
    "Total number of file conversions",
    labelnames=["status", "user_type"],  # Labels for tracking success/failure and user type
)


def track_conversion(status="success", user_type="registered"):
    """
    Increments the file conversion counter with status and user type labels.

    Args:
        status: Success or failure status of the conversion
        user_type: Type of user (guest or registered)
    """
    file_conversions.labels(status=status, user_type=user_type).inc()
