/**
 * ImgCipherF1 QA Fixes
 * Cross-browser compatibility and responsive design fixes
 * Based on QA testing results
 */

/* General cross-browser fixes */
* {
  /* Prevent text size adjustment after orientation changes in iOS */
  -webkit-text-size-adjust: 100%;
  /* Improve rendering of fonts on OSX */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Fix for Safari flickering issues with transforms */
.dropdown-menu,
.btn-primary,
.btn-secondary,
.card-hover-effect {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform;
}

/* Fix for Firefox focus rings */
::-moz-focus-inner {
  border: 0;
}

/* Fix for Edge and IE form elements */
input, select, textarea {
  -ms-overflow-style: none;
}

/* Fix for Safari file inputs */
input[type="file"] {
  -webkit-appearance: none;
  appearance: none;
}

/* Fix for mobile Safari button styling */
button, 
input[type="button"], 
input[type="submit"] {
  -webkit-appearance: none;
  appearance: none;
  border-radius: 0.25rem;
}

/* Fix for Samsung Internet touch events */
.drag-drop-area {
  touch-action: none;
}

/* Fix for Safari drag and drop */
.drag-drop-area {
  -webkit-user-select: none;
  user-select: none;
}

/* Improved fallback for browsers that don't support drag and drop */
.drag-drop-fallback {
  display: none;
}

@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
  .drag-drop-fallback {
    display: block;
    margin-top: 1rem;
  }
}

/* Fix for Safari animations */
@-webkit-keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@-webkit-keyframes slideInTop {
  from { 
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
    opacity: 0;
  }
  to { 
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

@-webkit-keyframes pulse {
  0% { -webkit-transform: scale(1); transform: scale(1); }
  50% { -webkit-transform: scale(1.05); transform: scale(1.05); }
  100% { -webkit-transform: scale(1); transform: scale(1); }
}

@-webkit-keyframes bounce {
  0%, 100% { -webkit-transform: translateY(0); transform: translateY(0); }
  50% { -webkit-transform: translateY(-10px); transform: translateY(-10px); }
}

@-webkit-keyframes spin {
  from { -webkit-transform: rotate(0deg); transform: rotate(0deg); }
  to { -webkit-transform: rotate(360deg); transform: rotate(360deg); }
}

@-webkit-keyframes shake {
  0%, 100% { -webkit-transform: translateX(0); transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { -webkit-transform: translateX(-5px); transform: translateX(-5px); }
  20%, 40%, 60%, 80% { -webkit-transform: translateX(5px); transform: translateX(5px); }
}

@-webkit-keyframes checkmark {
  0% { -webkit-transform: scale(0); transform: scale(0); }
  100% { -webkit-transform: scale(1); transform: scale(1); }
}

@-webkit-keyframes ripple {
  0% {
    -webkit-transform: scale(0, 0);
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(20, 20);
    transform: scale(20, 20);
    opacity: 0;
  }
}

/* Fix for tooltip positioning on mobile */
@media (max-width: 768px) {
  [data-tooltip]::after {
    width: auto;
    max-width: 200px;
    white-space: normal;
    line-height: 1.2;
    padding: 0.5rem;
    font-size: 0.75rem;
  }
  
  [data-tooltip][data-tooltip-position="top"]::after {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-6px);
  }
  
  [data-tooltip][data-tooltip-position="bottom"]::after {
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(6px);
  }
  
  [data-tooltip][data-tooltip-position="left"]::after {
    right: 100%;
    top: 50%;
    transform: translateY(-50%) translateX(-6px);
  }
  
  [data-tooltip][data-tooltip-position="right"]::after {
    left: 100%;
    top: 50%;
    transform: translateY(-50%) translateX(6px);
  }
}

/* Fix for mobile form elements */
@media (max-width: 480px) {
  input, select, textarea {
    font-size: 16px !important; /* Prevent iOS zoom on focus */
  }
  
  .form-group {
    margin-bottom: 1.25rem;
  }
  
  button, 
  .btn-primary, 
  .btn-secondary {
    padding: 0.625rem 1rem;
    min-height: 44px; /* Minimum touch target size */
  }
  
  /* Increase spacing between form elements */
  .form-group + .form-group {
    margin-top: 1.5rem;
  }
  
  /* Make checkboxes and radio buttons easier to tap */
  input[type="checkbox"], 
  input[type="radio"] {
    width: 1.5rem;
    height: 1.5rem;
  }
}

/* Fix for mobile navigation */
@media (max-width: 480px) {
  #mobile-menu a {
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
  }
  
  .mobile-menu {
    width: 80%; /* Wider mobile menu */
  }
}

/* Fix for tablet layout issues */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  .grid {
    gap: 1.25rem;
  }
}

/* Fix for high-DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .menu-item i,
  .dropdown-menu a i {
    transform: translateZ(0); /* Prevent blurry icons */
  }
}

/* Fix for older browsers that don't support CSS variables */
@supports not (--test: 0) {
  .btn-primary {
    background-color: #1D4ED8;
    color: #FFFFFF;
  }
  
  .btn-secondary {
    background-color: #4B5563;
    color: #F9FAFB;
  }
  
  body {
    background-color: #111827;
    color: #F9FAFB;
  }
  
  nav, footer {
    background-color: #1F2937;
  }
}

/* Fix for browsers that don't support focus-visible */
@supports not selector(:focus-visible) {
  a:focus, button:focus, input:focus, select:focus, textarea:focus, [tabindex]:focus {
    outline: 2px solid #60A5FA;
    outline-offset: 2px;
  }
}

/* Fix for browsers that don't support backdrop-filter */
@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
  .dropdown-menu,
  .contextual-help-panel {
    background-color: rgba(31, 41, 55, 0.95);
  }
}

/* Fix for print styles */
@media print {
  #mobile-menu-button {
    display: none !important;
  }
  
  body {
    background-color: white !important;
    color: black !important;
  }
  
  .container {
    max-width: 100% !important;
    padding: 0 !important;
  }
}

/* Fix for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
