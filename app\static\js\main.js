document.addEventListener('DOMContentLoaded', () => {
    // Mobile Menu Toggle
    document.getElementById('mobile-menu-button').addEventListener('click', () => {
      document.getElementById('mobile-menu').classList.remove('translate-x-full');
    });
    document.getElementById('close-mobile-menu').addEventListener('click', () => {
      document.getElementById('mobile-menu').classList.add('translate-x-full');
    });
  
    // Tab Switching for Sidebars
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tab = button.getAttribute('data-tab');
        const sidebar = button.closest('aside');
        const contents = sidebar.querySelectorAll('.tab-content');
        contents.forEach(content => content.classList.add('hidden'));
        sidebar.querySelector(`#${tab}-content`).classList.remove('hidden');
        sidebar.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
      });
    });
    // Set default active tabs
    document.querySelectorAll('aside').forEach(sidebar => {
      const firstButton = sidebar.querySelector('.tab-button');
      if (firstButton) {
        firstButton.classList.add('active');
        const defaultTab = firstButton.getAttribute('data-tab');
        sidebar.querySelector(`#${defaultTab}-content`).classList.remove('hidden');
      }
    });
  
    // Quick Poll
    document.querySelectorAll('.poll-option').forEach(button => {
      button.addEventListener('click', async () => {
        const choice = button.textContent;
        await fetch('/api/poll/vote', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ choice })
        });
        const response = await fetch('/api/poll/results');
        const results = await response.json();
        document.getElementById('tabs-percent').textContent = results.tabs;
        document.getElementById('spaces-percent').textContent = results.spaces;
        document.getElementById('poll-results').classList.remove('hidden');
      });
    });
  
    // Dev Vibe Selector
    const vibeColors = {
      productive: 'bg-green-700',
      tired: 'bg-gray-700',
      debugging: 'bg-red-700'
    };
    document.querySelectorAll('.vibe-option').forEach(button => {
      button.addEventListener('click', () => {
        const vibe = button.getAttribute('data-vibe');
        const sidebar = button.closest('aside');
        Object.values(vibeColors).forEach(cls => sidebar.classList.remove(cls));
        sidebar.classList.add(vibeColors[vibe]);
        localStorage.setItem('dev-vibe', vibe);
      });
    });
    const savedVibe = localStorage.getItem('dev-vibe');
    if (savedVibe) {
      const sidebar = document.querySelector('aside:nth-child(3)'); // Right sidebar
      sidebar.classList.add(vibeColors[savedVibe]);
    }
  
    // Today in Tech History
    async function loadTechHistory() {
      const response = await fetch('/api/tech-history');
      const data = await response.json();
      document.getElementById('tech-history-text').textContent = data.text;
      document.getElementById('tech-history-link').href = data.link;
    }
    loadTechHistory();
  
    // Anonymous Confessions
    let confessions = [];
    let currentIndex = 0;
    async function loadConfessions() {
      const response = await fetch('/api/confessions');
      confessions = await response.json();
      showConfession();
    }
    function showConfession() {
      if (confessions.length > 0) {
        document.getElementById('confession-text').textContent = confessions[currentIndex];
      }
    }
    document.getElementById('next-confession').addEventListener('click', () => {
      currentIndex = (currentIndex + 1) % confessions.length;
      showConfession();
    });
    loadConfessions();
  });