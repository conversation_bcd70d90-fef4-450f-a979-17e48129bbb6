"""
Tests for contextual help functionality.
"""

# These imports are used by pytest fixtures
import pytest  # noqa: F401
from flask import url_for  # noqa: F401
from flask.testing import Flask<PERSON><PERSON>


def test_contextual_help_css_included(client: FlaskClient) -> None:
    """Test that the contextual help CSS file is included in the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for contextual help CSS link
    assert b'href="/static/css/contextual-help.css"' in response.data
    assert b'rel="stylesheet"' in response.data


def test_contextual_help_js_included(client: FlaskClient) -> None:
    """Test that the contextual help JavaScript file is included in the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for contextual help JS script
    assert b'src="/static/js/contextual-help.js"' in response.data


def test_tooltips_exist(client: FlaskClient) -> None:
    """Test that tooltips are added to key UI elements."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for tooltips on navigation items
    assert b'data-tooltip="Go to the homepage"' in response.data
    assert b'data-tooltip="PDF manipulation tools"' in response.data
    assert b'data-tooltip="Word document tools"' in response.data
    assert b'data-tooltip="Data conversion tools"' in response.data
    assert b'data-tooltip="Image manipulation tools"' in response.data
    assert b'data-tooltip="Text extraction tools"' in response.data


def test_help_panel_exists(client: FlaskClient) -> None:
    """Test that the help panel exists in the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for help panel
    assert b'id="main-help-panel"' in response.data
    assert b'class="contextual-help-panel"' in response.data
    assert b"ImgCipher Help" in response.data


def test_help_toggle_button_exists(client: FlaskClient) -> None:
    """Test that the help toggle button exists in the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for help toggle button
    assert b'id="help-button"' in response.data
    assert b'data-help-toggle="main-help-panel"' in response.data
    assert b'aria-expanded="false"' in response.data
