/*
 * Enhanced Responsive Layout Styling
 * Improves mobile experience with better breakpoints and layout adjustments
 * Updated with cross-browser compatibility fixes and improved mobile experience
 */

/* Base responsive adjustments */
@media (max-width: 640px) {
  /* Improve spacing for mobile */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Adjust form containers for mobile */
  .form-container {
    padding: 1.25rem;
  }

  /* Improve touch targets */
  button,
  .btn,
  input[type="submit"] {
    min-height: 2.75rem;
  }

  /* Adjust headings for mobile */
  h1 {
    font-size: 1.75rem;
    line-height: 2rem;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 1.75rem;
  }

  /* Improve spacing between form elements */
  .form-group + .form-group,
  .space-y-4 > * + * {
    margin-top: 1.25rem;
  }

  /* Ensure drop areas have enough space */
  .drop-area {
    padding: 1.5rem 1rem;
  }

  /* Adjust grid layouts to single column */
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  /* Improve spacing for file details */
  #file-details {
    margin-top: 1rem;
  }

  /* Adjust error messages for mobile */
  .error-message {
    padding: 0.75rem;
  }
}

/* Small mobile devices */
@media (max-width: 380px) {
  /* Further reduce padding */
  .form-container {
    padding: 1rem;
  }

  /* Adjust button text size */
  button,
  .btn,
  input[type="submit"] {
    font-size: 0.875rem;
  }

  /* Reduce heading size further */
  h1 {
    font-size: 1.5rem;
  }
}

/* Medium screens (tablets) */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Adjust form containers for tablets */
  .form-container {
    padding: 1.5rem;
  }

  /* Improve grid layouts for tablets */
  .grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Specific component adjustments */

/* Tool cards on homepage */
@media (max-width: 640px) {
  .tool-card {
    padding: 1.25rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .tool-card h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .tool-card p {
    font-size: 0.875rem;
    line-height: 1.4;
  }

  /* Improve tool card touch targets */
  .tool-card a {
    display: block;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
  }
}

/* Form layouts */
@media (max-width: 640px) {
  /* Convert 2-column layouts to 1-column on mobile */
  .split-options-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* Ensure form inputs have enough height for touch */
  input[type="text"],
  input[type="number"],
  input[type="email"],
  input[type="password"],
  select,
  textarea {
    height: 2.75rem;
    font-size: 1rem;
  }

  /* Improve checkbox/radio alignment */
  .checkbox-wrapper,
  .radio-wrapper {
    display: flex;
    align-items: center;
  }

  .checkbox-wrapper input,
  .radio-wrapper input {
    margin-right: 0.5rem;
  }
}

/* Navigation improvements */
@media (max-width: 640px) {
  /* Adjust mobile menu padding */
  #mobile-menu {
    padding: 1.5rem 1.25rem;
    width: 85%; /* Wider mobile menu */
    max-width: 320px;
  }

  /* Improve mobile menu item spacing */
  #mobile-menu a {
    padding: 0.875rem 1rem;
    margin-bottom: 0.375rem;
    border-radius: 0.375rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
  }

  #mobile-menu a i {
    margin-right: 0.75rem;
    font-size: 1.125rem;
    min-width: 1.5rem;
    text-align: center;
  }

  /* Adjust logo size */
  nav img {
    height: 2rem;
  }

  /* Improve mobile menu button */
  #mobile-menu-button {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
  }

  /* Improve dropdown positioning on mobile */
  .dropdown-menu {
    min-width: 200px;
    max-width: 90vw;
  }
}

/* Progress bars and status indicators */
@media (max-width: 640px) {
  #progress-container {
    margin-top: 1rem;
  }

  #progress-bar {
    height: 0.5rem;
  }
}

/* File preview adjustments */
@media (max-width: 640px) {
  #file-preview-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .file-preview-item {
    max-width: 100%;
  }
}

/* Toast notifications */
@media (max-width: 640px) {
  #toast-container {
    max-width: 90%;
    left: 5%;
    right: 5%;
  }
}

/* Improve form spacing */
@media (max-width: 640px) {
  .form-row {
    margin-bottom: 1rem;
  }

  label {
    margin-bottom: 0.375rem;
    display: block;
    font-size: 0.9375rem;
    font-weight: 500;
  }
}

/* Fixed position elements adjustments */
@media (max-width: 640px) {

}

/* Landscape mode optimizations */
@media (max-width: 896px) and (orientation: landscape) {


  /* Reduce form padding in landscape */
  .form-container {
    padding: 1rem;
    max-height: 80vh;
    overflow-y: auto;
  }

  /* Adjust headings in landscape */
  h1 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }

  /* Optimize form layouts for landscape */
  .grid-cols-1 {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}
