{% extends "base.html" %}

{% block title %}Profile{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto mt-24 p-6 rounded-lg shadow-lg">
  <h1 class="text-2xl font-bold mb-6">User Profile</h1>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-opacity-10 p-4 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Account Information</h2>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">Username</p>
        <p class="font-medium">{{ current_user.username }}</p>
      </div>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">Email</p>
        <p class="font-medium">{{ current_user.email }}</p>
      </div>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">Account Status</p>
        <p class="font-medium">{{ current_user.status.value }}</p>
      </div>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">Email Verified</p>
        <p class="font-medium">{{ 'Yes' if current_user.email_verified else 'No' }}</p>
      </div>
    </div>
    
    <div class="bg-opacity-10 p-4 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Security</h2>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">Last Login</p>
        <p class="font-medium">{{ current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') if current_user.last_login else 'Never' }}</p>
      </div>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">Login Count</p>
        <p class="font-medium">{{ current_user.login_count }}</p>
      </div>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">API Key</p>
        <div class="flex items-center">
          <input type="password" value="{{ current_user.api_key }}" class="bg-transparent border rounded px-2 py-1 mr-2 w-full" readonly id="apiKey">
          <button onclick="toggleApiKey()" class="text-sm px-2 py-1 rounded bg-gray-700 hover:bg-gray-600">Show</button>
        </div>
      </div>
      
      <div class="mb-4">
        <p class="text-sm opacity-70">Roles</p>
        <div class="flex flex-wrap gap-2 mt-1">
          {% for role in current_user.roles %}
            <span class="px-2 py-1 text-xs rounded-full bg-blue-600 text-white">{{ role.name }}</span>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
  
  <div class="mt-8 flex justify-end">
    <a href="{{ url_for('auth.reset_password_request') }}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Change Password</a>
  </div>
</div>

<script>
  function toggleApiKey() {
    const apiKeyInput = document.getElementById('apiKey');
    const type = apiKeyInput.getAttribute('type') === 'password' ? 'text' : 'password';
    apiKeyInput.setAttribute('type', type);
    
    const button = apiKeyInput.nextElementSibling;
    button.textContent = type === 'password' ? 'Show' : 'Hide';
  }
</script>
{% endblock %}
