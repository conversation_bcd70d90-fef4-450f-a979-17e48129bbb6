[flake8]
max-line-length = 127
exclude = .git,__pycache__,build,dist,venv
ignore = E203, W503

[mypy]
python_version = 3.10
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
strict_optional = True

[mypy.plugins.flask.Flask]
ignore_missing_imports = True

[mypy.plugins.sqlalchemy.*]
ignore_missing_imports = True
