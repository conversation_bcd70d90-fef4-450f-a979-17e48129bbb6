"""
Smoke tests for ImgCipherF1 application.

These tests are designed to be run against a deployed instance of the application
to verify that the basic functionality is working correctly.

The tests require the following environment variables to be set:
- STAGING_URL: The URL of the staging environment
- STAGING_API_KEY: The API key for the staging environment (if required)

Usage:
    pytest tests/smoke_tests.py -v
"""

import os
import io
import time
import pytest
import requests
from PIL import Image
from typing import Dict, Any, List, Optional

# Get the URL from environment variables
BASE_URL = os.environ.get("STAGING_URL", "http://localhost:5000")
API_KEY = os.environ.get("STAGING_API_KEY", "")

# Test data
TEST_USERNAME = "smoketest_user"
TEST_PASSWORD = "Smoke!Test123"
TEST_EMAIL = "<EMAIL>"


def test_health_endpoint():
    """Test that the health endpoint returns a 200 status code."""
    response = requests.get(f"{BASE_URL}/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert "version" in data


def test_main_page():
    """Test that the main page loads correctly."""
    response = requests.get(BASE_URL)
    assert response.status_code == 200
    assert "ImgCipherF1" in response.text


def test_api_history():
    """Test that the history API returns a random tech history fact."""
    response = requests.get(f"{BASE_URL}/tools/api/history")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "text" in data
    assert "link" in data


def test_api_confession():
    """Test that the confession API works correctly."""
    # Get a confession
    response = requests.get(f"{BASE_URL}/tools/api/confession")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "confession" in data

    # Add a confession
    response = requests.post(f"{BASE_URL}/tools/api/confession", json={"confession": "This is a smoke test confession"})
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True


def test_api_poll():
    """Test that the poll API works correctly."""
    # Get poll results
    response = requests.get(f"{BASE_URL}/tools/api/poll")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "results" in data

    # Vote in the poll
    response = requests.post(f"{BASE_URL}/tools/api/poll", json={"option": "tabs"})
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "results" in data


def test_api_vibe():
    """Test that the vibe API works correctly."""
    # Get vibe counts
    response = requests.get(f"{BASE_URL}/tools/api/vibe")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "vibes" in data

    # Update vibe
    response = requests.post(f"{BASE_URL}/tools/api/vibe", json={"vibe": "debugging"})
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "vibes" in data


def test_image_conversion():
    """Test that image conversion works correctly."""
    # Create a test image
    img = Image.new("RGB", (100, 100), color=(73, 109, 137))
    img_io = io.BytesIO()
    img.save(img_io, "JPEG")
    img_io.seek(0)

    # Convert the image
    files = {"files": ("test.jpg", img_io, "image/jpeg")}
    data = {"format": "webp", "quality": "80"}

    response = requests.post(f"{BASE_URL}/tools/convert", files=files, data=data)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "files" in data
    assert len(data["files"]) > 0
    assert "download_url" in data["files"][0]


def test_user_registration_and_login():
    """Test user registration and login."""
    session = requests.Session()

    # Get the registration page to extract CSRF token
    response = session.get(f"{BASE_URL}/auth/register")
    assert response.status_code == 200

    # Extract CSRF token (simplified, might need adjustment based on actual implementation)
    csrf_token = None
    for line in response.text.split("\n"):
        if "csrf_token" in line and "value" in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break

    assert csrf_token is not None

    # Register a new user
    register_data = {
        "username": TEST_USERNAME,
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "password2": TEST_PASSWORD,
        "csrf_token": csrf_token,
    }

    response = session.post(f"{BASE_URL}/auth/register", data=register_data)

    # If registration fails because user already exists, try to log in instead
    if "already taken" in response.text:
        # Get the login page to extract CSRF token
        response = session.get(f"{BASE_URL}/auth/login")
        assert response.status_code == 200

        # Extract CSRF token
        csrf_token = None
        for line in response.text.split("\n"):
            if "csrf_token" in line and "value" in line:
                start = line.find('value="') + 7
                end = line.find('"', start)
                csrf_token = line[start:end]
                break

        assert csrf_token is not None

        # Log in
        login_data = {"username": TEST_USERNAME, "password": TEST_PASSWORD, "csrf_token": csrf_token, "remember_me": "y"}

        response = session.post(f"{BASE_URL}/auth/login", data=login_data)

    # Verify we're logged in by accessing the profile page
    response = session.get(f"{BASE_URL}/profile")
    assert response.status_code == 200
    assert TEST_USERNAME in response.text

    # Log out
    response = session.get(f"{BASE_URL}/auth/logout")
    assert response.status_code == 200 or response.status_code == 302


def test_rate_limiting():
    """Test that rate limiting is working correctly."""
    # Create a test image
    img = Image.new("RGB", (100, 100), color=(255, 0, 0))

    # Make multiple requests to trigger rate limiting
    success_count = 0
    rate_limited = False

    for i in range(20):
        img_io = io.BytesIO()
        img.save(img_io, "JPEG")
        img_io.seek(0)

        files = {"files": ("test.jpg", img_io, "image/jpeg")}
        data = {"format": "png", "quality": "80"}

        response = requests.post(f"{BASE_URL}/tools/convert", files=files, data=data)

        if response.status_code == 200:
            success_count += 1
        elif response.status_code == 429:
            rate_limited = True
            break

        # Add a small delay to avoid overwhelming the server
        time.sleep(0.5)

    # For guest users, we should eventually hit rate limiting
    # But we'll consider the test passed if we either get rate limited or can make at least 5 successful requests
    assert rate_limited or success_count >= 5


if __name__ == "__main__":
    # Run the tests manually
    test_health_endpoint()
    test_main_page()
    test_api_history()
    test_api_confession()
    test_api_poll()
    test_api_vibe()
    test_image_conversion()
    test_user_registration_and_login()
    test_rate_limiting()

    print("All smoke tests passed!")
