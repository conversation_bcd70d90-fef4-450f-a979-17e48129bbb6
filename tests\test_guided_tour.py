"""
Tests for guided tour functionality.
"""

# These imports are used by pytest fixtures
import pytest  # noqa: F401
from flask import url_for  # noqa: F401


def test_guided_tour_css_included(client) -> None:
    """Test that the guided tour CSS file is included in the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for guided tour CSS link
    assert b'href="/static/css/guided-tour.css"' in response.data
    assert b'rel="stylesheet"' in response.data


def test_guided_tour_js_included(client) -> None:
    """Test that the guided tour JavaScript file is included in the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for guided tour JS script
    assert b'src="/static/js/guided-tour.js"' in response.data


def test_restart_tour_button_removed(client) -> None:
    """Test that the restart tour button has been removed from the base template."""
    response = client.get("/")
    assert response.status_code == 200

    # Check that restart tour button is not present
    assert b'id="restart-tour-button"' not in response.data
    assert b'class="restart-tour-button"' not in response.data
    assert b'aria-label="Restart guided tour"' not in response.data


def test_guided_tour_script_loaded(client) -> None:
    """Test that the guided tour script is loaded."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for guided tour script
    assert b'<script src="/static/js/guided-tour.js"></script>' in response.data
