import os
import time
import logging
import threading
import platform
from flask import Blueprint, render_template, current_app, send_from_directory, abort, jsonify
from werkzeug.utils import secure_filename
from app.tasks import delete_file
from app.forms.pdf_forms import SplitPDFForm, MergePDFForm

# Logging setup
logger = logging.getLogger(__name__)

# Blueprint initialization
main_bp = Blueprint("main", __name__)


# Ensure the output folder exists
def ensure_folder_exists(folder_path):
    os.makedirs(folder_path, exist_ok=True)


@main_bp.route("/")
def index():
    """Render the homepage."""
    return render_template("index.html")


@main_bp.route("/merge_pdf", methods=["GET"])
def merge_pdf_form():
    """Render PDF merge form."""
    return render_template("merge_pdf.html", form=MergePDFForm())


@main_bp.route("/split_pdf", methods=["GET"])
def split_pdf_form():
    """Render PDF split form."""
    return render_template("split_pdf.html", form=SplitPDFForm())


@main_bp.route("/download/<filename>")
def download(filename):
    """
    Securely download a file from the output folder.

    Implements multiple security checks to prevent path traversal attacks.
    """
    # Validate filename
    if not filename or not isinstance(filename, str):
        current_app.logger.error("Invalid filename type")
        abort(400, "Invalid filename")

    # Security checks
    if not filename.isascii():
        current_app.logger.error(f"Non-ASCII filename attempted: {filename}")
        abort(400, "Filename contains invalid characters")

    if "/" in filename or "\\" in filename or ".." in filename:
        current_app.logger.error(f"Path traversal attempt: {filename}")
        abort(400, "Invalid filename")

    # Use secure_filename to sanitize
    safe_filename = secure_filename(filename)
    if safe_filename != filename:
        current_app.logger.error(f"Filename sanitized: {filename} -> {safe_filename}")
        abort(400, "Invalid filename")

    # Get absolute paths and verify the file is within the output folder
    outputs = os.path.abspath(current_app.config["OUTPUT_FOLDER"])
    file_path = os.path.abspath(os.path.join(outputs, filename))

    # Ensure the file path is within the output folder
    if not file_path.startswith(outputs):
        current_app.logger.error(f"Path traversal attempt detected: {filename}")
        abort(400, "Invalid filename")

    if not os.path.exists(file_path):
        current_app.logger.error(f"File not found: {filename}")
        abort(404, "File not found")

    # Verify it's a regular file, not a directory or symlink
    if not os.path.isfile(file_path):
        current_app.logger.error(f"Not a regular file: {filename}")
        abort(400, "Invalid file")

    # Log the download
    current_app.logger.info(f"File download: {filename}")

    return send_from_directory(outputs, filename, as_attachment=True, mimetype="application/octet-stream")


# Progress route replaced by task_progress in tools_bp


def delayed_delete(path):
    """Deletes a file after a delay using threading."""
    if os.path.exists(path):
        time.sleep(180)  # 3-minute delay
        os.remove(path)
        logger.info(f"File {path} deleted successfully.")


def schedule_deletion(*paths, delay=180):
    """Schedules file deletion using Celery or fallback threading."""
    for path in paths:
        if os.path.exists(path):
            if delete_file:
                delete_file.apply_async(args=[path], countdown=delay)  # Celery
            else:
                threading.Thread(target=delayed_delete, args=(path,)).start()  # Thread fallback


# Privacy and terms routes moved to dedicated blueprints


@main_bp.route("/browser-compatibility")
def browser_compatibility():
    """Render the browser compatibility page."""
    return render_template("browser_compatibility.html")


@main_bp.route("/health")
def health_check():
    """
    Health check endpoint for monitoring.

    Returns basic information about the application status.
    """
    # Get application version from config or default to "development"
    version = current_app.config.get("VERSION", "development")

    # Get Python version
    python_version = platform.python_version()

    # Get system info
    system_info = {
        "system": platform.system(),
        "release": platform.release(),
        "python": python_version,
    }

    # Check database connection
    db_status = "ok"
    try:
        from app.extensions import db

        db.session.execute("SELECT 1")
    except Exception as e:
        db_status = f"error: {str(e)}"

    # Check Redis connection
    redis_status = "ok"
    try:
        from app.extensions import redis_client

        redis_client.ping()
    except Exception as e:
        redis_status = f"error: {str(e)}"

    # Build response
    response = {
        "status": "ok",
        "version": version,
        "timestamp": int(time.time()),
        "environment": current_app.config.get("ENV", "production"),
        "python": python_version,
        "system": system_info,
        "services": {
            "database": db_status,
            "redis": redis_status,
        },
    }

    return jsonify(response)
