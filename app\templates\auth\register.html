{% extends "base.html" %}

{% block title %}Register{% endblock %}

{% block content %}
<div class="max-w-md mx-auto mt-24 p-6 rounded-lg shadow-lg">
  <h1 class="text-2xl font-bold mb-6 text-center">Create an Account</h1>
  
  <form method="POST" action="{{ url_for('auth.register') }}">
    {{ form.hidden_tag() }}
    
    <div class="mb-4">
      {{ form.username.label(class="block text-sm font-medium mb-1") }}
      {{ form.username(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.username.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-4">
      {{ form.email.label(class="block text-sm font-medium mb-1") }}
      {{ form.email(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.email.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-4">
      {{ form.password.label(class="block text-sm font-medium mb-1") }}
      {{ form.password(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.password.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-6">
      {{ form.password2.label(class="block text-sm font-medium mb-1") }}
      {{ form.password2(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.password2.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
    </div>
    
    <div class="mb-4">
      {{ form.submit(class="w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2") }}
    </div>
  </form>
  
  <div class="mt-4 text-center text-sm">
    <p>Already have an account? <a href="{{ url_for('auth.login') }}" class="text-blue-600 hover:underline">Sign In</a></p>
  </div>
</div>
{% endblock %}
