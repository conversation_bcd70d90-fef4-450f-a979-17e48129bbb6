["tests/test_api.py::test_confession_endpoint", "tests/test_api.py::test_poll_votes_endpoint", "tests/test_api.py::test_vibes_endpoint", "tests/test_auth.py::test_login_logout", "tests/test_auth.py::test_login_page", "tests/test_auth.py::test_profile_page", "tests/test_auth.py::test_register_page", "tests/test_auth.py::test_user_registration", "tests/test_batch_conversion.py::test_batch_convert_page_get_authenticated", "tests/test_batch_conversion.py::test_batch_convert_page_get_unauthenticated", "tests/test_batch_conversion.py::test_batch_convert_post_authenticated", "tests/test_batch_conversion.py::test_batch_convert_post_unauthenticated", "tests/test_batch_conversion.py::test_batch_progress_page_authenticated", "tests/test_batch_conversion.py::test_batch_progress_page_unauthenticated", "tests/test_batch_conversion.py::test_download_batch_authenticated", "tests/test_batch_conversion.py::test_download_batch_unauthenticated", "tests/test_batch_conversion.py::test_task_progress_api_authenticated", "tests/test_batch_conversion.py::test_task_progress_api_unauthenticated", "tests/test_batch_conversion.py::test_task_result_api_authenticated", "tests/test_batch_conversion.py::test_task_result_api_unauthenticated", "tests/test_contextual_help.py::test_contextual_help_css_included", "tests/test_contextual_help.py::test_contextual_help_js_included", "tests/test_contextual_help.py::test_help_panel_exists", "tests/test_contextual_help.py::test_help_toggle_button_exists", "tests/test_contextual_help.py::test_tooltips_exist", "tests/test_guided_tour.py::test_guided_tour_css_included", "tests/test_guided_tour.py::test_guided_tour_home_page", "tests/test_guided_tour.py::test_guided_tour_initialization", "tests/test_guided_tour.py::test_guided_tour_js_included", "tests/test_guided_tour.py::test_guided_tour_script_loaded", "tests/test_guided_tour.py::test_guided_tour_tools_page", "tests/test_guided_tour.py::test_restart_tour_button_exists", "tests/test_guided_tour.py::test_restart_tour_functionality", "tests/test_main_routes.py::test_index_page", "tests/test_main_routes.py::test_merge_pdf_form", "tests/test_main_routes.py::test_split_pdf_form", "tests/test_performance.py::test_cache_hit_miss_performance", "tests/test_performance.py::test_etag_performance", "tests/test_performance.py::test_static_page_caching", "tests/test_theme_toggle.py::test_html_has_data_theme_attribute", "tests/test_theme_toggle.py::test_theme_css_included", "tests/test_theme_toggle.py::test_theme_toggle_button_exists", "tests/test_theme_toggle.py::test_theme_toggle_js_included", "tests/test_tools.py::test_convert_file_guest_size_limit", "tests/test_tools.py::test_convert_file_invalid_file", "tests/test_tools.py::test_convert_file_page_get", "tests/test_tools.py::test_convert_file_page_get_authenticated", "tests/test_tools.py::test_convert_file_registered_size_limit", "tests/test_tools.py::test_rate_limiting", "tests/test_tools.py::test_rate_limiting_simulation", "tests/test_tools_routes.py::test_merge_pdf_page", "tests/test_tools_routes.py::test_privacy_page", "tests/test_tools_routes.py::test_split_pdf_page", "tests/test_tools_routes.py::test_terms_page", "tests/test_user_model.py::test_create_role", "tests/test_user_model.py::test_create_user", "tests/test_user_model.py::test_user_api_key", "tests/test_user_model.py::test_user_roles", "tests/test_user_model.py::test_user_status"]