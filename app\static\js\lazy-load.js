/**
 * Lazy Loading Utility
 * Provides functionality for lazy loading images and other resources
 */

(function() {
  'use strict';
  
  // Check if IntersectionObserver is supported
  if ('IntersectionObserver' in window) {
    const lazyImageObserver = new IntersectionObserver(function(entries, observer) {
      entries.forEach(function(entry) {
        if (entry.isIntersecting) {
          const lazyImage = entry.target;
          
          // Handle different types of lazy loading
          if (lazyImage.dataset.src) {
            lazyImage.src = lazyImage.dataset.src;
            lazyImage.removeAttribute('data-src');
          }
          
          if (lazyImage.dataset.srcset) {
            lazyImage.srcset = lazyImage.dataset.srcset;
            lazyImage.removeAttribute('data-srcset');
          }
          
          if (lazyImage.dataset.background) {
            lazyImage.style.backgroundImage = `url('${lazyImage.dataset.background}')`;
            lazyImage.removeAttribute('data-background');
          }
          
          lazyImage.classList.remove('lazy');
          lazyImageObserver.unobserve(lazyImage);
        }
      });
    }, {
      rootMargin: '200px 0px', // Start loading when image is 200px from viewport
      threshold: 0.01 // Trigger when at least 1% of the image is visible
    });
    
    // Observe all elements with the 'lazy' class
    document.addEventListener('DOMContentLoaded', function() {
      const lazyImages = document.querySelectorAll('.lazy');
      lazyImages.forEach(function(lazyImage) {
        lazyImageObserver.observe(lazyImage);
      });
    });
    
    // Re-check for new lazy elements after DOM changes
    // This is useful for dynamically added content
    const mutationObserver = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === 1) { // Element node
            const newLazyImages = node.querySelectorAll('.lazy');
            newLazyImages.forEach(function(lazyImage) {
              lazyImageObserver.observe(lazyImage);
            });
          }
        });
      });
    });
    
    // Start observing the document body for DOM changes
    mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    function lazyLoad() {
      const lazyImages = document.querySelectorAll('.lazy');
      const windowHeight = window.innerHeight;
      
      lazyImages.forEach(function(lazyImage) {
        const rect = lazyImage.getBoundingClientRect();
        const isInViewport = (
          rect.bottom >= 0 &&
          rect.right >= 0 &&
          rect.top <= windowHeight &&
          rect.left <= window.innerWidth
        );
        
        if (isInViewport) {
          if (lazyImage.dataset.src) {
            lazyImage.src = lazyImage.dataset.src;
            lazyImage.removeAttribute('data-src');
          }
          
          if (lazyImage.dataset.srcset) {
            lazyImage.srcset = lazyImage.dataset.srcset;
            lazyImage.removeAttribute('data-srcset');
          }
          
          if (lazyImage.dataset.background) {
            lazyImage.style.backgroundImage = `url('${lazyImage.dataset.background}')`;
            lazyImage.removeAttribute('data-background');
          }
          
          lazyImage.classList.remove('lazy');
        }
      });
      
      // If all images are loaded, stop listening
      if (lazyImages.length === 0) {
        document.removeEventListener('scroll', lazyLoad);
        window.removeEventListener('resize', lazyLoad);
        window.removeEventListener('orientationchange', lazyLoad);
      }
    }
    
    // Add event listeners for scroll, resize, and orientation change
    document.addEventListener('DOMContentLoaded', function() {
      document.addEventListener('scroll', lazyLoad);
      window.addEventListener('resize', lazyLoad);
      window.addEventListener('orientationchange', lazyLoad);
      lazyLoad(); // Initial load
    });
  }
  
  // Expose the API to the global scope
  window.lazyLoad = {
    refresh: function() {
      if ('IntersectionObserver' in window) {
        const lazyImages = document.querySelectorAll('.lazy');
        const lazyImageObserver = new IntersectionObserver(function(entries, observer) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              const lazyImage = entry.target;
              
              if (lazyImage.dataset.src) {
                lazyImage.src = lazyImage.dataset.src;
                lazyImage.removeAttribute('data-src');
              }
              
              if (lazyImage.dataset.srcset) {
                lazyImage.srcset = lazyImage.dataset.srcset;
                lazyImage.removeAttribute('data-srcset');
              }
              
              if (lazyImage.dataset.background) {
                lazyImage.style.backgroundImage = `url('${lazyImage.dataset.background}')`;
                lazyImage.removeAttribute('data-background');
              }
              
              lazyImage.classList.remove('lazy');
              lazyImageObserver.unobserve(lazyImage);
            }
          });
        }, {
          rootMargin: '200px 0px',
          threshold: 0.01
        });
        
        lazyImages.forEach(function(lazyImage) {
          lazyImageObserver.observe(lazyImage);
        });
      } else {
        // Fallback
        const lazyImages = document.querySelectorAll('.lazy');
        const windowHeight = window.innerHeight;
        
        lazyImages.forEach(function(lazyImage) {
          const rect = lazyImage.getBoundingClientRect();
          const isInViewport = (
            rect.bottom >= 0 &&
            rect.right >= 0 &&
            rect.top <= windowHeight &&
            rect.left <= window.innerWidth
          );
          
          if (isInViewport) {
            if (lazyImage.dataset.src) {
              lazyImage.src = lazyImage.dataset.src;
              lazyImage.removeAttribute('data-src');
            }
            
            if (lazyImage.dataset.srcset) {
              lazyImage.srcset = lazyImage.dataset.srcset;
              lazyImage.removeAttribute('data-srcset');
            }
            
            if (lazyImage.dataset.background) {
              lazyImage.style.backgroundImage = `url('${lazyImage.dataset.background}')`;
              lazyImage.removeAttribute('data-background');
            }
            
            lazyImage.classList.remove('lazy');
          }
        });
      }
    }
  };
})();
