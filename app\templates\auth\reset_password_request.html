{% extends "base.html" %}

{% block title %}Reset Password{% endblock %}

{% block content %}
<div class="max-w-md mx-auto mt-24 p-6 rounded-lg shadow-lg">
  <h1 class="text-2xl font-bold mb-6 text-center">Reset Password</h1>
  
  <form method="POST" action="{{ url_for('auth.reset_password_request') }}">
    {{ form.hidden_tag() }}
    
    <div class="mb-6">
      {{ form.email.label(class="block text-sm font-medium mb-1") }}
      {{ form.email(class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500") }}
      {% for error in form.email.errors %}
        <span class="text-red-500 text-sm">{{ error }}</span>
      {% endfor %}
      <p class="text-sm mt-1 opacity-70">Enter your email address and we'll send you a link to reset your password.</p>
    </div>
    
    <div class="mb-4">
      {{ form.submit(class="w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2") }}
    </div>
  </form>
  
  <div class="mt-4 text-center text-sm">
    <p><a href="{{ url_for('auth.login') }}" class="text-blue-600 hover:underline">Back to login</a></p>
  </div>
</div>
{% endblock %}
