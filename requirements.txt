alembic==1.15.1
amqp==5.3.1
attrs==25.3.0
billiard==4.2.1
blinker==1.9.0
beautifulsoup4==4.12.2
cachelib==0.13.0
celery==5.4.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
coloredlogs==15.0.1
cryptography==43.0.3
Deprecated==1.2.18
docx2pdf==0.1.8
dotenv==0.9.9
et_xmlfile==2.0.0
Flask==3.1.0
Flask-Caching==2.3.1
Flask-Limiter==3.11.0
Flask-Migrate==4.1.0
Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.2
flatbuffers==25.2.10
greenlet==3.1.1
humanfriendly==10.0
idna==3.10
imageio==2.37.0
importlib_metadata==8.6.1
itsdangerous==2.2.0
Jinja2==3.1.6
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kombu==5.5.0
lazy_loader==0.4
limits==4.2
llvmlite==0.43.0
lxml==5.3.1
Mako==1.3.9
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
mpmath==1.3.0
networkx==3.2.1
numba==0.60.0
numpy==2.0.2
onnxruntime==1.19.2
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
ordered-set==4.1.0
packaging==24.2
pandas==2.2.3
pdf2docx==0.5.6
pdfminer.six==20231228
pdfplumber==0.11.5
pillow==11.1.0
platformdirs==4.3.7
pooch==1.8.2
prometheus_client==0.21.1
prompt_toolkit==3.0.50
protobuf==6.30.1
psycopg2==2.9.10
pycparser==2.22
pycryptodome==3.22.0
Pygments==2.19.1
PyMatting==1.1.13
PyPDF2==3.0.1
pypdfium2==4.30.1
pyreadline3==3.5.4
pytesseract==0.3.10
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-pptx==1.0.2
pytz==2025.1
referencing==0.36.2
redis==5.2.1
rembg==2.0.61
requests==2.32.3
rich==13.9.4
rpds-py==0.23.1
scikit-image==0.24.0
scipy==1.13.1
six==1.17.0
SQLAlchemy==2.0.39
sympy==1.13.3
tabula-py==2.9.0
tifffile==2024.8.30
tqdm==4.67.1
typing_extensions==4.12.2
tzdata==2025.1
unicodecsv==0.14.1
urllib3==2.3.0
URLObject==2.4.3
vine==5.1.0
Wand==0.6.13
wcwidth==0.2.13
Werkzeug==3.1.3
wrapt==1.17.2
WTForms==3.2.1
XlsxWriter==3.2.2
zipp==3.21.0