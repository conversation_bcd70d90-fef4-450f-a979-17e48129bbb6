{% extends "base.html" %}

{% block title %}Image Converter - File Processing Hub{% endblock %}
{% block meta %}
    <meta name="description" content="Convert images to WebP, JPG, PNG, GIF, or ICO formats with our free online image converter">
    <meta name="keywords" content="image converter, WebP, JPG, PNG, GIF, ICO, image format conversion">
    <meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}
{% block content %}
<div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center px-4 py-8">
    <div class="text-center max-w-xl mb-8 relative">
        <h1 class="text-3xl md:text-4xl font-bold text-white mb-4 inline-block relative">
            Image Converter
        </h1>
        <p class="text-gray-400">Convert up to 10 images into WebP, JPG, PNG, GIF, or ICO formats.</p>
    </div>

    <!-- Upload Form -->
    <div class="w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 form-container">
        <div id="toast-container" class="fixed bottom-4 right-4 space-y-2 z-50"></div>

        {% if error_message %}
        <div class="mb-6 p-4 bg-red-800 text-red-200 rounded-lg flex items-center">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ error_message }}
        </div>
        {% endif %}

        <form method="POST" action="{{ url_for('tools.image_converter_page') }}" enctype="multipart/form-data" id="image-convert-form">
            {{ form.hidden_tag() }}

            <!-- Enhanced Drag-and-Drop Area -->
            <div id="drop-area" class="drop-area image-convert" tabindex="0" role="button" aria-label="Drop image files here or click to browse">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-yellow-300 mt-2">Drag & Drop your images here (max 10)</p>
                <p class="text-gray-400">or</p>
                {{ form.files(class="hidden", id="file-input", accept="image/*", multiple=True) }}
                <button type="button" onclick="document.getElementById('file-input').click()"
                        class="mt-2 bg-yellow-600 px-4 py-2 rounded hover:bg-yellow-500 transition duration-300 text-white"
                        aria-label="Choose Images">
                    Choose Images
                </button>
                <div class="sr-only" aria-live="polite" id="file-upload-status">No images selected</div>
            </div>

            <!-- File Details -->
            <div id="file-details" class="mt-4">
                <div id="file-preview-container" class="text-gray-400 text-sm"></div>
                <div id="file-info">
                    <div id="file-count" class="text-gray-400 text-sm">No images selected.</div>
                    <div id="size-warning" class="hidden text-yellow-400 mt-1">
                        Note: Total file size should be under 20MB for best performance
                    </div>
                </div>
            </div>

            <!-- File Preview Display (initially hidden) -->
            <div id="file-preview-display" class="hidden relative mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700"></div>

            <!-- Presets -->
            <div class="mt-4" id="presets-container">
                <!-- Will be populated by JavaScript -->
            </div>

            <!-- Conversion Options - Improved for mobile -->
            <div class="space-y-4 mt-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div class="form-row">
                        <label for="output-format" class="block text-sm font-medium text-gray-300 mb-1">Output Format</label>
                        <select name="format" id="output-format" required
                                class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-yellow-600 focus:border-transparent">
                            <option value="webp">WebP</option>
                            <option value="jpg">JPG</option>
                            <option value="png">PNG</option>
                            <option value="gif">GIF</option>
                            <option value="ico">ICO</option>
                        </select>
                    </div>
                    <div class="form-row">
                        <label for="quality" class="block text-sm font-medium text-gray-300 mb-1">Quality (0-100)</label>
                        <input type="number" name="quality" id="quality" value="80" min="0" max="100" required
                               class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-yellow-600 focus:border-transparent">
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="submit-button"
                    class="w-full px-4 py-3 bg-yellow-600 text-white font-semibold rounded-lg hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                    disabled>
                Convert Images
            </button>

            <!-- Progress Container - Will be populated by JavaScript -->
            <div id="progress-container" class="hidden mt-4"></div>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script src="{{ url_for('static', filename='js/presets.js') }}"></script>
<script src="{{ url_for('static', filename='js/image_converter.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize presets manager
    const presetsManager = new PresetsManager({
        presetType: 'image',
        containerSelector: '#presets-container',
        onPresetSelected: (preset) => {
            console.log('Preset selected:', preset);
        },
        getFormValues: () => {
            return {
                format: document.getElementById('output-format').value,
                quality: document.getElementById('quality').value
            };
        },
        setFormValues: (settings) => {
            if (settings.format) {
                document.getElementById('output-format').value = settings.format;
            }
            if (settings.quality) {
                document.getElementById('quality').value = settings.quality;
            }
        }
    });

    // Add class to body if user is authenticated
    {% if current_user.is_authenticated %}
    document.body.classList.add('user-authenticated');
    {% endif %}
});
{% endblock %}