# Blueprint is imported for type hints in other modules
# flake8: noqa
from flask import Blueprint


# Define blueprints here (import inside function to avoid circular imports)
def register_routes(app):
    """Dynamically register all route blueprints."""

    from .main import main_bp
    from .tools import tools_bp

    app.register_blueprint(main_bp)  # No prefix for main routes
    app.register_blueprint(tools_bp, url_prefix="/tools")  # Tools-related routes
