# ImgCipherF1 Makefile
# Provides common development and deployment tasks

.PHONY: help setup setup-dev setup-prod clean test lint format install run docker-build docker-run docker-stop

# Default target
help:
	@echo "ImgCipherF1 Development Commands"
	@echo "================================"
	@echo ""
	@echo "Setup Commands:"
	@echo "  setup-dev     - Setup development environment"
	@echo "  setup-prod    - Setup production environment"
	@echo "  setup-docker  - Setup Docker environment"
	@echo "  install       - Install dependencies only"
	@echo ""
	@echo "Development Commands:"
	@echo "  run           - Run the application in development mode"
	@echo "  test          - Run the test suite"
	@echo "  test-cov      - Run tests with coverage report"
	@echo "  lint          - Run code linting (flake8)"
	@echo "  format        - Format code (black + isort)"
	@echo "  format-check  - Check code formatting"
	@echo ""
	@echo "Database Commands:"
	@echo "  db-init       - Initialize database"
	@echo "  db-migrate    - Create database migration"
	@echo "  db-upgrade    - Apply database migrations"
	@echo "  db-reset      - Reset database (WARNING: destroys data)"
	@echo ""
	@echo "Docker Commands:"
	@echo "  docker-build  - Build Docker images"
	@echo "  docker-run    - Run application with Docker Compose"
	@echo "  docker-stop   - Stop Docker containers"
	@echo "  docker-clean  - Clean Docker images and containers"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean         - Clean temporary files and caches"
	@echo "  deps-update   - Update dependencies"
	@echo "  security      - Run security checks"

# Setup commands
setup-dev:
	python setup.py --dev

setup-prod:
	python setup.py --prod

setup-docker:
	python setup.py --docker

install:
	pip install --upgrade pip
	pip install -r requirements.txt
	pip install -r requirements-dev.txt

# Development commands
run:
	python run.py

test:
	pytest tests/ -v

test-cov:
	pytest tests/ --cov=app --cov-report=html --cov-report=term

lint:
	flake8 app tests
	mypy app --ignore-missing-imports

format:
	black app tests setup.py
	isort app tests setup.py

format-check:
	black --check app tests setup.py
	isort --check-only app tests setup.py

# Database commands
db-init:
	flask db init

db-migrate:
	flask db migrate -m "Auto migration"

db-upgrade:
	flask db upgrade

db-reset:
	@echo "WARNING: This will destroy all data in the database!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ]
	rm -f instance/app.db
	flask db upgrade

# Docker commands
docker-build:
	docker-compose build

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-clean:
	docker-compose down --rmi all --volumes --remove-orphans
	docker system prune -f

# Utility commands
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf dist
	rm -rf build

deps-update:
	pip list --outdated --format=freeze | grep -v '^\-e' | cut -d = -f 1 | xargs -n1 pip install -U

security:
	bandit -r app/
	safety check

# Development workflow
dev-setup: setup-dev
	@echo "Development environment ready!"
	@echo "Run 'make run' to start the application"

# CI/CD helpers
ci-test: install lint test

ci-build: clean install test docker-build

# Production deployment helpers
prod-deploy: setup-prod test
	@echo "Production setup complete"
	@echo "Review configuration before deploying"

# Quick development cycle
dev: format lint test
	@echo "Development checks passed!"

# Full quality check
quality: format-check lint test security
	@echo "All quality checks passed!"
