from app import create_app
from app.models.auth import Role
from app.extensions import db

app, _ = create_app()

with app.app_context():
    # Create default roles
    roles = [
        {"name": "guest", "description": "Limited access for non-registered users"},
        {"name": "registered", "description": "Standard access for registered users"},
        {"name": "admin", "description": "Full access to all features"},
    ]

    for role_data in roles:
        if not Role.query.filter_by(name=role_data["name"]).first():
            db.session.add(Role(**role_data))

    db.session.commit()
    print("Default roles created successfully")
