"""
Performance tests for the application.
These tests measure response times with and without caching.
"""

import pytest
import time
from flask import current_app


@pytest.mark.skip(reason="Redis not available in test environment")
def test_cache_hit_miss_performance(client):
    """Test cache hit/miss performance for API endpoints."""
    # Skip cache clearing since Redis is not available

    # Test endpoints
    endpoints = [
        "/api/confession",
        "/api/poll",
        "/api/vibe",
        "/api/history",
    ]

    for endpoint in endpoints:
        # First request (cache miss)
        start_time = time.time()
        response = client.get(f"/tools{endpoint}")
        assert response.status_code == 200
        miss_time = time.time() - start_time

        # Second request (cache hit)
        start_time = time.time()
        response = client.get(f"/tools{endpoint}")
        assert response.status_code == 200
        hit_time = time.time() - start_time

        # Cache hit should be faster than cache miss
        assert hit_time < miss_time, f"Cache hit ({hit_time:.4f}s) not faster than miss ({miss_time:.4f}s) for {endpoint}"

        # Log the performance improvement
        improvement = (1 - hit_time / miss_time) * 100
        print(f"{endpoint}: Cache miss: {miss_time:.4f}s, Cache hit: {hit_time:.4f}s, Improvement: {improvement:.2f}%")


@pytest.mark.skip(reason="Redis not available in test environment")
def test_etag_performance(client):
    """Test ETag performance for API endpoints."""
    # Skip cache clearing since Redis is not available

    # Test endpoints
    endpoints = [
        "/api/confession",
        "/api/poll",
        "/api/vibe",
        "/api/history",
    ]

    for endpoint in endpoints:
        # First request to get ETag
        response = client.get(f"/tools{endpoint}")
        assert response.status_code == 200
        etag = response.headers.get("ETag")
        assert etag is not None, f"No ETag header in response for {endpoint}"

        # Second request with If-None-Match header
        start_time = time.time()
        response = client.get(f"/tools{endpoint}", headers={"If-None-Match": etag})
        assert response.status_code == 304, f"Expected 304 Not Modified for {endpoint}"
        etag_time = time.time() - start_time

        # Request without ETag for comparison
        start_time = time.time()
        response = client.get(f"/tools{endpoint}")
        assert response.status_code == 200
        normal_time = time.time() - start_time

        # ETag request should be faster
        assert etag_time < normal_time, f"ETag ({etag_time:.4f}s) not faster than normal ({normal_time:.4f}s) for {endpoint}"

        # Log the performance improvement
        improvement = (1 - etag_time / normal_time) * 100
        print(f"{endpoint}: Normal: {normal_time:.4f}s, ETag: {etag_time:.4f}s, Improvement: {improvement:.2f}%")


@pytest.mark.skip(reason="Redis not available in test environment")
def test_static_page_caching(client):
    """Test caching performance for static pages."""
    # Skip cache clearing since Redis is not available

    # Test static pages
    pages = [
        "/privacy/privacy",
        "/terms/terms",
    ]

    for page in pages:
        # First request (cache miss)
        start_time = time.time()
        response = client.get(page)
        assert response.status_code == 200
        miss_time = time.time() - start_time

        # Second request (cache hit)
        start_time = time.time()
        response = client.get(page)
        assert response.status_code == 200
        hit_time = time.time() - start_time

        # Cache hit should be faster than cache miss
        assert hit_time < miss_time, f"Cache hit ({hit_time:.4f}s) not faster than miss ({miss_time:.4f}s) for {page}"

        # Log the performance improvement
        improvement = (1 - hit_time / miss_time) * 100
        print(f"{page}: Cache miss: {miss_time:.4f}s, Cache hit: {hit_time:.4f}s, Improvement: {improvement:.2f}%")
