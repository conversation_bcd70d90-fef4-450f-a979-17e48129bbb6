"""
Feedback models for ImgCipherF1 application.
Handles user feedback and usability testing data.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List

from sqlalchemy.dialects.postgresql import JSONB
from flask_login import current_user

from app.extensions import db


class Feedback(db.Model):
    """Model for storing user feedback from usability testing."""

    __tablename__ = "feedback"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey("user.id"), nullable=True)
    session_id = db.Column(db.String(64), nullable=False)
    page_url = db.Column(db.String(255), nullable=False)
    feedback_type = db.Column(db.String(50), nullable=False)
    rating = db.Column(db.Integer, nullable=True)
    comment = db.Column(db.Text, nullable=True)
    feedback_metadata = db.Column(JSONB, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    browser_info = db.Column(db.String(255), nullable=True)
    device_type = db.Column(db.String(50), nullable=True)
    screen_size = db.Column(db.String(50), nullable=True)

    # Relationships
    user = db.relationship("User", backref=db.backref("feedback", lazy=True))

    def __repr__(self) -> str:
        """String representation of the feedback."""
        return f"<Feedback {self.id}: {self.feedback_type} on {self.page_url}>"

    @classmethod
    def create(
        cls,
        session_id: str,
        page_url: str,
        feedback_type: str,
        rating: Optional[int] = None,
        comment: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        browser_info: Optional[str] = None,
        device_type: Optional[str] = None,
        screen_size: Optional[str] = None,
    ) -> "Feedback":
        """Create a new feedback entry."""
        feedback = cls(
            user_id=current_user.id if not current_user.is_anonymous else None,
            session_id=session_id,
            page_url=page_url,
            feedback_type=feedback_type,
            rating=rating,
            comment=comment,
            feedback_metadata=metadata,
            browser_info=browser_info,
            device_type=device_type,
            screen_size=screen_size,
        )
        db.session.add(feedback)
        db.session.commit()
        return feedback

    @classmethod
    def get_recent_feedback(cls, limit: int = 100) -> List["Feedback"]:
        """Get recent feedback entries."""
        return cls.query.order_by(cls.created_at.desc()).limit(limit).all()

    @classmethod
    def get_feedback_by_page(cls, page_url: str) -> List["Feedback"]:
        """Get feedback entries for a specific page."""
        return cls.query.filter_by(page_url=page_url).order_by(cls.created_at.desc()).all()

    @classmethod
    def get_feedback_by_type(cls, feedback_type: str) -> List["Feedback"]:
        """Get feedback entries of a specific type."""
        return cls.query.filter_by(feedback_type=feedback_type).order_by(cls.created_at.desc()).all()

    @classmethod
    def get_feedback_stats(cls) -> Dict[str, Any]:
        """Get feedback statistics."""
        total_count = cls.query.count()
        avg_rating = db.session.query(db.func.avg(cls.rating)).scalar() or 0

        # Count by type
        type_counts = {}
        for row in db.session.query(cls.feedback_type, db.func.count(cls.id)).group_by(cls.feedback_type).all():
            type_counts[row[0]] = row[1]

        # Count by page
        page_counts = {}
        for row in db.session.query(cls.page_url, db.func.count(cls.id)).group_by(cls.page_url).all():
            page_counts[row[0]] = row[1]

        return {
            "total_count": total_count,
            "avg_rating": float(avg_rating),
            "type_counts": type_counts,
            "page_counts": page_counts,
        }


class UsabilityTest(db.Model):
    """Model for storing usability test sessions."""

    __tablename__ = "usability_test"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=True)
    session_id = db.Column(db.String(64), nullable=False)
    test_name = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(20), default="in_progress")  # in_progress, completed, abandoned
    start_time = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime, nullable=True)
    completion_time = db.Column(db.Integer, nullable=True)  # in seconds
    tasks_completed = db.Column(db.Integer, default=0)
    tasks_total = db.Column(db.Integer, default=0)
    success_rate = db.Column(db.Float, nullable=True)
    satisfaction_score = db.Column(db.Integer, nullable=True)
    browser_info = db.Column(db.String(255), nullable=True)
    device_type = db.Column(db.String(50), nullable=True)
    screen_size = db.Column(db.String(50), nullable=True)

    # Relationships
    user = db.relationship("User", backref=db.backref("usability_tests", lazy=True))

    def __repr__(self) -> str:
        """String representation of the usability test."""
        return f"<UsabilityTest {self.id}: {self.test_name} ({self.status})>"

    def complete(self, satisfaction_score: Optional[int] = None) -> None:
        """Mark the test as completed."""
        self.status = "completed"
        self.end_time = datetime.utcnow()
        self.completion_time = (self.end_time - self.start_time).total_seconds()
        if satisfaction_score is not None:
            self.satisfaction_score = satisfaction_score
        db.session.commit()

    def abandon(self) -> None:
        """Mark the test as abandoned."""
        self.status = "abandoned"
        self.end_time = datetime.utcnow()
        self.completion_time = (self.end_time - self.start_time).total_seconds()
        db.session.commit()

    def update_progress(self, tasks_completed: int, tasks_total: int) -> None:
        """Update the test progress."""
        self.tasks_completed = tasks_completed
        self.tasks_total = tasks_total
        self.success_rate = tasks_completed / tasks_total if tasks_total > 0 else 0
        db.session.commit()
