{% extends "base.html" %}

{% block title %}Merge PDF - File Processing Hub{% endblock %}

{% block content %}
<!-- Add these in your head section -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
<script>
  pdfjsLib = pdfjsLib || {};
  pdfjsLib.GlobalWorkerOptions = {
    workerSrc: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js'
  };
</script>
<div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center px-4 py-8">
    <div class="text-center max-w-xl mb-8 relative">
        <h1 class="text-3xl md:text-4xl font-bold text-white mb-4 inline-block relative">
            Merge PDF Files
        </h1>
        <p class="text-gray-400">Combine multiple PDF documents into one unified file.</p>
    </div>

    <!-- Upload Form -->
    <div class="w-full max-w-2xl bg-gray-800 rounded-lg shadow-lg p-6 form-container">
        <div id="toast-container" class="fixed bottom-4 right-4 space-y-2"></div>

        {% if error_message %}
        <div class="mb-6 p-4 bg-red-800 text-red-200 rounded-lg flex items-center">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ error_message }}
        </div>
        {% endif %}

        <form method="POST" action="{{ url_for('tools.merge_pdf_page') }}" enctype="multipart/form-data" id="pdf-merge-form">
            {{ form.hidden_tag() }}

            <!-- Enhanced Drag-and-Drop Area -->
            <div id="drop-area" class="drop-area pdf-merge" tabindex="0" role="button" aria-label="Drop PDF files here or click to browse">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="text-red-300 mt-2">Drag & Drop your PDF files here</p>
                <p class="text-gray-400">or</p>
                {{ form.pdf_files(class="hidden", id="file-input", accept=".pdf", multiple=True) }}
                <button type="button" onclick="document.getElementById('file-input').click()"
                        class="mt-2 bg-red-600 px-4 py-2 rounded hover:bg-red-500 transition duration-300 text-white"
                        aria-label="Choose PDFs">
                    Choose PDF Files
                </button>
                <div class="sr-only" aria-live="polite" id="file-upload-status">No files selected</div>
            </div>

            <!-- File Details -->
            <div id="file-details" class="mt-4">
                <div id="file-preview-container" class="text-gray-400 text-sm"></div>
                <div id="file-info">
                    <div id="file-count" class="text-gray-400 text-sm">No PDF files selected.</div>
                    <div id="page-count" class="hidden text-red-400 text-xs mt-2"></div>
                    <div id="size-warning" class="hidden text-yellow-400 mt-1">
                        Note: Total file size should be under 50MB for best performance
                    </div>
                </div>
            </div>

            <!-- Merge Options - Improved for mobile -->
            <div class="space-y-4 mt-4">
                <div class="form-row">
                    <label for="output-filename" class="block text-sm font-medium text-gray-300 mb-1">Output Filename</label>
                    <input type="text" name="output_filename" id="output-filename" value="merged.pdf"
                           class="w-full px-4 py-2 bg-gray-700 text-gray-200 rounded-lg focus:ring-2 focus:ring-red-600 focus:border-transparent"
                           pattern="[a-zA-Z0-9-_]+\.pdf$" title="Filename must end with .pdf">
                </div>
                <div class="checkbox-wrapper flex items-center py-2">
                    <input type="checkbox" name="optimize" id="optimize" checked
                           class="h-5 w-5 text-red-600 focus:ring-red-500 border-gray-600 rounded bg-gray-700">
                    <label for="optimize" class="ml-2 block text-sm text-gray-300">Optimize PDF (reduce file size)</label>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" id="submit-button"
                    class="w-full px-4 py-3 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                    disabled>
                Merge PDFs
            </button>

            <!-- Progress Bar -->
            <div id="progress-container" class="hidden mt-4">
                <div class="flex justify-between text-sm text-gray-400 mb-1">
                    <span>Processing...</span>
                    <span id="progress-text">0%</span>
                </div>
                <div id="progress-bar" class="h-2 bg-red-600 rounded-full transition-all duration-300" style="width: 0%;"></div>
            </div>
        </form>
    </div>
</div>

<!-- JavaScript -->
<script src="{{ url_for('static', filename='js/merge_pdf.js') }}"></script>
{% endblock %}