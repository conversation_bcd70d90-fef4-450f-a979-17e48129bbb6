import mimetypes
import logging
import uuid
import hashlib
import json
from pathlib import Path
from werkzeug.utils import secure_filename
from typing import Optional, Dict, Any, Tuple
from flask import jsonify, request, Response, make_response

# Import from utility modules to avoid circular imports
from app.utils.error_responses import create_error_response
from app.utils.constants import (
    ALLOWED_IMAGE_EXTENSIONS,
    ALLOWED_FILE_EXTENSIONS,
    MAX_IMAGE_SIZE,
    MAX_FILE_SIZE,
    MAX_BATCH_SIZE,
    MAX_PAGES
)


def allowed_file(filename: str, allowed_extensions: set) -> bool:
    """Check if the file has an allowed extension and valid MIME type."""
    if not filename or "." not in filename:
        return False

    ext = filename.rsplit(".", 1)[1].lower()
    if ext not in allowed_extensions:
        print(f"Invalid extension: {ext}")
        return False

    # Skip MIME type check for certain extensions
    if ext in {"csv", "xlsx", "json", "xml", "parquet"}:
        return True

    mime_type, _ = mimetypes.guess_type(filename)
    if mime_type is None:
        return False

    # Check if the main type is 'image' for image files
    if ext in ALLOWED_IMAGE_EXTENSIONS and not mime_type.startswith("image/"):
        return False

    return True


def check_file_size(file, max_size: int) -> bool:
    """Check if the uploaded file size is within the allowed limit."""
    # Get the file size by seeking to end
    file.seek(0, 2)  # Seek to end of file
    file_size = file.tell()
    file.seek(0)  # Reset file pointer to beginning
    return file_size <= max_size


def secure_and_validate_filename(filename: str, allowed_extensions: set, max_length: int = 100) -> Optional[str]:
    """Secure and validate a filename based on length and allowed extensions."""
    if not filename:
        return None

    filename = secure_filename(filename)
    if len(filename) > max_length or not allowed_file(filename, allowed_extensions):
        return None
    return filename


def log_conversion(user: str, action: str) -> None:
    """Logs conversion actions with structured details."""
    logging.info(f"[Conversion] User: {user} | Action: {action}")


def generate_etag(data: Any) -> str:
    """
    Generate an ETag for the given data.

    Args:
        data: The data to generate an ETag for

    Returns:
        A string containing the ETag
    """
    # Convert data to JSON string and encode to bytes
    json_str = json.dumps(data, sort_keys=True)
    # Generate MD5 hash of the JSON string
    etag = hashlib.md5(json_str.encode()).hexdigest()
    return f'"{etag}"'


def add_cache_headers(response: Response, max_age: int = 300) -> Response:
    """
    Add cache control headers to a response.

    Args:
        response: The Flask response object
        max_age: The maximum age of the cache in seconds (default: 5 minutes)

    Returns:
        The modified response object
    """
    response.headers["Cache-Control"] = f"max-age={max_age}, public"
    return response


def add_etag_header(response: Response, data: Any) -> Response:
    """
    Add an ETag header to a response.

    Args:
        response: The Flask response object
        data: The data to generate an ETag for

    Returns:
        The modified response object
    """
    etag = generate_etag(data)
    response.headers["ETag"] = etag

    # Check if the client sent an If-None-Match header
    if_none_match = request.headers.get("If-None-Match")
    if if_none_match and if_none_match == etag:
        # Return 304 Not Modified if the ETag matches
        return make_response("", 304)

    return response


def save_uploaded_file(file, upload_folder: str, file_type: str) -> Tuple[str, str]:
    """Securely saves an uploaded file and returns the stored path & original filename."""
    # Check file size
    if file_type == "image" and not check_file_size(file, MAX_IMAGE_SIZE):
        raise ValueError(f"Image file exceeds the {MAX_IMAGE_SIZE // (1024 * 1024)}MB size limit.")
    elif file_type == "file" and not check_file_size(file, MAX_FILE_SIZE):
        raise ValueError(f"File exceeds the {MAX_FILE_SIZE // (1024 * 1024)}MB size limit.")

    upload_folder = Path(upload_folder)
    upload_folder.mkdir(parents=True, exist_ok=True)  # Ensure directory exists

    original_filename = secure_filename(file.filename)
    unique_filename = f"{uuid.uuid4().hex}_{original_filename}"
    file_path = upload_folder / unique_filename

    file.save(str(file_path))
    return str(file_path), original_filename  # Return full file path and original name
