#!/usr/bin/env python3
"""
Edge case tests for ImgCipherF1 application.

This script tests various edge cases that might not be covered by regular unit tests.
Run this script after the application is running locally.
"""

import os
import sys
import time
import random
import string
import requests
import argparse
from PIL import Image
import io
import json
import logging
from concurrent.futures import ThreadPoolExecutor

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Default application URL
DEFAULT_URL = "http://localhost:5000"

# Test credentials
TEST_USER = {"username": "testuser", "password": "password123"}
TEST_ADMIN = {"username": "admin", "password": "adminpass123"}


def create_test_files(output_dir):
    """Create test files for edge case testing."""
    os.makedirs(output_dir, exist_ok=True)

    # Create a very large image
    large_image_path = os.path.join(output_dir, "large_image.jpg")
    img = Image.new("RGB", (8000, 8000), color=(73, 109, 137))
    img.save(large_image_path)

    # Create a corrupt image
    corrupt_image_path = os.path.join(output_dir, "corrupt_image.jpg")
    with open(corrupt_image_path, "wb") as f:
        f.write(b"This is not a valid image file")

    # Create a zero-byte file
    zero_byte_path = os.path.join(output_dir, "zero_byte.png")
    open(zero_byte_path, "w").close()

    # Create a file with special characters in name
    special_char_path = os.path.join(output_dir, "special_@#$%^&*()_+.jpg")
    img = Image.new("RGB", (100, 100), color=(255, 0, 0))
    img.save(special_char_path)

    # Create a very long filename
    long_name = "".join(random.choices(string.ascii_letters, k=200))
    long_name_path = os.path.join(output_dir, f"{long_name}.png")
    img = Image.new("RGB", (100, 100), color=(0, 255, 0))
    img.save(long_name_path)

    logger.info(f"Created test files in {output_dir}")
    return {
        "large_image": large_image_path,
        "corrupt_image": corrupt_image_path,
        "zero_byte": zero_byte_path,
        "special_char": special_char_path,
        "long_name": long_name_path,
    }


def login(base_url, credentials):
    """Log in to the application and return the session."""
    session = requests.Session()

    # Get the login page to extract CSRF token
    response = session.get(f"{base_url}/auth/login")

    # Extract CSRF token (simplified, might need adjustment based on actual implementation)
    csrf_token = None
    for line in response.text.split("\n"):
        if "csrf_token" in line and "value" in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break

    if not csrf_token:
        logger.error("Could not extract CSRF token")
        return None

    # Log in
    login_data = {
        "username": credentials["username"],
        "password": credentials["password"],
        "csrf_token": csrf_token,
        "remember_me": "y",
    }

    response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)

    if response.url.endswith("/"):
        logger.info(f"Successfully logged in as {credentials['username']}")
        return session
    else:
        logger.error(f"Failed to log in as {credentials['username']}")
        return None


def test_large_image_conversion(base_url, session, test_files):
    """Test converting a very large image."""
    logger.info("Testing large image conversion...")

    with open(test_files["large_image"], "rb") as f:
        files = {"files": f}
        data = {"format": "webp", "quality": "80"}

        try:
            response = session.post(f"{base_url}/tools/convert", files=files, data=data)
            if response.status_code == 200:
                logger.info("Large image conversion successful")
                return True
            else:
                logger.warning(f"Large image conversion failed with status {response.status_code}")
                logger.warning(f"Response: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error during large image conversion: {str(e)}")
            return False


def test_corrupt_file_handling(base_url, session, test_files):
    """Test handling of corrupt files."""
    logger.info("Testing corrupt file handling...")

    with open(test_files["corrupt_image"], "rb") as f:
        files = {"files": f}
        data = {"format": "png", "quality": "80"}

        try:
            response = session.post(f"{base_url}/tools/convert", files=files, data=data)
            # We expect an error response, but it should be handled gracefully
            if 400 <= response.status_code < 500:
                logger.info("Corrupt file handled correctly with client error")
                return True
            elif response.status_code == 200 and "error" in response.text.lower():
                logger.info("Corrupt file handled correctly with error message")
                return True
            else:
                logger.warning(f"Unexpected response for corrupt file: {response.status_code}")
                logger.warning(f"Response: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error during corrupt file test: {str(e)}")
            return False


def test_concurrent_requests(base_url, session, test_files, num_requests=10):
    """Test handling of concurrent requests."""
    logger.info(f"Testing {num_requests} concurrent requests...")

    def make_request(_):
        with open(test_files["special_char"], "rb") as f:
            files = {"files": f}
            data = {"format": "jpg", "quality": "80"}
            try:
                response = session.post(f"{base_url}/tools/convert", files=files, data=data)
                return response.status_code
            except Exception as e:
                logger.error(f"Error during concurrent request: {str(e)}")
                return 0

    with ThreadPoolExecutor(max_workers=num_requests) as executor:
        results = list(executor.map(make_request, range(num_requests)))

    success_count = results.count(200)
    logger.info(f"Concurrent requests: {success_count} successful out of {num_requests}")
    return success_count >= num_requests * 0.8  # At least 80% should succeed


def test_rate_limiting(base_url):
    """Test rate limiting for guest users."""
    logger.info("Testing rate limiting...")

    session = requests.Session()
    success_count = 0
    rate_limited = False

    # Create a small test image in memory
    img_io = io.BytesIO()
    img = Image.new("RGB", (100, 100), color=(255, 0, 0))
    img.save(img_io, "JPEG")
    img_io.seek(0)

    # Make multiple requests to trigger rate limiting
    for i in range(10):
        files = {"files": ("test.jpg", img_io, "image/jpeg")}
        data = {"format": "png", "quality": "80"}

        try:
            response = session.post(f"{base_url}/tools/convert", files=files, data=data)
            if response.status_code == 200:
                success_count += 1
            elif response.status_code == 429:
                rate_limited = True
                logger.info(f"Rate limiting triggered after {i+1} requests")
                break

            # Reset file pointer for next request
            img_io.seek(0)

        except Exception as e:
            logger.error(f"Error during rate limiting test: {str(e)}")

    if rate_limited:
        logger.info("Rate limiting test passed")
        return True
    else:
        logger.warning("Rate limiting not triggered after 10 requests")
        return False


def main():
    parser = argparse.ArgumentParser(description="Run edge case tests for ImgCipherF1")
    parser.add_argument("--url", default=DEFAULT_URL, help=f"Base URL of the application (default: {DEFAULT_URL})")
    parser.add_argument("--output", default="./test_files", help="Directory to store test files")
    args = parser.parse_args()

    # Create test files
    test_files = create_test_files(args.output)

    # Login as registered user
    session = login(args.url, TEST_USER)
    if not session:
        logger.error("Cannot proceed with tests without login")
        return 1

    # Run tests
    tests = [
        ("Large image conversion", lambda: test_large_image_conversion(args.url, session, test_files)),
        ("Corrupt file handling", lambda: test_corrupt_file_handling(args.url, session, test_files)),
        ("Concurrent requests", lambda: test_concurrent_requests(args.url, session, test_files)),
        ("Rate limiting", lambda: test_rate_limiting(args.url)),
    ]

    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
            logger.info(f"Test '{name}': {'PASS' if result else 'FAIL'}")
        except Exception as e:
            logger.error(f"Error running test '{name}': {str(e)}")
            results.append((name, False))

    # Summary
    logger.info("\n--- Test Summary ---")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    logger.info(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")

    for name, result in results:
        logger.info(f"{name}: {'PASS' if result else 'FAIL'}")

    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
