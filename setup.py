#!/usr/bin/env python3
"""
ImgCipherF1 Setup Script

This script automates the setup process for the ImgCipherF1 application.
It handles virtual environment creation, dependency installation, database setup,
and initial configuration across different platforms.

Usage:
    python setup.py [options]

Options:
    --dev           Setup for development (includes dev dependencies)
    --prod          Setup for production
    --docker        Setup for Docker environment
    --skip-venv     Skip virtual environment creation
    --skip-deps     Skip dependency installation
    --skip-db       Skip database initialization
    --force         Force overwrite existing files
    --help          Show this help message

Examples:
    python setup.py --dev                    # Development setup
    python setup.py --prod                   # Production setup
    python setup.py --docker                 # Docker setup
    python setup.py --dev --skip-venv        # Dev setup without venv
"""

import os
import sys
import subprocess
import platform
import shutil
import argparse
import secrets
from pathlib import Path
from typing import List, Optional, Dict, Any


class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


class SetupError(Exception):
    """Custom exception for setup errors."""
    pass


class ImgCipherF1Setup:
    """Main setup class for ImgCipherF1 application."""
    
    def __init__(self, args: argparse.Namespace):
        self.args = args
        self.project_root = Path(__file__).parent.absolute()
        self.venv_path = self.project_root / "venv"
        self.is_windows = platform.system() == "Windows"
        self.python_executable = self._get_python_executable()
        
        # Environment-specific configurations
        self.env_config = {
            'dev': {
                'flask_env': 'development',
                'debug': 'true',
                'requirements_file': 'requirements.txt'
            },
            'prod': {
                'flask_env': 'production',
                'debug': 'false',
                'requirements_file': 'requirements.txt'
            },
            'docker': {
                'flask_env': 'production',
                'debug': 'false',
                'requirements_file': 'requirements.txt'
            }
        }
    
    def _get_python_executable(self) -> str:
        """Get the appropriate Python executable."""
        if self.args.skip_venv:
            return sys.executable
        
        if self.is_windows:
            return str(self.venv_path / "Scripts" / "python.exe")
        else:
            return str(self.venv_path / "bin" / "python")
    
    def _get_pip_executable(self) -> str:
        """Get the appropriate pip executable."""
        if self.args.skip_venv:
            return "pip"
        
        if self.is_windows:
            return str(self.venv_path / "Scripts" / "pip.exe")
        else:
            return str(self.venv_path / "bin" / "pip")
    
    def _run_command(self, command: List[str], cwd: Optional[Path] = None, 
                    check: bool = True, capture_output: bool = False) -> subprocess.CompletedProcess:
        """Run a command with proper error handling."""
        try:
            self.print_info(f"Running: {' '.join(command)}")
            result = subprocess.run(
                command,
                cwd=cwd or self.project_root,
                check=check,
                capture_output=capture_output,
                text=True
            )
            return result
        except subprocess.CalledProcessError as e:
            self.print_error(f"Command failed: {' '.join(command)}")
            self.print_error(f"Error: {e}")
            if capture_output and e.stdout:
                self.print_error(f"Stdout: {e.stdout}")
            if capture_output and e.stderr:
                self.print_error(f"Stderr: {e.stderr}")
            raise SetupError(f"Command failed: {' '.join(command)}")
    
    def print_header(self, message: str):
        """Print a header message."""
        print(f"\n{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}")
        print(f"{Colors.HEADER}{Colors.BOLD}{message.center(60)}{Colors.ENDC}")
        print(f"{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}\n")
    
    def print_success(self, message: str):
        """Print a success message."""
        print(f"{Colors.OKGREEN}✓ {message}{Colors.ENDC}")
    
    def print_info(self, message: str):
        """Print an info message."""
        print(f"{Colors.OKBLUE}ℹ {message}{Colors.ENDC}")
    
    def print_warning(self, message: str):
        """Print a warning message."""
        print(f"{Colors.WARNING}⚠ {message}{Colors.ENDC}")
    
    def print_error(self, message: str):
        """Print an error message."""
        print(f"{Colors.FAIL}✗ {message}{Colors.ENDC}")
    
    def check_prerequisites(self):
        """Check system prerequisites."""
        self.print_header("Checking Prerequisites")
        
        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 9):
            raise SetupError(f"Python 3.9+ required, found {python_version.major}.{python_version.minor}")
        self.print_success(f"Python {python_version.major}.{python_version.minor}.{python_version.micro} found")
        
        # Check for required system packages
        required_packages = []
        
        if not self.args.docker:
            # Check for Tesseract OCR
            try:
                result = subprocess.run(['tesseract', '--version'], 
                                      capture_output=True, text=True, check=False)
                if result.returncode == 0:
                    self.print_success("Tesseract OCR found")
                else:
                    required_packages.append("tesseract-ocr")
            except FileNotFoundError:
                required_packages.append("tesseract-ocr")
            
            # Check for Redis (optional but recommended)
            try:
                result = subprocess.run(['redis-cli', '--version'], 
                                      capture_output=True, text=True, check=False)
                if result.returncode == 0:
                    self.print_success("Redis found")
                else:
                    self.print_warning("Redis not found (optional for caching)")
            except FileNotFoundError:
                self.print_warning("Redis not found (optional for caching)")
        
        if required_packages:
            self.print_warning("Missing required packages:")
            for package in required_packages:
                print(f"  - {package}")
            
            if self.is_windows:
                self.print_info("Install Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki")
            else:
                self.print_info("Install missing packages:")
                if platform.system() == "Darwin":  # macOS
                    print("  brew install tesseract")
                else:  # Linux
                    print("  sudo apt-get install tesseract-ocr")
                    print("  # or")
                    print("  sudo yum install tesseract")
    
    def create_virtual_environment(self):
        """Create a virtual environment."""
        if self.args.skip_venv:
            self.print_info("Skipping virtual environment creation")
            return
        
        self.print_header("Creating Virtual Environment")
        
        if self.venv_path.exists():
            if self.args.force:
                self.print_warning("Removing existing virtual environment")
                shutil.rmtree(self.venv_path)
            else:
                self.print_success("Virtual environment already exists")
                return
        
        self.print_info(f"Creating virtual environment at {self.venv_path}")
        self._run_command([sys.executable, "-m", "venv", str(self.venv_path)])
        self.print_success("Virtual environment created")

    def install_dependencies(self):
        """Install Python dependencies."""
        if self.args.skip_deps:
            self.print_info("Skipping dependency installation")
            return

        self.print_header("Installing Dependencies")

        # Determine environment
        env_type = 'dev' if self.args.dev else ('docker' if self.args.docker else 'prod')
        config = self.env_config[env_type]

        # Upgrade pip first
        self.print_info("Upgrading pip")
        self._run_command([self._get_pip_executable(), "install", "--upgrade", "pip"])

        # Install requirements
        requirements_file = self.project_root / config['requirements_file']
        if requirements_file.exists():
            self.print_info(f"Installing requirements from {requirements_file}")
            self._run_command([
                self._get_pip_executable(), "install", "-r", str(requirements_file)
            ])
        else:
            self.print_warning(f"Requirements file not found: {requirements_file}")

        # Install development dependencies if in dev mode
        if self.args.dev:
            dev_requirements = self.project_root / "requirements-dev.txt"
            if dev_requirements.exists():
                self.print_info("Installing development dependencies")
                self._run_command([
                    self._get_pip_executable(), "install", "-r", str(dev_requirements)
                ])
            else:
                # Install common dev dependencies
                dev_packages = [
                    "pytest>=8.0.0",
                    "pytest-cov>=4.0.0",
                    "black>=23.0.0",
                    "flake8>=6.0.0",
                    "isort>=5.12.0"
                ]
                self.print_info("Installing common development packages")
                self._run_command([
                    self._get_pip_executable(), "install"
                ] + dev_packages)

        self.print_success("Dependencies installed successfully")

    def create_environment_file(self):
        """Create .env file with default configuration."""
        self.print_header("Creating Environment Configuration")

        env_file = self.project_root / ".env"

        if env_file.exists() and not self.args.force:
            self.print_success(".env file already exists")
            return

        # Determine environment type
        env_type = 'dev' if self.args.dev else ('docker' if self.args.docker else 'prod')
        config = self.env_config[env_type]

        # Generate secret key
        secret_key = secrets.token_hex(32)

        # Create .env content
        env_content = f"""# ImgCipherF1 Environment Configuration
# Generated by setup script

# Flask Configuration
FLASK_APP=run.py
FLASK_ENV={config['flask_env']}
FLASK_DEBUG={config['debug']}

# Security
SECRET_KEY={secret_key}

# Database Configuration
DATABASE_URL=sqlite:///instance/app.db

# File Storage
UPLOAD_FOLDER=data/uploads
OUTPUT_FOLDER=data/outputs
MAX_CONTENT_LENGTH=16777216

# Celery Configuration (Redis)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Caching Configuration
CACHE_TYPE=RedisCache
CACHE_REDIS_URL=redis://localhost:6379/1
CACHE_DEFAULT_TIMEOUT=300

# Logging
LOG_FOLDER=logs
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# Rate Limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/2

# Production-specific settings (uncomment for production)
# DATABASE_URL=postgresql://user:password@localhost/imgcipherf1
# REDIS_URL=redis://localhost:6379/0
"""

        # Docker-specific adjustments
        if self.args.docker:
            env_content = env_content.replace("localhost", "redis")
            env_content = env_content.replace("sqlite:///instance/app.db", "sqlite:///instance/app.db")

        # Write .env file
        with open(env_file, 'w') as f:
            f.write(env_content)

        self.print_success(f".env file created at {env_file}")

        if not self.args.docker:
            self.print_warning("Remember to:")
            self.print_warning("1. Install and start Redis for caching and background tasks")
            self.print_warning("2. Update DATABASE_URL for production use")
            self.print_warning("3. Review and customize other settings as needed")

    def create_directories(self):
        """Create necessary directories."""
        self.print_header("Creating Directories")

        directories = [
            "data/uploads",
            "data/outputs",
            "logs",
            "instance",
            "migrations"
        ]

        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            self.print_success(f"Created directory: {directory}")

    def initialize_database(self):
        """Initialize the database."""
        if self.args.skip_db:
            self.print_info("Skipping database initialization")
            return

        self.print_header("Initializing Database")

        # Set environment variables for Flask
        env = os.environ.copy()
        env['FLASK_APP'] = 'run.py'

        try:
            # Check if migrations directory exists
            migrations_dir = self.project_root / "migrations"
            if not migrations_dir.exists():
                self.print_info("Initializing Flask-Migrate")
                self._run_command([
                    self._get_python_executable(), "-m", "flask", "db", "init"
                ])

            # Create initial migration if none exist
            versions_dir = migrations_dir / "versions"
            if not versions_dir.exists() or not list(versions_dir.glob("*.py")):
                self.print_info("Creating initial migration")
                self._run_command([
                    self._get_python_executable(), "-m", "flask", "db", "migrate",
                    "-m", "Initial migration"
                ])

            # Apply migrations
            self.print_info("Applying database migrations")
            self._run_command([
                self._get_python_executable(), "-m", "flask", "db", "upgrade"
            ])

            self.print_success("Database initialized successfully")

        except SetupError:
            self.print_warning("Database initialization failed")
            self.print_info("You may need to run database setup manually:")
            self.print_info("  flask db init")
            self.print_info("  flask db migrate -m 'Initial migration'")
            self.print_info("  flask db upgrade")

    def create_admin_user(self):
        """Create an admin user."""
        if self.args.docker:
            self.print_info("Skipping admin user creation for Docker setup")
            return

        self.print_header("Creating Admin User")

        try:
            # Check if admin user already exists
            result = self._run_command([
                self._get_python_executable(), "-c",
                "from app import create_app; from app.models.auth import User; "
                "app, _ = create_app(); "
                "with app.app_context(): "
                "    admin = User.query.filter_by(username='admin').first(); "
                "    print('exists' if admin else 'not_found')"
            ], capture_output=True)

            if "exists" in result.stdout:
                self.print_success("Admin user already exists")
                return

            # Create admin user
            admin_password = secrets.token_urlsafe(16)
            self.print_info("Creating admin user")

            self._run_command([
                self._get_python_executable(), "-m", "flask", "create-admin",
                "--username", "admin",
                "--email", "<EMAIL>",
                "--password", admin_password
            ])

            self.print_success("Admin user created successfully")
            self.print_warning(f"Admin credentials:")
            self.print_warning(f"  Username: admin")
            self.print_warning(f"  Password: {admin_password}")
            self.print_warning("Please change the password after first login!")

        except SetupError:
            self.print_warning("Admin user creation failed")
            self.print_info("You can create an admin user manually:")
            self.print_info("  flask create-admin --username admin --email <EMAIL> --password yourpassword")

    def run_tests(self):
        """Run tests to verify setup."""
        if not self.args.dev:
            return

        self.print_header("Running Tests")

        try:
            # Run basic import test
            self.print_info("Testing application import")
            self._run_command([
                self._get_python_executable(), "-c",
                "from app import create_app; app, celery = create_app(); print('✓ Application imports successfully')"
            ])

            # Run pytest if available
            try:
                self.print_info("Running test suite")
                self._run_command([
                    self._get_python_executable(), "-m", "pytest",
                    "tests/", "-v", "--tb=short", "--maxfail=5"
                ])
                self.print_success("All tests passed")
            except SetupError:
                self.print_warning("Some tests failed, but setup is complete")

        except SetupError:
            self.print_error("Application import test failed")
            raise

    def print_completion_message(self):
        """Print setup completion message."""
        self.print_header("Setup Complete!")

        env_type = 'development' if self.args.dev else ('docker' if self.args.docker else 'production')

        print(f"{Colors.OKGREEN}ImgCipherF1 has been set up for {env_type} environment!{Colors.ENDC}\n")

        print(f"{Colors.BOLD}Next steps:{Colors.ENDC}")

        if not self.args.skip_venv and not self.args.docker:
            if self.is_windows:
                print(f"1. Activate virtual environment: {Colors.OKCYAN}venv\\Scripts\\activate{Colors.ENDC}")
            else:
                print(f"1. Activate virtual environment: {Colors.OKCYAN}source venv/bin/activate{Colors.ENDC}")

        if not self.args.docker:
            print(f"2. Start Redis server: {Colors.OKCYAN}redis-server{Colors.ENDC}")
            print(f"3. Start Celery worker: {Colors.OKCYAN}celery -A app.celery worker --loglevel=info{Colors.ENDC}")
            print(f"4. Run the application: {Colors.OKCYAN}python run.py{Colors.ENDC}")
            print(f"5. Open browser to: {Colors.OKCYAN}http://localhost:5000{Colors.ENDC}")
        else:
            print(f"2. Build and run with Docker: {Colors.OKCYAN}docker-compose up -d{Colors.ENDC}")
            print(f"3. Open browser to: {Colors.OKCYAN}http://localhost:5000{Colors.ENDC}")

        print(f"\n{Colors.BOLD}Useful commands:{Colors.ENDC}")
        print(f"- Run tests: {Colors.OKCYAN}pytest{Colors.ENDC}")
        print(f"- Check code style: {Colors.OKCYAN}flake8 .{Colors.ENDC}")
        print(f"- Format code: {Colors.OKCYAN}black .{Colors.ENDC}")
        print(f"- Database migrations: {Colors.OKCYAN}flask db migrate{Colors.ENDC}")
        print(f"- Create admin user: {Colors.OKCYAN}flask create-admin{Colors.ENDC}")

        print(f"\n{Colors.BOLD}Documentation:{Colors.ENDC}")
        print(f"- README.md - Setup and usage instructions")
        print(f"- docs/API.md - API documentation")
        print(f"- docs/deployment.md - Deployment guide")

        if self.args.dev and hasattr(self, '_admin_password'):
            print(f"\n{Colors.WARNING}Don't forget to change the admin password!{Colors.ENDC}")

    def run_setup(self):
        """Run the complete setup process."""
        try:
            self.print_header("ImgCipherF1 Setup")
            self.print_info(f"Setting up for: {'development' if self.args.dev else ('docker' if self.args.docker else 'production')}")
            self.print_info(f"Project root: {self.project_root}")

            self.check_prerequisites()
            self.create_virtual_environment()
            self.install_dependencies()
            self.create_directories()
            self.create_environment_file()
            self.initialize_database()
            self.create_admin_user()

            if self.args.dev:
                self.run_tests()

            self.print_completion_message()

        except SetupError as e:
            self.print_error(f"Setup failed: {e}")
            sys.exit(1)
        except KeyboardInterrupt:
            self.print_error("Setup interrupted by user")
            sys.exit(1)
        except Exception as e:
            self.print_error(f"Unexpected error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="ImgCipherF1 Setup Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python setup.py --dev                    # Development setup
  python setup.py --prod                   # Production setup
  python setup.py --docker                 # Docker setup
  python setup.py --dev --skip-venv        # Dev setup without venv
  python setup.py --force                  # Force overwrite existing files
        """
    )

    # Environment options (mutually exclusive)
    env_group = parser.add_mutually_exclusive_group()
    env_group.add_argument('--dev', action='store_true',
                          help='Setup for development environment')
    env_group.add_argument('--prod', action='store_true',
                          help='Setup for production environment')
    env_group.add_argument('--docker', action='store_true',
                          help='Setup for Docker environment')

    # Skip options
    parser.add_argument('--skip-venv', action='store_true',
                       help='Skip virtual environment creation')
    parser.add_argument('--skip-deps', action='store_true',
                       help='Skip dependency installation')
    parser.add_argument('--skip-db', action='store_true',
                       help='Skip database initialization')

    # Other options
    parser.add_argument('--force', action='store_true',
                       help='Force overwrite existing files')

    args = parser.parse_args()

    # Default to development if no environment specified
    if not (args.dev or args.prod or args.docker):
        args.dev = True

    # Run setup
    setup = ImgCipherF1Setup(args)
    setup.run_setup()


if __name__ == "__main__":
    main()
