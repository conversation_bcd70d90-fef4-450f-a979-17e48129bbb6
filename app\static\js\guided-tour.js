/**
 * Guided Tour Functionality
 * Provides first-time user onboarding with step-by-step tour overlays
 */

class GuidedTour {
  constructor(options = {}) {
    // Default configuration
    this.config = {
      storageKey: 'guided-tour-completed',
      steps: [],
      onComplete: () => {},
      onSkip: () => {},
      showSkip: true,
      ...options
    };

    // State
    this.currentStepIndex = 0;
    this.isActive = false;
    this.overlay = null;
    this.tooltipElement = null;

    // Bind methods
    this.start = this.start.bind(this);
    this.next = this.next.bind(this);
    this.prev = this.prev.bind(this);
    this.skip = this.skip.bind(this);
    this.complete = this.complete.bind(this);
    this.createOverlay = this.createOverlay.bind(this);
    this.createTooltip = this.createTooltip.bind(this);
    this.positionTooltip = this.positionTooltip.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.cleanup = this.cleanup.bind(this);
    this.hasCompletedTour = this.hasCompletedTour.bind(this);
    this.markTourAsCompleted = this.markTourAsCompleted.bind(this);
  }

  /**
   * Check if the user has completed the tour
   * @returns {boolean} Whether the tour has been completed
   */
  hasCompletedTour() {
    return localStorage.getItem(this.config.storageKey) === 'true';
  }

  /**
   * Mark the tour as completed
   */
  markTourAsCompleted() {
    localStorage.setItem(this.config.storageKey, 'true');
  }

  /**
   * Start the guided tour
   */
  start() {
    if (this.isActive || this.config.steps.length === 0) return;

    this.isActive = true;
    this.currentStepIndex = 0;

    // Create overlay and tooltip
    this.createOverlay();
    this.showCurrentStep();

    // Add keyboard navigation
    document.addEventListener('keydown', this.handleKeyDown);
  }

  /**
   * Show the current step
   */
  showCurrentStep() {
    const step = this.config.steps[this.currentStepIndex];
    if (!step) return;

    // Find target element
    const targetElement = document.querySelector(step.target);
    if (!targetElement) {
      console.error(`Target element not found: ${step.target}`);
      this.next();
      return;
    }

    // Create tooltip
    this.createTooltip(step, targetElement);
  }

  /**
   * Create the overlay element
   */
  createOverlay() {
    // Remove existing overlay if any
    if (this.overlay) {
      this.overlay.remove();
    }

    // Create new overlay
    this.overlay = document.createElement('div');
    this.overlay.className = 'guided-tour-overlay';
    this.overlay.setAttribute('role', 'dialog');
    this.overlay.setAttribute('aria-modal', 'true');
    this.overlay.setAttribute('aria-label', 'Guided Tour');
    document.body.appendChild(this.overlay);
  }

  /**
   * Create the tooltip element
   * @param {Object} step The current step configuration
   * @param {HTMLElement} targetElement The target element to highlight
   */
  createTooltip(step, targetElement) {
    // Remove existing tooltip if any
    if (this.tooltipElement) {
      this.tooltipElement.remove();
    }

    // Create tooltip container
    this.tooltipElement = document.createElement('div');
    this.tooltipElement.className = 'guided-tour-tooltip';
    this.tooltipElement.setAttribute('role', 'tooltip');

    // Add title
    if (step.title) {
      const titleElement = document.createElement('h3');
      titleElement.className = 'guided-tour-title';
      titleElement.textContent = step.title;
      this.tooltipElement.appendChild(titleElement);
    }

    // Add content
    const contentElement = document.createElement('div');
    contentElement.className = 'guided-tour-content';
    contentElement.innerHTML = step.content;
    this.tooltipElement.appendChild(contentElement);

    // Add navigation buttons
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'guided-tour-buttons';

    // Previous button (if not first step)
    if (this.currentStepIndex > 0) {
      const prevButton = document.createElement('button');
      prevButton.className = 'guided-tour-button guided-tour-prev';
      prevButton.textContent = 'Previous';
      prevButton.setAttribute('aria-label', 'Go to previous step');
      prevButton.addEventListener('click', this.prev);
      buttonsContainer.appendChild(prevButton);
    }

    // Skip button (if enabled)
    if (this.config.showSkip) {
      const skipButton = document.createElement('button');
      skipButton.className = 'guided-tour-button guided-tour-skip';
      skipButton.textContent = 'Skip Tour';
      skipButton.setAttribute('aria-label', 'Skip the tour');
      skipButton.addEventListener('click', this.skip);
      buttonsContainer.appendChild(skipButton);
    }

    // Next/Finish button
    const isLastStep = this.currentStepIndex === this.config.steps.length - 1;
    const nextButton = document.createElement('button');
    nextButton.className = 'guided-tour-button guided-tour-next';
    nextButton.textContent = isLastStep ? 'Finish' : 'Next';
    nextButton.setAttribute('aria-label', isLastStep ? 'Finish the tour' : 'Go to next step');
    nextButton.addEventListener('click', isLastStep ? this.complete : this.next);
    buttonsContainer.appendChild(nextButton);

    this.tooltipElement.appendChild(buttonsContainer);

    // Add step indicator
    const stepIndicator = document.createElement('div');
    stepIndicator.className = 'guided-tour-step-indicator';
    stepIndicator.textContent = `Step ${this.currentStepIndex + 1} of ${this.config.steps.length}`;
    stepIndicator.setAttribute('aria-live', 'polite');
    this.tooltipElement.appendChild(stepIndicator);

    // Add to DOM
    document.body.appendChild(this.tooltipElement);

    // Position tooltip
    this.positionTooltip(targetElement);

    // Highlight target element
    targetElement.classList.add('guided-tour-highlight');

    // Set focus to the tooltip for accessibility
    this.tooltipElement.setAttribute('tabindex', '-1');
    this.tooltipElement.focus();
  }

  /**
   * Position the tooltip relative to the target element
   * @param {HTMLElement} targetElement The target element
   */
  positionTooltip(targetElement) {
    if (!this.tooltipElement || !targetElement) return;

    const targetRect = targetElement.getBoundingClientRect();
    const tooltipRect = this.tooltipElement.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;

    // Default position (below target)
    let top = targetRect.bottom + 10;
    let left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);

    // Check if tooltip would go off-screen and adjust
    if (top + tooltipRect.height > windowHeight) {
      // Position above target
      top = targetRect.top - tooltipRect.height - 10;
    }

    if (left < 10) {
      left = 10;
    } else if (left + tooltipRect.width > windowWidth - 10) {
      left = windowWidth - tooltipRect.width - 10;
    }

    // Apply position
    this.tooltipElement.style.top = `${top}px`;
    this.tooltipElement.style.left = `${left}px`;
  }

  /**
   * Go to the next step
   */
  next() {
    // Remove highlight from current target
    const currentStep = this.config.steps[this.currentStepIndex];
    if (currentStep) {
      const currentTarget = document.querySelector(currentStep.target);
      if (currentTarget) {
        currentTarget.classList.remove('guided-tour-highlight');
      }
    }

    // Move to next step
    this.currentStepIndex++;

    // Check if tour is complete
    if (this.currentStepIndex >= this.config.steps.length) {
      this.complete();
      return;
    }

    // Show next step
    this.showCurrentStep();
  }

  /**
   * Go to the previous step
   */
  prev() {
    // Remove highlight from current target
    const currentStep = this.config.steps[this.currentStepIndex];
    if (currentStep) {
      const currentTarget = document.querySelector(currentStep.target);
      if (currentTarget) {
        currentTarget.classList.remove('guided-tour-highlight');
      }
    }

    // Move to previous step
    this.currentStepIndex--;

    // Check if we're before the first step
    if (this.currentStepIndex < 0) {
      this.currentStepIndex = 0;
    }

    // Show current step
    this.showCurrentStep();
  }

  /**
   * Skip the tour
   */
  skip() {
    this.cleanup();
    this.markTourAsCompleted();

    if (typeof this.config.onSkip === 'function') {
      this.config.onSkip();
    }
  }

  /**
   * Complete the tour
   */
  complete() {
    this.cleanup();
    this.markTourAsCompleted();

    if (typeof this.config.onComplete === 'function') {
      this.config.onComplete();
    }
  }

  /**
   * Clean up tour elements
   */
  cleanup() {
    // Remove highlight from current target
    const currentStep = this.config.steps[this.currentStepIndex];
    if (currentStep) {
      const currentTarget = document.querySelector(currentStep.target);
      if (currentTarget) {
        currentTarget.classList.remove('guided-tour-highlight');
      }
    }

    // Remove overlay and tooltip
    if (this.overlay) {
      this.overlay.remove();
      this.overlay = null;
    }

    if (this.tooltipElement) {
      this.tooltipElement.remove();
      this.tooltipElement = null;
    }

    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyDown);

    this.isActive = false;
  }

  /**
   * Handle keyboard navigation
   * @param {KeyboardEvent} event The keyboard event
   */
  handleKeyDown(event) {
    if (!this.isActive) return;

    switch (event.key) {
      case 'Escape':
        this.skip();
        break;
      case 'ArrowRight':
      case 'Enter':
        this.next();
        break;
      case 'ArrowLeft':
        this.prev();
        break;
    }
  }
}

// Initialize tours when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create tour instance for each page
  window.guidedTour = new GuidedTour();

  // Initialize page-specific tours
  initializePageTours();


});

/**
 * Initialize page-specific tours
 */
function initializePageTours() {
  const currentPath = window.location.pathname;

  // Home page tour
  if (currentPath === '/') {
    initializeHomeTour();
  }
  // Tools page tour
  else if (currentPath.startsWith('/tools')) {
    initializeToolsTour();
  }
}

/**
 * Initialize the home page tour
 */
function initializeHomeTour() {
  const tour = window.guidedTour;

  // Skip if tour already completed
  if (tour.hasCompletedTour()) return;

  tour.config.steps = [
    {
      target: 'nav',
      title: 'Welcome to ImgCipher!',
      content: 'This guided tour will help you get familiar with our platform. Let\'s start with the navigation bar.'
    },
    {
      target: '.tool-card:first-child',
      title: 'Tool Cards',
      content: 'These cards provide quick access to our most popular tools. Click on any card to start using that tool.'
    },
    {
      target: 'footer',
      title: 'Additional Resources',
      content: 'At the bottom of the page, you\'ll find links to our terms and privacy policy, as well as other helpful resources.'
    }
  ];

  // Start the tour with a slight delay to ensure page is fully loaded
  setTimeout(() => {
    tour.start();
  }, 500);
}

/**
 * Initialize the tools page tour
 */
function initializeToolsTour() {
  const tour = window.guidedTour;

  // Skip if tour already completed
  if (tour.hasCompletedTour()) return;

  // Different tours based on specific tool pages
  if (window.location.pathname.includes('convert-file')) {
    tour.config.steps = [
      {
        target: '.drop-area',
        title: 'Upload Files',
        content: 'Drag and drop your files here, or click to select files from your computer.'
      },
      {
        target: '.conversion-options',
        title: 'Conversion Options',
        content: 'Select your desired output format and any additional options for the conversion.'
      },
      {
        target: '.submit-button',
        title: 'Start Conversion',
        content: 'Click this button to start the conversion process after selecting your files and options.'
      }
    ];
  } else if (window.location.pathname.includes('pdf')) {
    tour.config.steps = [
      {
        target: '.pdf-options',
        title: 'PDF Tools',
        content: 'Here you can split, merge, or manipulate PDF files. Select the operation you want to perform.'
      },
      {
        target: '.drop-area',
        title: 'Upload PDF Files',
        content: 'Drag and drop your PDF files here, or click to select files from your computer.'
      },
      {
        target: '.submit-button',
        title: 'Process PDFs',
        content: 'Click this button to start processing your PDF files after selecting your options.'
      }
    ];
  }

  // Start the tour with a slight delay to ensure page is fully loaded
  setTimeout(() => {
    tour.start();
  }, 500);
}
