{"tests/test_tools.py::test_rate_limiting": true, "tests/test_guided_tour.py::test_guided_tour_home_page": true, "tests/test_guided_tour.py::test_guided_tour_tools_page": true, "tests/test_guided_tour.py::test_guided_tour_initialization": true, "tests/test_guided_tour.py::test_restart_tour_functionality": true, "tests/test_tools.py::test_convert_file_registered_size_limit": true, "tests/test_contextual_help.py::test_contextual_help_js_included": true, "tests/test_contextual_help.py::test_tooltips_exist": true, "tests/test_contextual_help.py::test_help_panel_exists": true, "tests/test_contextual_help.py::test_help_toggle_button_exists": true, "tests/test_guided_tour.py::test_guided_tour_css_included": true, "tests/test_guided_tour.py::test_guided_tour_js_included": true, "tests/test_guided_tour.py::test_restart_tour_button_exists": true, "tests/test_guided_tour.py::test_guided_tour_script_loaded": true, "tests/test_theme_toggle.py::test_theme_toggle_button_exists": true, "tests/test_theme_toggle.py::test_theme_css_included": true, "tests/test_theme_toggle.py::test_theme_toggle_js_included": true, "tests/test_theme_toggle.py::test_html_has_data_theme_attribute": true, "tests/test_tools.py::test_convert_file_page_get": true, "tests/test_tools.py::test_convert_file_invalid_file": true, "tests/test_tools.py::test_convert_file_guest_size_limit": true, "tests/test_tools.py::test_rate_limiting_simulation": true, "tests/test_tools_routes.py::test_split_pdf_page": true, "tests/test_tools_routes.py::test_merge_pdf_page": true, "tests/test_tools_routes.py::test_privacy_page": true, "tests/test_tools_routes.py::test_terms_page": true, "tests/test_user_model.py::test_create_role": true, "tests/test_user_model.py::test_create_user": true, "tests/test_user_model.py::test_user_roles": true, "tests/test_user_model.py::test_user_status": true, "tests/test_user_model.py::test_user_api_key": true, "tests/test_api.py::test_vibes_endpoint": true, "tests/test_api.py::test_confession_endpoint": true, "tests/test_api.py::test_poll_votes_endpoint": true, "tests/test_auth.py::test_login_page": true, "tests/test_auth.py::test_register_page": true}