"""
Tests for User and Role models.
"""

# flake8: noqa
import pytest
from app.models.auth import User, Role, UserStatus
from app.extensions import db


def test_create_role(app):
    """Test creating a new role."""
    with app.app_context():
        role = Role(name="test_role", description="Test role description")
        db.session.add(role)
        db.session.commit()

        retrieved_role = Role.query.filter_by(name="test_role").first()
        assert retrieved_role is not None
        assert retrieved_role.name == "test_role"
        assert retrieved_role.description == "Test role description"


def test_create_user(app):
    """Test creating a new user."""
    with app.app_context():
        user = User(
            username="testuser",
            email="<EMAIL>",
        )
        user.password = "password123"
        db.session.add(user)
        db.session.commit()

        retrieved_user = User.query.filter_by(username="testuser").first()
        assert retrieved_user is not None
        assert retrieved_user.username == "testuser"
        assert retrieved_user.email == "<EMAIL>"
        assert retrieved_user.verify_password("password123")
        assert not retrieved_user.verify_password("wrongpassword")


def test_user_roles(app):
    """Test adding and removing roles from a user."""
    with app.app_context():
        # Create roles
        role1 = Role(name="role1", description="Role 1")
        role2 = Role(name="role2", description="Role 2")
        db.session.add_all([role1, role2])
        db.session.commit()

        # Create user
        user = User(
            username="roleuser",
            email="<EMAIL>",
        )
        user.password = "password123"
        db.session.add(user)
        db.session.commit()

        # Add roles to user
        user.add_role(role1)
        user.add_role("role2")  # Test adding by name
        db.session.commit()

        # Retrieve user and check roles
        retrieved_user = User.query.filter_by(username="roleuser").first()
        assert len(retrieved_user.roles) == 2
        assert retrieved_user.has_role("role1")
        assert retrieved_user.has_role("role2")

        # Remove a role
        user.remove_role(role1)
        db.session.commit()

        # Check roles again
        retrieved_user = User.query.filter_by(username="roleuser").first()
        assert len(retrieved_user.roles) == 1
        assert not retrieved_user.has_role("role1")
        assert retrieved_user.has_role("role2")


def test_user_status(app):
    """Test user status enum."""
    with app.app_context():
        user = User(username="statususer", email="<EMAIL>", status=UserStatus.ACTIVE)
        user.password = "password123"
        db.session.add(user)
        db.session.commit()

        retrieved_user = User.query.filter_by(username="statususer").first()
        assert retrieved_user.status == UserStatus.ACTIVE

        # Change status
        retrieved_user.status = UserStatus.SUSPENDED
        db.session.commit()

        # Check status again
        retrieved_user = User.query.filter_by(username="statususer").first()
        assert retrieved_user.status == UserStatus.SUSPENDED


def test_user_api_key(app):
    """Test generating API key for a user."""
    with app.app_context():
        user = User(
            username="apiuser",
            email="<EMAIL>",
        )
        user.password = "password123"

        # Generate API key
        api_key = user.generate_api_key()
        assert api_key is not None
        assert len(api_key) == 64  # 32 bytes = 64 hex chars

        db.session.add(user)
        db.session.commit()

        # Retrieve user and check API key
        retrieved_user = User.query.filter_by(username="apiuser").first()
        assert retrieved_user.api_key == api_key
