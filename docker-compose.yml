version: '3.8'

services:
  web:
    build: .
    restart: always
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./instance:/app/instance
    depends_on:
      - redis
    environment:
      - FLASK_APP=run.py
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-default-secret-key-change-in-production}
      - DATABASE_URL=sqlite:///instance/app.db
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CACHE_REDIS_URL=redis://redis:6379/1

  celery:
    build: .
    restart: always
    command: celery -A app.celery worker --loglevel=info
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./instance:/app/instance
    depends_on:
      - redis
    environment:
      - FLASK_APP=run.py
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-default-secret-key-change-in-production}
      - DATABASE_URL=sqlite:///instance/app.db
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CACHE_REDIS_URL=redis://redis:6379/1

  redis:
    image: redis:alpine
    restart: always
    volumes:
      - redis_data:/data

volumes:
  redis_data:
