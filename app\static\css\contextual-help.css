/**
 * Contextual Help CSS
 * Styles for tooltips and help panels
 */

/* Tooltip container */
.contextual-tooltip {
  position: absolute;
  z-index: 9000;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  max-width: 250px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.2s ease, transform 0.2s ease;
  border: 1px solid var(--border-primary);
}

/* Active tooltip */
.contextual-tooltip.active {
  opacity: 1;
  transform: translateY(0);
}

/* Tooltip arrow */
.contextual-tooltip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

/* Tooltip position variants */
.contextual-tooltip.position-top::before {
  bottom: -6px;
  left: 50%;
  margin-left: -6px;
  border-width: 6px 6px 0 6px;
  border-color: var(--bg-secondary) transparent transparent transparent;
}

.contextual-tooltip.position-bottom::before {
  top: -6px;
  left: 50%;
  margin-left: -6px;
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent var(--bg-secondary) transparent;
}

.contextual-tooltip.position-left::before {
  right: -6px;
  top: 50%;
  margin-top: -6px;
  border-width: 6px 0 6px 6px;
  border-color: transparent transparent transparent var(--bg-secondary);
}

.contextual-tooltip.position-right::before {
  left: -6px;
  top: 50%;
  margin-top: -6px;
  border-width: 6px 6px 6px 0;
  border-color: transparent var(--bg-secondary) transparent transparent;
}

/* Help panel container */
.contextual-help-panel {
  position: fixed;
  z-index: 8000;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 1.5rem;
  max-width: 400px;
  width: calc(100% - 40px);
  max-height: 80vh;
  overflow-y: auto;
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.3s ease, transform 0.3s ease;
  border: 1px solid var(--border-primary);
  display: none;
}

/* Active help panel */
.contextual-help-panel.active {
  opacity: 1;
  transform: scale(1);
  display: block;
}

/* Help panel header */
.contextual-help-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-primary);
}

.contextual-help-panel-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.contextual-help-panel-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 0.25rem;
  font-size: 1.25rem;
  line-height: 1;
  transition: color 0.2s ease;
}

.contextual-help-panel-close:hover {
  color: var(--text-primary);
}

.contextual-help-panel-close:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* Help panel content */
.contextual-help-panel-content {
  font-size: 0.9375rem;
  line-height: 1.5;
  color: var(--text-secondary);
}

.contextual-help-panel-content h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 1.25rem 0 0.5rem;
  color: var(--text-primary);
}

.contextual-help-panel-content p {
  margin: 0 0 0.75rem;
}

.contextual-help-panel-content ul,
.contextual-help-panel-content ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.contextual-help-panel-content li {
  margin-bottom: 0.5rem;
}

.contextual-help-panel-content code {
  background-color: var(--bg-tertiary);
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.875rem;
}

/* Help toggle button */
.help-toggle-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
  margin-left: 0.5rem;
}

.help-toggle-button:hover {
  background-color: var(--bg-accent);
  color: white;
}

.help-toggle-button:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* Tooltip trigger styling */
[data-tooltip] {
  position: relative;
  cursor: help;
  border-bottom: 1px dotted var(--border-secondary);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .contextual-tooltip {
    max-width: 200px;
    font-size: 0.8125rem;
    padding: 0.375rem 0.625rem;
  }
  
  .contextual-help-panel {
    max-width: calc(100% - 40px);
    padding: 1.25rem;
  }
  
  .contextual-help-panel-title {
    font-size: 1rem;
  }
  
  .contextual-help-panel-content {
    font-size: 0.875rem;
  }
}

/* Accessibility focus styles */
.contextual-help-panel:focus {
  outline: none;
}

/* High contrast mode adjustments */
@media (forced-colors: active) {
  .contextual-tooltip,
  .contextual-help-panel {
    border: 1px solid CanvasText;
  }
  
  .help-toggle-button {
    border: 1px solid CanvasText;
  }
}
