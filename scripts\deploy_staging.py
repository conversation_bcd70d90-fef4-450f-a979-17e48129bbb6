#!/usr/bin/env python3
"""
Deployment script for ImgCipherF1 staging environment.

This script automates the process of deploying the application to the staging environment.
It performs the following steps:
1. Checks that all tests pass
2. Builds the application
3. Deploys to the staging server
4. Runs smoke tests to verify the deployment

Usage:
    python deploy_staging.py [--skip-tests] [--skip-build]

Options:
    --skip-tests    <PERSON><PERSON> running tests before deployment
    --skip-build    Skip building the application (use existing build)
"""

import os
import sys
import time
import argparse
import subprocess
import requests
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union

# Configuration
STAGING_URL = "https://staging.imgcipherf1.example.com"
STAGING_API_KEY = os.environ.get("STAGING_API_KEY", "")
STAGING_SERVER = os.environ.get("STAGING_SERVER", "staging-server.example.com")
STAGING_USER = os.environ.get("STAGING_USER", "deploy")
BUILD_DIR = "build"
DIST_DIR = "dist"

# Ensure we're in the project root
project_root = Path(__file__).parent.parent.absolute()
os.chdir(project_root)


def run_command(command: List[str], cwd: Optional[str] = None) -> Tuple[int, str]:
    """Run a command and return the exit code and output."""
    try:
        result = subprocess.run(
            command,
            cwd=cwd or os.getcwd(),
            check=False,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
        )
        return result.returncode, result.stdout
    except Exception as e:
        return 1, str(e)


def check_prerequisites() -> bool:
    """Check that all prerequisites for deployment are met."""
    print("Checking prerequisites...")

    # Check if we have the required environment variables
    if not STAGING_API_KEY:
        print("Error: STAGING_API_KEY environment variable is not set")
        return False

    # Check if we have access to the staging server
    print(f"Checking access to staging server ({STAGING_SERVER})...")
    exit_code, output = run_command(["ping", "-c", "1", STAGING_SERVER])
    if exit_code != 0:
        print(f"Error: Cannot reach staging server: {output}")
        return False

    print("All prerequisites met!")
    return True


def run_tests() -> bool:
    """Run all tests to ensure the application is ready for deployment."""
    print("Running tests...")

    # Run pytest
    print("Running pytest...")
    exit_code, output = run_command(["pytest", "--maxfail=1", "--disable-warnings", "-q"])
    if exit_code != 0:
        print(f"Error: Tests failed: {output}")
        return False

    # Run flake8
    print("Running flake8...")
    exit_code, output = run_command(["flake8", "."])
    if exit_code != 0:
        print(f"Error: Flake8 check failed: {output}")
        return False

    # Run black
    print("Running black...")
    exit_code, output = run_command(["black", "--check", "."])
    if exit_code != 0:
        print(f"Error: Black check failed: {output}")
        return False

    print("All tests passed!")
    return True


def build_application() -> bool:
    """Build the application for deployment."""
    print("Building application...")

    # Create build directory
    os.makedirs(BUILD_DIR, exist_ok=True)
    os.makedirs(DIST_DIR, exist_ok=True)

    # Clean previous build
    print("Cleaning previous build...")
    for item in os.listdir(BUILD_DIR):
        path = os.path.join(BUILD_DIR, item)
        if os.path.isdir(path):
            for subitem in os.listdir(path):
                os.remove(os.path.join(path, subitem))
            os.rmdir(path)
        else:
            os.remove(path)

    # Copy application files
    print("Copying application files...")
    exit_code, output = run_command(
        [
            "robocopy",
            ".",
            BUILD_DIR,
            "/E",
            "/XD",
            ".git",
            "venv",
            "__pycache__",
            "build",
            "dist",
            "/XF",
            "*.pyc",
            "*.pyo",
            "*.pyd",
            "*.so",
            "*.dll",
            "*.exe",
        ]
    )
    # robocopy returns non-zero even on success, so we check if it's < 8
    if exit_code >= 8:
        print(f"Error: Failed to copy application files: {output}")
        return False

    # Create a zip archive for deployment
    print("Creating deployment archive...")
    exit_code, output = run_command(
        [
            "powershell",
            "-Command",
            f"Compress-Archive -Path {BUILD_DIR}\\* -DestinationPath {DIST_DIR}\\imgcipherf1-staging.zip -Force",
        ]
    )
    if exit_code != 0:
        print(f"Error: Failed to create deployment archive: {output}")
        return False

    print("Application built successfully!")
    return True


def deploy_to_staging() -> bool:
    """Deploy the application to the staging environment."""
    print("Deploying to staging...")

    # Upload the deployment package
    print("Uploading deployment package...")
    exit_code, output = run_command(["scp", f"{DIST_DIR}/imgcipherf1-staging.zip", f"{STAGING_USER}@{STAGING_SERVER}:/tmp/"])
    if exit_code != 0:
        print(f"Error: Failed to upload deployment package: {output}")
        return False

    # Execute deployment on the server
    print("Executing deployment on the server...")
    deploy_command = (
        "cd /tmp && "
        "unzip -o imgcipherf1-staging.zip -d /tmp/imgcipherf1-staging && "
        "sudo systemctl stop imgcipherf1-staging && "
        "sudo rm -rf /opt/imgcipherf1-staging/* && "
        "sudo cp -r /tmp/imgcipherf1-staging/* /opt/imgcipherf1-staging/ && "
        "cd /opt/imgcipherf1-staging && "
        "sudo -u imgcipherf1 python -m venv venv && "
        "sudo -u imgcipherf1 venv/bin/pip install -r requirements.txt && "
        "sudo systemctl start imgcipherf1-staging && "
        "rm -rf /tmp/imgcipherf1-staging /tmp/imgcipherf1-staging.zip"
    )

    exit_code, output = run_command(["ssh", f"{STAGING_USER}@{STAGING_SERVER}", deploy_command])
    if exit_code != 0:
        print(f"Error: Failed to execute deployment on the server: {output}")
        return False

    print("Deployment to staging completed successfully!")
    return True


def run_smoke_tests() -> bool:
    """Run smoke tests to verify the deployment."""
    print("Running smoke tests...")

    # Wait for the application to start
    print("Waiting for the application to start...")
    max_retries = 10
    retry_interval = 5  # seconds

    for i in range(max_retries):
        try:
            response = requests.get(f"{STAGING_URL}/health")
            if response.status_code == 200 and response.json().get("status") == "ok":
                print("Application is up and running!")
                break
        except Exception:
            pass

        print(f"Waiting... ({i+1}/{max_retries})")
        time.sleep(retry_interval)
    else:
        print("Error: Application did not start within the expected time")
        return False

    # Test the main page
    try:
        response = requests.get(STAGING_URL)
        if response.status_code != 200:
            print(f"Error: Main page returned status code {response.status_code}")
            return False
    except Exception as e:
        print(f"Error: Failed to access main page: {e}")
        return False

    # Test the API
    try:
        response = requests.get(f"{STAGING_URL}/tools/api/history")
        if response.status_code != 200 or not response.json().get("success"):
            print(f"Error: API test failed with status code {response.status_code}")
            return False
    except Exception as e:
        print(f"Error: Failed to access API: {e}")
        return False

    # Test image conversion (simple test)
    try:
        with open("tests/test_data/sample.jpg", "rb") as f:
            files = {"files": ("sample.jpg", f)}
            data = {"format": "webp", "quality": "80"}
            response = requests.post(f"{STAGING_URL}/tools/convert", files=files, data=data)

            if response.status_code != 200 or not response.json().get("success"):
                print(f"Error: Image conversion test failed with status code {response.status_code}")
                return False
    except Exception as e:
        print(f"Error: Failed to test image conversion: {e}")
        return False

    print("All smoke tests passed!")
    return True


def main() -> int:
    """Main entry point for the deployment script."""
    parser = argparse.ArgumentParser(description="Deploy ImgCipherF1 to staging")
    parser.add_argument("--skip-tests", action="store_true", help="Skip running tests")
    parser.add_argument("--skip-build", action="store_true", help="Skip building the application")
    args = parser.parse_args()

    # Check prerequisites
    if not check_prerequisites():
        return 1

    # Run tests (unless skipped)
    if not args.skip_tests and not run_tests():
        return 1

    # Build the application (unless skipped)
    if not args.skip_build and not build_application():
        return 1

    # Deploy to staging
    if not deploy_to_staging():
        return 1

    # Run smoke tests
    if not run_smoke_tests():
        return 1

    print("Deployment to staging completed successfully!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
