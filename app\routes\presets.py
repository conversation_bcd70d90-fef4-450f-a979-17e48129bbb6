"""
Routes for managing presets.
"""

from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required, current_user
from app.models.preset import Preset, PresetType
from app.utils.error_responses import create_error_response
from app.decorators import roles_accepted

presets_bp = Blueprint("presets", __name__)


@presets_bp.route("/api/presets", methods=["GET"])
@login_required
def get_presets():
    """
    Get all presets for the current user.

    Query Parameters:
        type: Filter presets by type (optional)

    Returns:
        JSON response with presets
    """
    preset_type = request.args.get("type")

    try:
        presets = Preset.get_user_presets(current_user.id, preset_type)

        # Convert presets to dictionaries
        presets_data = [
            {
                "id": preset.id,
                "name": preset.name,
                "preset_type": preset.preset_type,
                "is_default": preset.is_default,
                "settings": preset.settings_dict,
                "created_at": preset.created_at.isoformat(),
                "updated_at": preset.updated_at.isoformat(),
            }
            for preset in presets
        ]

        return jsonify({"success": True, "presets": presets_data})
    except Exception as e:
        current_app.logger.error(f"Error getting presets: {str(e)}")
        return create_error_response(str(e), 500)


@presets_bp.route("/api/presets", methods=["POST"])
@login_required
@roles_accepted(["registered", "admin"])
def create_preset():
    """
    Create a new preset.

    Request Body:
        name: Name of the preset
        preset_type: Type of preset (file, image, pdf, batch, etc.)
        settings: Dictionary of preset settings
        is_default: Whether this preset should be the default (optional)

    Returns:
        JSON response with the created preset
    """
    try:
        data = request.json

        # Validate required fields
        required_fields = ["name", "preset_type", "settings"]
        for field in required_fields:
            if field not in data:
                return create_error_response(f"Missing required field: {field}", 400)

        # Validate preset type
        valid_types = [
            PresetType.FILE,
            PresetType.IMAGE,
            PresetType.PDF,
            PresetType.BATCH,
            PresetType.BACKGROUND_REMOVAL,
            PresetType.TEXT_EXTRACTION,
        ]
        if data["preset_type"] not in valid_types:
            return create_error_response(f"Invalid preset type. Must be one of: {', '.join(valid_types)}", 400)

        # Create the preset
        preset = Preset.create_preset(
            user_id=current_user.id,
            name=data["name"],
            preset_type=data["preset_type"],
            settings_dict=data["settings"],
            is_default=data.get("is_default", False),
        )

        # Return the created preset
        preset_data = {
            "id": preset.id,
            "name": preset.name,
            "preset_type": preset.preset_type,
            "is_default": preset.is_default,
            "settings": preset.settings_dict,
            "created_at": preset.created_at.isoformat(),
            "updated_at": preset.updated_at.isoformat(),
        }

        return jsonify({"success": True, "preset": preset_data})
    except Exception as e:
        current_app.logger.error(f"Error creating preset: {str(e)}")
        return create_error_response(str(e), 500)


@presets_bp.route("/api/presets/<int:preset_id>", methods=["PUT"])
@login_required
@roles_accepted(["registered", "admin"])
def update_preset(preset_id):
    """
    Update an existing preset.

    URL Parameters:
        preset_id: ID of the preset to update

    Request Body:
        name: Name of the preset (optional)
        settings: Dictionary of preset settings (optional)
        is_default: Whether this preset should be the default (optional)

    Returns:
        JSON response with the updated preset
    """
    try:
        data = request.json

        # Get the preset
        preset = Preset.query.get(preset_id)
        if not preset:
            return create_error_response("Preset not found", 404)

        # Check if the preset belongs to the current user
        if preset.user_id != current_user.id:
            return create_error_response("You do not have permission to update this preset", 403)

        # Update the preset
        preset.update(name=data.get("name"), settings_dict=data.get("settings"), is_default=data.get("is_default"))

        # Return the updated preset
        preset_data = {
            "id": preset.id,
            "name": preset.name,
            "preset_type": preset.preset_type,
            "is_default": preset.is_default,
            "settings": preset.settings_dict,
            "created_at": preset.created_at.isoformat(),
            "updated_at": preset.updated_at.isoformat(),
        }

        return jsonify({"success": True, "preset": preset_data})
    except Exception as e:
        current_app.logger.error(f"Error updating preset: {str(e)}")
        return create_error_response(str(e), 500)


@presets_bp.route("/api/presets/<int:preset_id>", methods=["DELETE"])
@login_required
@roles_accepted(["registered", "admin"])
def delete_preset(preset_id):
    """
    Delete a preset.

    URL Parameters:
        preset_id: ID of the preset to delete

    Returns:
        JSON response with success message
    """
    try:
        # Get the preset
        preset = Preset.query.get(preset_id)
        if not preset:
            return create_error_response("Preset not found", 404)

        # Check if the preset belongs to the current user
        if preset.user_id != current_user.id:
            return create_error_response("You do not have permission to delete this preset", 403)

        # Delete the preset
        preset.delete()

        return jsonify({"success": True, "message": "Preset deleted successfully"})
    except Exception as e:
        current_app.logger.error(f"Error deleting preset: {str(e)}")
        return create_error_response(str(e), 500)
