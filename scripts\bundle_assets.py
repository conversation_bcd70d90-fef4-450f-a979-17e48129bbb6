"""
Asset Bundling Script

This script bundles and minifies CSS and JavaScript files to improve performance.
It creates:
1. A critical CSS bundle with essential styles
2. A non-critical CSS bundle with the rest of the styles
3. A common JavaScript bundle with shared functionality

Usage:
    python scripts/bundle_assets.py
"""

import os
import re
import glob
import cssmin
import jsmin
import argparse
from pathlib import Path

# Paths
APP_DIR = Path(__file__).parent.parent
STATIC_DIR = APP_DIR / 'app' / 'static'
CSS_DIR = STATIC_DIR / 'css'
JS_DIR = STATIC_DIR / 'js'
DIST_DIR = STATIC_DIR / 'dist'

# Create dist directory if it doesn't exist
DIST_DIR.mkdir(exist_ok=True)

# Critical CSS files (loaded immediately)
CRITICAL_CSS = [
    'theme.css',
    'responsive.css',
]

# Non-critical CSS files (loaded asynchronously)
NON_CRITICAL_CSS = [
    'fun-zone.css',
    'drag-drop.css',
    'guided-tour.css',
    'contextual-help.css',
    'progress-indicators.css',
    'result-pages.css',
    'file-preview.css',
    'presets.css',
]

# Common JavaScript files (bundled together)
COMMON_JS = [
    'mobile-menu.js',
    'theme-toggle.js',
    'lazy-load.js',
]

def read_file(file_path):
    """Read a file and return its contents."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def write_file(file_path, content):
    """Write content to a file."""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def bundle_css(file_list, output_name):
    """Bundle and minify CSS files."""
    print(f"Bundling {len(file_list)} CSS files into {output_name}...")
    
    bundle = []
    for css_file in file_list:
        file_path = CSS_DIR / css_file
        if file_path.exists():
            content = read_file(file_path)
            # Add file name as comment for debugging
            bundle.append(f"/* {css_file} */")
            bundle.append(content)
        else:
            print(f"Warning: {css_file} not found")
    
    # Join all CSS content
    bundle_content = "\n".join(bundle)
    
    # Minify the bundle
    minified = cssmin.cssmin(bundle_content)
    
    # Write the bundle to the dist directory
    output_path = DIST_DIR / output_name
    write_file(output_path, minified)
    
    # Calculate size reduction
    original_size = len(bundle_content)
    minified_size = len(minified)
    reduction = (1 - minified_size / original_size) * 100
    
    print(f"  Original size: {original_size / 1024:.2f} KB")
    print(f"  Minified size: {minified_size / 1024:.2f} KB")
    print(f"  Reduction: {reduction:.2f}%")
    
    return output_path

def bundle_js(file_list, output_name):
    """Bundle and minify JavaScript files."""
    print(f"Bundling {len(file_list)} JavaScript files into {output_name}...")
    
    bundle = []
    for js_file in file_list:
        file_path = JS_DIR / js_file
        if file_path.exists():
            content = read_file(file_path)
            # Add file name as comment for debugging
            bundle.append(f"/* {js_file} */")
            bundle.append(content)
        else:
            print(f"Warning: {js_file} not found")
    
    # Join all JS content
    bundle_content = "\n".join(bundle)
    
    # Minify the bundle
    minified = jsmin.jsmin(bundle_content)
    
    # Write the bundle to the dist directory
    output_path = DIST_DIR / output_name
    write_file(output_path, minified)
    
    # Calculate size reduction
    original_size = len(bundle_content)
    minified_size = len(minified)
    reduction = (1 - minified_size / original_size) * 100
    
    print(f"  Original size: {original_size / 1024:.2f} KB")
    print(f"  Minified size: {minified_size / 1024:.2f} KB")
    print(f"  Reduction: {reduction:.2f}%")
    
    return output_path

def optimize_images():
    """Optimize images using Python's PIL library."""
    try:
        from PIL import Image
    except ImportError:
        print("PIL not installed. Skipping image optimization.")
        return
    
    print("Optimizing images...")
    
    # Get all image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.webp']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(str(STATIC_DIR / ext)))
    
    for image_file in image_files:
        try:
            img = Image.open(image_file)
            
            # Create optimized filename
            file_path = Path(image_file)
            optimized_path = file_path.parent / f"{file_path.stem}.opt{file_path.suffix}"
            
            # Optimize based on image format
            if file_path.suffix.lower() in ['.jpg', '.jpeg']:
                img.save(optimized_path, 'JPEG', quality=85, optimize=True)
            elif file_path.suffix.lower() == '.png':
                img.save(optimized_path, 'PNG', optimize=True)
            elif file_path.suffix.lower() == '.webp':
                img.save(optimized_path, 'WEBP', quality=85)
            
            # Get file sizes
            original_size = os.path.getsize(image_file)
            optimized_size = os.path.getsize(optimized_path)
            
            # If optimized version is smaller, replace the original
            if optimized_size < original_size:
                os.replace(optimized_path, image_file)
                reduction = (1 - optimized_size / original_size) * 100
                print(f"  Optimized {file_path.name}: {original_size / 1024:.2f} KB -> {optimized_size / 1024:.2f} KB ({reduction:.2f}% reduction)")
            else:
                # Otherwise, delete the optimized version
                os.remove(optimized_path)
                print(f"  {file_path.name} already optimized")
                
        except Exception as e:
            print(f"  Error optimizing {image_file}: {e}")

def main():
    """Main function to bundle and minify assets."""
    parser = argparse.ArgumentParser(description='Bundle and minify CSS and JavaScript files.')
    parser.add_argument('--css-only', action='store_true', help='Only bundle CSS files')
    parser.add_argument('--js-only', action='store_true', help='Only bundle JavaScript files')
    parser.add_argument('--images-only', action='store_true', help='Only optimize images')
    args = parser.parse_args()
    
    # If no specific flag is provided, do everything
    do_all = not (args.css_only or args.js_only or args.images_only)
    
    if args.css_only or do_all:
        # Bundle critical CSS
        bundle_css(CRITICAL_CSS, 'critical.min.css')
        
        # Bundle non-critical CSS
        bundle_css(NON_CRITICAL_CSS, 'non-critical.min.css')
    
    if args.js_only or do_all:
        # Bundle common JavaScript
        bundle_js(COMMON_JS, 'common.min.js')
    
    if args.images_only or do_all:
        # Optimize images
        optimize_images()
    
    print("Asset bundling complete!")

if __name__ == "__main__":
    main()
