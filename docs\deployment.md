# ImgCipherF1 Deployment Guide

This document provides instructions for deploying the ImgCipherF1 application to staging and production environments.

## Prerequisites

Before deploying, ensure you have the following:

1. Access to the target server (staging or production)
2. SSH key configured for the deployment user
3. Required environment variables set:
   - `STAGING_API_KEY` or `PRODUCTION_API_KEY`
   - `STAGING_SERVER` or `PRODUCTION_SERVER`
   - `STAGING_USER` or `PRODUCTION_USER`

## Deployment Process

### Automated Deployment to Staging

The application can be deployed to staging using the provided deployment script:

```bash
# Deploy to staging
python scripts/deploy_staging.py

# Skip tests if needed
python scripts/deploy_staging.py --skip-tests

# Skip build if you want to use an existing build
python scripts/deploy_staging.py --skip-build
```

The deployment script performs the following steps:

1. Runs tests to ensure the application is ready for deployment
2. Builds the application
3. Deploys to the staging server
4. Runs smoke tests to verify the deployment

### Manual Deployment

If you need to deploy manually, follow these steps:

#### 1. Prepare the Application

```bash
# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
venv\Scripts\activate
# On Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run tests
pytest --maxfail=1 --disable-warnings -q
flake8 .
black --check .

# Create a distribution package
mkdir -p dist
zip -r dist/imgcipherf1.zip app/ migrations/ static/ templates/ requirements.txt run.py config.py
```

#### 2. Deploy to the Server

```bash
# Copy the distribution package to the server
scp dist/imgcipherf1.zip user@server:/tmp/

# SSH into the server
ssh user@server

# Extract the package
cd /tmp
unzip imgcipherf1.zip -d imgcipherf1

# Stop the existing application
sudo systemctl stop imgcipherf1

# Copy the files to the application directory
sudo rm -rf /opt/imgcipherf1/*
sudo cp -r /tmp/imgcipherf1/* /opt/imgcipherf1/

# Set up the virtual environment
cd /opt/imgcipherf1
sudo -u imgcipherf1 python -m venv venv
sudo -u imgcipherf1 venv/bin/pip install -r requirements.txt

# Run database migrations
sudo -u imgcipherf1 venv/bin/flask db upgrade

# Start the application
sudo systemctl start imgcipherf1

# Clean up
rm -rf /tmp/imgcipherf1 /tmp/imgcipherf1.zip
```

## Server Configuration

### Systemd Service

The application is configured as a systemd service. Here's an example configuration:

```ini
[Unit]
Description=ImgCipherF1 Web Application
After=network.target

[Service]
User=imgcipherf1
Group=imgcipherf1
WorkingDirectory=/opt/imgcipherf1
ExecStart=/opt/imgcipherf1/venv/bin/gunicorn -w 4 -b 127.0.0.1:8000 run:app
Restart=always
Environment=FLASK_APP=run.py
Environment=FLASK_ENV=production
Environment=DATABASE_URL=postgresql://user:password@localhost/imgcipherf1
Environment=SECRET_KEY=your-secret-key
Environment=REDIS_URL=redis://localhost:6379/0

[Install]
WantedBy=multi-user.target
```

### Nginx Configuration

The application is served through Nginx. Here's an example configuration:

```nginx
server {
    listen 80;
    server_name staging.imgcipherf1.example.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /opt/imgcipherf1/static;
        expires 30d;
    }
    
    client_max_body_size 20M;
}
```

## Monitoring

The application provides a health check endpoint at `/health` that returns the status of the application and its dependencies. You can use this endpoint for monitoring the application.

Example response:

```json
{
  "status": "ok",
  "version": "1.0.0",
  "timestamp": **********,
  "environment": "production",
  "python": "3.10.4",
  "system": {
    "system": "Linux",
    "release": "5.4.0-1-amd64",
    "python": "3.10.4"
  },
  "services": {
    "database": "ok",
    "redis": "ok"
  }
}
```

## Rollback Procedure

If a deployment fails or causes issues, you can roll back to the previous version:

```bash
# SSH into the server
ssh user@server

# Stop the current application
sudo systemctl stop imgcipherf1

# Restore from backup
sudo rm -rf /opt/imgcipherf1/*
sudo cp -r /opt/imgcipherf1-backup/* /opt/imgcipherf1/

# Start the application
sudo systemctl start imgcipherf1
```

## Continuous Integration

The application includes GitHub Actions workflows for CI/CD:

1. **Build and Test**: Runs on every push and pull request to verify the code quality
2. **Smoke Tests**: Runs after deployment to verify the application is working correctly

You can manually trigger the smoke tests workflow from the GitHub Actions tab.

## Troubleshooting

### Common Issues

1. **Application fails to start**:
   - Check the logs: `sudo journalctl -u imgcipherf1`
   - Verify the environment variables are set correctly
   - Check the permissions on the application files

2. **Database migration fails**:
   - Check the database connection
   - Verify the database user has the necessary permissions
   - Run the migrations manually: `flask db upgrade`

3. **File uploads fail**:
   - Check the permissions on the upload directory
   - Verify the maximum file size settings in Nginx and the application

### Getting Help

If you encounter issues that you cannot resolve, contact the development <NAME_EMAIL>.

---

*Last updated: 2025-05-14*
