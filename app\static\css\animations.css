/**
 * ImgCipherF1 Animations and Micro-interactions
 * This file contains reusable animations, transitions, and micro-interactions
 * for consistent user experience across the application.
 */

/* Animation Timing Variables */
:root {
  /* Duration variables */
  --transition-fast: 150ms;
  --transition-medium: 300ms;
  --transition-slow: 500ms;
  
  /* Easing functions */
  --ease-default: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* Animation presets */
  --hover-scale: scale(1.05);
  --active-scale: scale(0.98);
  --hover-brightness: brightness(1.1);
  --active-brightness: brightness(0.9);
}

/* Reusable Animation Classes */

/* Fade In */
.fade-in {
  animation: fadeIn var(--transition-medium) var(--ease-out) forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Fade Out */
.fade-out {
  animation: fadeOut var(--transition-medium) var(--ease-in) forwards;
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Slide In From Top */
.slide-in-top {
  animation: slideInTop var(--transition-medium) var(--ease-out) forwards;
}

@keyframes slideInTop {
  from { 
    transform: translateY(-20px);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

/* Slide In From Bottom */
.slide-in-bottom {
  animation: slideInBottom var(--transition-medium) var(--ease-out) forwards;
}

@keyframes slideInBottom {
  from { 
    transform: translateY(20px);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

/* Pulse Animation */
.pulse {
  animation: pulse 2s var(--ease-in-out) infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Bounce Animation */
.bounce {
  animation: bounce 1s var(--ease-bounce) infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Spin Animation */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Shake Animation (for errors) */
.shake {
  animation: shake 0.5s var(--ease-in-out);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Button Hover/Focus Effects */
.btn-hover-effect {
  transition: 
    transform var(--transition-fast) var(--ease-out),
    filter var(--transition-fast) var(--ease-out),
    box-shadow var(--transition-fast) var(--ease-out);
}

.btn-hover-effect:hover {
  transform: var(--hover-scale);
  filter: var(--hover-brightness);
}

.btn-hover-effect:active {
  transform: var(--active-scale);
  filter: var(--active-brightness);
}

/* Card Hover Effects */
.card-hover-effect {
  transition: 
    transform var(--transition-medium) var(--ease-out),
    box-shadow var(--transition-medium) var(--ease-out);
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* Input Focus Animation */
.input-focus-effect {
  transition: border-color var(--transition-medium) var(--ease-out);
}

.input-focus-effect:focus {
  border-color: var(--border-accent);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Page Transition Effects */
.page-transition {
  animation: pageTransition var(--transition-medium) var(--ease-out);
}

@keyframes pageTransition {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tooltip Animation */
.tooltip-animation {
  transition: 
    opacity var(--transition-fast) var(--ease-out),
    transform var(--transition-fast) var(--ease-out);
  opacity: 0;
  transform: translateY(5px);
}

.tooltip-trigger:hover .tooltip-animation,
.tooltip-trigger:focus .tooltip-animation {
  opacity: 1;
  transform: translateY(0);
}

/* Dropdown Animation */
.dropdown-animation {
  transition: 
    opacity var(--transition-fast) var(--ease-out),
    transform var(--transition-fast) var(--ease-out);
  transform-origin: top center;
}

.dropdown-animation.entering {
  opacity: 0;
  transform: scaleY(0.95);
}

.dropdown-animation.entered {
  opacity: 1;
  transform: scaleY(1);
}

.dropdown-animation.exiting {
  opacity: 0;
  transform: scaleY(0.95);
}

/* Loading Indicator Animation */
.loading-dots {
  display: inline-flex;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  margin: 0 3px;
  border-radius: 50%;
  background-color: currentColor;
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loadingDots {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}
