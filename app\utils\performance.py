"""
Performance Optimization Utilities

This module provides utilities for optimizing the performance of the application.
"""

import os
import hashlib
from functools import lru_cache
from flask import current_app, request, url_for

# Cache for static file hashes
_file_hash_cache = {}


def get_file_hash(file_path):
    """
    Calculate the hash of a file for cache busting.

    Args:
        file_path (str): Path to the file relative to the static folder

    Returns:
        str: Hash of the file
    """
    # Check if hash is already in cache
    if file_path in _file_hash_cache:
        return _file_hash_cache[file_path]

    # Get the absolute path to the file
    abs_path = os.path.join(current_app.static_folder, file_path)

    # Check if file exists and is a file (not a directory)
    if not os.path.exists(abs_path) or os.path.isdir(abs_path):
        return ""

    # Calculate hash
    try:
        with open(abs_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()[:8]

        # Store hash in cache
        _file_hash_cache[file_path] = file_hash

        return file_hash
    except (<PERSON><PERSON><PERSON><PERSON>, PermissionError):
        current_app.logger.warning(f"Could not read file for hashing: {file_path}")
        return ""


def versioned_url_for(endpoint, **values):
    """
    Generate a URL with a version hash for cache busting.

    Args:
        endpoint (str): Flask endpoint
        **values: Values to pass to url_for

    Returns:
        str: URL with version hash
    """
    if endpoint == 'static':
        filename = values.get('filename', None)
        if filename and not filename.endswith('/'):
            file_hash = get_file_hash(filename)
            if file_hash:
                values['v'] = file_hash

    return url_for(endpoint, **values)


def should_load_module(module_name, page_modules=None):
    """
    Determine if a module should be loaded for the current page.

    Args:
        module_name (str): Name of the module
        page_modules (list): List of modules required for the current page

    Returns:
        bool: True if the module should be loaded, False otherwise
    """
    if page_modules is None:
        return False

    return module_name in page_modules


def get_critical_css():
    """
    Get the critical CSS for the current page.

    Returns:
        str: Path to the critical CSS file
    """
    # Check if bundled critical CSS exists
    bundled_path = 'dist/critical.min.css'
    if os.path.exists(os.path.join(current_app.static_folder, bundled_path)):
        return bundled_path

    # Fall back to individual CSS files
    return 'css/theme.css'


def get_non_critical_css():
    """
    Get the non-critical CSS for the current page.

    Returns:
        str: Path to the non-critical CSS file
    """
    # Check if bundled non-critical CSS exists
    bundled_path = 'dist/non-critical.min.css'
    if os.path.exists(os.path.join(current_app.static_folder, bundled_path)):
        return bundled_path

    # Fall back to individual CSS files
    return None


@lru_cache(maxsize=32)
def get_page_modules(page_name):
    """
    Get the modules required for a specific page.

    Args:
        page_name (str): Name of the page

    Returns:
        list: List of module names
    """
    # Define modules required for each page
    page_module_map = {
        'index': ['main'],
        'convert_files': ['convert_files', 'file-preview', 'presets'],
        'batch_convert': ['convert_files', 'file-preview', 'presets'],
        'image_converter': ['image_converter', 'file-preview', 'presets'],
        'split_pdf': ['split_pdf', 'file-preview'],
        'merge_pdf': ['merge_pdf', 'file-preview'],
        'extract_text': ['extract_text', 'file-preview'],
        'remove_background': ['remove_background', 'file-preview'],
        'batch_progress': ['progress-indicators'],
        'history': ['result-pages'],
    }

    return page_module_map.get(page_name, [])


def get_current_page_modules():
    """
    Get the modules required for the current page.

    Returns:
        list: List of module names
    """
    # Extract the page name from the endpoint
    endpoint = request.endpoint
    if endpoint:
        parts = endpoint.split('.')
        if len(parts) >= 2:
            page_name = parts[1].replace('_page', '')
            return get_page_modules(page_name)

    return []
