/**
 * Result Pages JavaScript Utilities
 * Provides functions for enhancing conversion result pages
 */

class ResultPageEnhancer {
    /**
     * Initialize a result page enhancer
     * @param {Object} options - Configuration options
     * @param {string} options.containerSelector - Selector for the result container
     * @param {string} options.toolType - Type of tool (image, pdf, data, text)
     * @param {Function} options.onReset - Callback function when user wants to convert another file
     */
    constructor(options) {
        this.options = Object.assign({
            containerSelector: '.result-container',
            toolType: 'file',
            onReset: null
        }, options);

        this.container = document.querySelector(this.options.containerSelector);
        if (!this.container) {
            console.error(`Container with selector "${this.options.containerSelector}" not found.`);
            return;
        }

        this.initEventListeners();
    }

    /**
     * Initialize event listeners
     */
    initEventListeners() {
        // Add event listeners for "Convert Another" buttons
        const resetButtons = this.container.querySelectorAll('.reset-button');
        resetButtons.forEach(button => {
            button.addEventListener('click', () => {
                if (typeof this.options.onReset === 'function') {
                    this.options.onReset();
                }
            });
        });

        // Add event listeners for download buttons to track downloads
        const downloadButtons = this.container.querySelectorAll('.download-button');
        downloadButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.trackDownload(e.currentTarget.getAttribute('data-filename'));
            });
        });

        // Add event listeners for copy buttons
        const copyButtons = this.container.querySelectorAll('.copy-button');
        copyButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const textToCopy = e.currentTarget.getAttribute('data-copy-text');
                if (textToCopy) {
                    this.copyToClipboard(textToCopy, e.currentTarget);
                }
            });
        });
    }

    /**
     * Track file download for analytics
     * @param {string} filename - Name of the downloaded file
     */
    trackDownload(filename) {
        // This could be expanded to send analytics data to the server
        console.log(`Download tracked: ${filename}`);
        
        // Show a toast notification
        this.showToast(`Download started for ${filename}`, 'success');
    }

    /**
     * Copy text to clipboard
     * @param {string} text - Text to copy
     * @param {HTMLElement} button - Button element that triggered the copy
     */
    copyToClipboard(text, button) {
        navigator.clipboard.writeText(text).then(() => {
            // Store original text
            const originalText = button.textContent;
            
            // Update button text
            button.textContent = 'Copied!';
            
            // Reset after 2 seconds
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
            
            this.showToast('Copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy text: ', err);
            this.showToast('Failed to copy to clipboard', 'error');
        });
    }

    /**
     * Show a toast notification
     * @param {string} message - Message to display
     * @param {string} type - Type of toast (success, error)
     */
    showToast(message, type = 'success') {
        // Check if toast container exists, create if not
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'fixed bottom-4 right-4 z-50 flex flex-col gap-2';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `px-4 py-2 rounded-lg shadow-lg flex items-center ${
            type === 'error' ? 'bg-red-600 text-white' : 'bg-green-600 text-white'
        } transform transition-all duration-300 translate-y-0 opacity-100`;
        
        // Add icon based on type
        const iconPath = type === 'error' 
            ? 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
            : 'M5 13l4 4L19 7';
        
        toast.innerHTML = `
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${iconPath}"></path>
            </svg>
            <span>${message}</span>
        `;
        
        // Add to container
        toastContainer.appendChild(toast);
        
        // Remove after 3 seconds
        setTimeout(() => {
            toast.classList.add('opacity-0', 'translate-y-2');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    /**
     * Add file details to a result card
     * @param {HTMLElement} card - The card element to add details to
     * @param {Object} fileInfo - Information about the file
     */
    static addFileDetails(card, fileInfo) {
        const detailsSection = document.createElement('div');
        detailsSection.className = 'file-details';
        
        // Add file details rows
        if (fileInfo.size) {
            detailsSection.appendChild(ResultPageEnhancer.createDetailRow('Size', ResultPageEnhancer.formatFileSize(fileInfo.size)));
        }
        
        if (fileInfo.format) {
            detailsSection.appendChild(ResultPageEnhancer.createDetailRow('Format', fileInfo.format.toUpperCase()));
        }
        
        if (fileInfo.dimensions) {
            detailsSection.appendChild(ResultPageEnhancer.createDetailRow('Dimensions', fileInfo.dimensions));
        }
        
        if (fileInfo.created) {
            detailsSection.appendChild(ResultPageEnhancer.createDetailRow('Created', new Date(fileInfo.created).toLocaleString()));
        }
        
        card.appendChild(detailsSection);
    }

    /**
     * Create a detail row for file information
     * @param {string} label - Label for the detail
     * @param {string} value - Value for the detail
     * @returns {HTMLElement} - The created detail row element
     */
    static createDetailRow(label, value) {
        const row = document.createElement('div');
        row.className = 'file-details-row';
        
        const labelSpan = document.createElement('span');
        labelSpan.className = 'file-details-label';
        labelSpan.textContent = label;
        
        const valueSpan = document.createElement('span');
        valueSpan.className = 'file-details-value';
        valueSpan.textContent = value;
        
        row.appendChild(labelSpan);
        row.appendChild(valueSpan);
        
        return row;
    }

    /**
     * Format file size in human-readable format
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Export to global scope
window.ResultPageEnhancer = ResultPageEnhancer;
